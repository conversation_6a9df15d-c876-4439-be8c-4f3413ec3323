"""
SSE 常量模块

定义 SSE 响应中使用的常量。
"""

from enum import Enum, auto


# 位置建议相关常量
LOCATION_SUGGESTION_TITLE = "请确认您具体的地点："


class ParentType(str, Enum):
    """位置类型枚举"""
    TypeBizcircle = "商圈"            # 商圈
    TypeSubway = "地铁"            # 商圈
    TypeSubwayLine = "地铁线"          # 地铁线
    TypeSubwayStation = "地铁站"    # 地铁站
    TypeResblock = "小区"              # 小区
    TypeDistrict = "城区"              # 城区
    TypePoiCompany = "公司"          # 公司
    TypePoiOffices = "办公楼"          # 办公楼
    TypePoiOfficeTowers = "写字楼"          # 办公楼
    TypePoiTechPark = "科技园区"        # 科技园区
    TypePoiSchool = "学校"            # 学校
    TypePoiOrg = "组织"                  # 组织
    TypePoiHos = "医院"                  # 医院
    TypePoiShopping = "购物中心"        # 购物中心
    TypePoiTourist = "景区"          # 景区
    TypePoiTraffic = "交通地点"          # 交通地点
    TypePoi = "地点"          # 交通地点
    TypePoiRoad = "道路"                # 道路
    TypeZra = "自如寓"                        # 自如寓
    TypeBorough = "行政区"                        # 行政区
    TypeRequirementCommunication = "requirement_communication"  # 需求沟通
    TypeLocationSuggestion = "location_suggestion"  # 位置建议
    TypeViewHouse = "view_house"  # 看房单

class SubType(str, Enum):
    """子类型枚举，表示数据格式"""
    TypeText = "text"    # 文本内容，一个字符一个字符地返回

    # 以下是各种列表类型，对应不同的父类型
    TypeBizcircleList = "bizcircle_list"        # 商圈列表
    TypeSubwayList = "subway_list"        # 商圈列表
    TypeSubwayLineList = "subway_line_list"     # 地铁线列表
    TypeSubwayStationList = "subway_station_list"  # 地铁站列表
    TypeResblockList = "resblock_list"          # 小区列表
    TypeDistrictList = "district_list"          # 城区列表
    TypePoiCompanyList = "poi_company_list"     # 公司列表
    TypePoiList = "poi_list"     # 公司列表
    TypePoiOfficesList = "poi_offices_list"     # 办公楼列表
    TypePoiOfficesTowerList = "poi_offices_tower_list"     # 办公楼列表
    TypePoiTechParkList = "poi_tech_park_list"  # 科技园区列表
    TypePoiSchoolList = "poi_school_list"       # 学校列表
    TypePoiOrgList = "poi_org_list"             # 组织列表
    TypePoiHosList = "poi_hos_list"             # 医院列表
    TypePoiShoppingList = "poi_shopping_list"   # 购物中心列表
    TypePoiTouristList = "poi_tourist_list"     # 景区列表
    TypePoiTrafficList = "poi_traffic_list"     # 交通地点列表
    TypePoiRoadList = "poi_road_list"           # 道路列表
    TypeZraList = "zra_list"                    # 自如寓列表
    TypeBoroughList = "borough_list"            # 行政区
    TypeRequirementCommunicationList = "requirement_communication_list"  # 需求沟通列表
    TypeLocationSuggestionList = "location_suggestion_list"  # 位置建议列表
    TypeViewHouseImg = "view_house_img"  # 看房单图片
    TypeViewHouseInfo = "view_house_info"  # 看房单信息
    TypeViewHouseList = "view_house_list"  # 看房单列表
    TypeViewHouseKeeper = "view_house_keeper"  # 看房单管家信息
    TypeViewHouseMore = "view_house_more"  # 看房单更多信息


class MappingRegistry:
    """映射关系注册中心，统一管理所有的映射逻辑"""
    
    # ParentType 到 SubType 的映射
    PARENT_TO_SUB_MAPPING = {
        ParentType.TypeBizcircle: SubType.TypeBizcircleList,
        ParentType.TypeSubway: SubType.TypeSubwayList,
        ParentType.TypeSubwayLine: SubType.TypeSubwayLineList,
        ParentType.TypeSubwayStation: SubType.TypeSubwayStationList,
        ParentType.TypeResblock: SubType.TypeResblockList,
        ParentType.TypePoi: SubType.TypePoiList,
        ParentType.TypeDistrict: SubType.TypeDistrictList,
        ParentType.TypePoiCompany: SubType.TypePoiCompanyList,
        ParentType.TypePoiOffices: SubType.TypePoiOfficesList,
        ParentType.TypePoiOfficeTowers: SubType.TypePoiOfficesTowerList,
        ParentType.TypePoiTechPark: SubType.TypePoiTechParkList,
        ParentType.TypePoiSchool: SubType.TypePoiSchoolList,
        ParentType.TypePoiOrg: SubType.TypePoiOrgList,
        ParentType.TypePoiHos: SubType.TypePoiHosList,
        ParentType.TypePoiShopping: SubType.TypePoiShoppingList,
        ParentType.TypePoiTourist: SubType.TypePoiTouristList,
        ParentType.TypePoiTraffic: SubType.TypePoiTrafficList,
        ParentType.TypePoiRoad: SubType.TypePoiRoadList,
        ParentType.TypeZra: SubType.TypeZraList,
        ParentType.TypeBorough: SubType.TypeBoroughList,
        ParentType.TypeRequirementCommunication: SubType.TypeRequirementCommunicationList,
        ParentType.TypeLocationSuggestion: SubType.TypeLocationSuggestionList,
        ParentType.TypeViewHouse: SubType.TypeViewHouseList
    }
    
    # 位置类型（中文）到 ParentType 的映射
    LOCATION_TYPE_TO_PARENT_MAPPING = {
        "商圈": ParentType.TypeBizcircle,
        "地铁": ParentType.TypeSubway,  # 添加"地铁"映射到地铁线
        "地铁线": ParentType.TypeSubwayLine,
        "地铁站": ParentType.TypeSubwayStation,
        "地点": ParentType.TypePoi,
        "小区": ParentType.TypeResblock,
        "城区": ParentType.TypeDistrict,
        "公司": ParentType.TypePoiCompany,
        "办公楼": ParentType.TypePoiOffices,
        "写字楼": ParentType.TypePoiOfficeTowers,
        "科技园区": ParentType.TypePoiTechPark,
        "学校": ParentType.TypePoiSchool,
        "组织": ParentType.TypePoiOrg,
        "医院": ParentType.TypePoiHos,
        "购物中心": ParentType.TypePoiShopping,
        "景区": ParentType.TypePoiTourist,
        "交通地点": ParentType.TypePoiTraffic,
        "道路": ParentType.TypePoiRoad,
        "自如寓": ParentType.TypeZra,
        "行政区": ParentType.TypeBorough
    }
    
    # ParentType（中文）到英文的映射
    PARENT_TYPE_TO_ENGLISH_MAPPING = {
        "商圈": "bizcircle",
        "地铁": "subway",  # 添加"地铁"映射到地铁线
        "地铁线": "subway_line",
        "地铁站": "subway_station",
        "小区": "resblock",
        "城区": "district",
        "地点": "poi",
        "公司": "poi_company",
        "办公楼": "poi_offices",
        "写字楼": "poi_offices_tower",
        "科技园区": "poi_tech_park",
        "学校": "poi_school",
        "组织": "poi_org",
        "医院": "poi_hos",
        "购物中心": "poi_shopping",
        "景区": "poi_tourist",
        "交通地点": "poi_traffic",
        "道路": "poi_road",
        "自如寓": "zra",
        "行政区": "borough",
        "location_suggestion": "location_suggestion",  # 位置建议类型
        "view_house": "view_house"  # 看房单类型
    }
    
    @classmethod
    def get_sub_type_for_parent_type(cls, parent_type: str) -> str:
        """
        根据父类型获取对应的子类型

        Args:
            parent_type: 父类型

        Returns:
            对应的子类型
        """
        return cls.PARENT_TO_SUB_MAPPING.get(parent_type, SubType.TypeRequirementCommunicationList)
    
    @classmethod
    def get_parent_type_by_location_type(cls, location_type: str) -> str:
        """
        根据位置类型获取父类型

        Args:
            location_type: 位置类型

        Returns:
            对应的父类型
        """
        return cls.LOCATION_TYPE_TO_PARENT_MAPPING.get(location_type, ParentType.TypeRequirementCommunication)
    
    @classmethod
    def get_parent_type_english(cls, parent_type: str) -> str:
        """
        获取parent_type的英文值，与sub_type前半段保持一致

        Args:
            parent_type: 父类型

        Returns:
            父类型的英文值
        """
        # 如果是需求沟通类型或者已经是英文，直接返回
        if parent_type == "requirement_communication" or parent_type in cls.PARENT_TYPE_TO_ENGLISH_MAPPING.values():
            return parent_type

        # 返回对应的英文值，如果没有匹配则返回原值
        return cls.PARENT_TYPE_TO_ENGLISH_MAPPING.get(parent_type, parent_type)
