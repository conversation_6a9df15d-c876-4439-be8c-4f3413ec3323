"""
SSE 数据模型模块

定义 SSE 响应的数据模型。
"""

import json
from typing import Any
import uuid

from app.sse.constants import SubType, MappingRegistry


class SSEEvent:
    """SSE 事件模型"""

    def __init__(self, parent_type: str, sub_type: str, content: Any, id: str = ""):
        """
        初始化 SSE 事件

        Args:
            parent_type: 父类型
            sub_type: 子类型
            content: 内容
            id: 事件ID
        """
        self.parent_type = parent_type
        self.sub_type = sub_type
        self.content = content
        self.id = id or str(uuid.uuid4())

    def to_sse_format(self) -> str:
        """
        转换为 SSE 格式

        Returns:
            SSE 格式的字符串
        """
        # 获取parent_type的英文值，与sub_type前半段保持一致
        parent_type_english = self._get_parent_type_english(self.parent_type)

        if self.sub_type == SubType.TypeText:
            # 文本类型，直接返回文本内容
            data = {
                "id": self.id,
                "parent_type": parent_type_english,
                "sub_type": self.sub_type,
                "content": self.content,
            }
        else:
            # JSON类型，使用新的格式
            data = {
                "id": self.id,
                "parent_type": parent_type_english,
                "sub_type": self.sub_type,
                "json": {
                    self.sub_type: self.content
                },
            }

        return f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

    def _get_parent_type_english(self, parent_type: str) -> str:
        """
        获取parent_type的英文值，与sub_type前半段保持一致

        Args:
            parent_type: 父类型

        Returns:
            父类型的英文值
        """
        return MappingRegistry.get_parent_type_english(parent_type)


class SSEDoneEvent:
    """SSE 结束事件"""

    def to_sse_format(self) -> str:
        """
        转换为 SSE 格式的字符串

        Returns:
            SSE 格式的字符串
        """
        return "data: [DONE]\n\n"
