"""
SSE 格式化器模块

提供将不同类型的数据格式化为 SSE 事件的功能。
"""

import logging

from app.sse.constants import MappingRegistry

logger = logging.getLogger(__name__)


class BaseFormatter:
    """基础格式化器"""

    @staticmethod
    def get_sub_type_for_parent_type(parent_type: str) -> str:
        """
        根据父类型获取对应的子类型

        Args:
            parent_type: 父类型

        Returns:
            对应的子类型
        """
        return MappingRegistry.get_sub_type_for_parent_type(parent_type)











