"""
SSE 生成器模块

提供生成 SSE 事件流的功能。
"""

import asyncio
import json
from typing import Dict, Any, AsyncGenerator

from app.sse.constants import SubType, ParentType
from app.sse.models import SSEEvent, SSEDoneEvent
from app.sse.formatters import BaseFormatter


class SSEGenerator:
    """SSE 事件生成器"""

    @staticmethod
    def _get_parent_type_english(parent_type: str) -> str:
        """
        获取parent_type的英文值，与sub_type前半段保持一致

        Args:
            parent_type: 父类型

        Returns:
            父类型的英文值
        """
        # 映射中文parent_type到英文
        parent_type_mapping = {
            "商圈": "bizcircle",
            "地铁": "subway",  # 添加"地铁"映射到地铁线
            "地铁线": "subway_line",
            "地铁站": "subway_station",
            "地点": "poi",
            "小区": "resblock",
            "城区": "district",
            "公司": "poi_company",
            "写字楼": "poi_offices_tower",
            "办公楼": "poi_offices",
            "科技园区": "poi_tech_park",
            "学校": "poi_school",
            "组织": "poi_org",
            "医院": "poi_hos",
            "购物中心": "poi_shopping",
            "景区": "poi_tourist",
            "交通地点": "poi_traffic",
            "道路": "poi_road",
            "自如寓": "zra",
            "行政区": "borough",
            "location_suggestion": "location_suggestion",  # 位置建议类型
            "view_house": "view_house"  # 看房单类型
        }

        # 如果是需求沟通类型或者已经是英文，直接返回
        if parent_type == "requirement_communication" or parent_type in parent_type_mapping.values():
            return parent_type

        # 返回对应的英文值，如果没有匹配则返回原值
        return parent_type_mapping.get(parent_type, parent_type)

    @staticmethod
    async def generate_text_stream(
        text: str,
        parent_type: str,
        delay: float = 0.05,
        session_id: str = ""
    ) -> AsyncGenerator[str, None]:
        """
        生成文本流

        Args:
            text: 文本内容
            parent_type: 父类型
            delay: 每个字符之间的延迟（秒）
            session_id: 会话ID，用于标识事件

        Returns:
            SSE 事件流
        """
        # 获取parent_type的英文值，与sub_type前半段保持一致
        parent_type_english = SSEGenerator._get_parent_type_english(parent_type)

        # 处理空文本的情况
        if not text:
            # 发送结束事件
            yield SSEDoneEvent().to_sse_format()
            return

        # 处理只有一个字符的情况
        if len(text) == 1:
            # 直接发送带有finish标记的字符
            data = {
                "id": session_id,
                "parent_type": parent_type_english,
                "sub_type": SubType.TypeText,
                "content": text[0],
                "finish": True
            }
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
            yield SSEDoneEvent().to_sse_format()
            return

        # 处理第一个字符（无延迟）
        first_char = text[0]
        event = SSEEvent(
            parent_type=parent_type_english,
            sub_type=SubType.TypeText,
            content=first_char,
            id=session_id
        )
        yield event.to_sse_format()

        # 处理中间字符（有延迟）
        for char in text[1:-1]:
            # 先添加延迟，再发送字符
            if delay > 0:
                await asyncio.sleep(delay)

            event = SSEEvent(
                parent_type=parent_type_english,
                sub_type=SubType.TypeText,
                content=char,
                id=session_id
            )
            yield event.to_sse_format()

        # 处理最后一个字符（带finish标记）
        if delay > 0:
            await asyncio.sleep(delay)
        
        last_char = text[-1]
        data = {
            "id": session_id,
            "parent_type": parent_type_english,
            "sub_type": SubType.TypeText,
            "content": last_char,
            "finish": True
        }
        yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

        # 发送结束事件
        yield SSEDoneEvent().to_sse_format()

    @staticmethod
    async def generate_json_stream(
        data: Dict[str, Any],
        parent_type: str,
        session_id: str = "",
        delay: float = 0
    ) -> AsyncGenerator[str, None]:
        """
        生成 JSON 流

        Args:
            data: JSON 数据
            parent_type: 父类型
            session_id: 会话ID，用于标识事件
            delay: 每个字符之间的延迟（秒），用于文本类型的打字机效果

        Returns:
            SSE 事件流
        """
        # 获取对应的子类型
        sub_type = BaseFormatter.get_sub_type_for_parent_type(parent_type)

        # 获取parent_type的英文值，与sub_type前半段保持一致
        parent_type_english = SSEGenerator._get_parent_type_english(parent_type)

        # 创建事件
        event = SSEEvent(
            parent_type=parent_type_english,
            sub_type=sub_type,
            content=data,
            id=session_id
        )
        yield event.to_sse_format()

        # 发送结束事件
        yield SSEDoneEvent().to_sse_format()

    @staticmethod
    async def generate_json_array_stream(
        data: Dict[str, Any],
        parent_type: str,
        session_id: str = "",
        delay: float = 0.1
    ) -> AsyncGenerator[str, None]:
        """
        生成 JSON 数组流，每个数组元素单独发送

        Args:
            data: 包含数组的 JSON 数据，格式为 {"title": "标题", "data": [item1, item2, ...], "summary": "摘要"}
            parent_type: 父类型
            session_id: 会话ID，用于标识事件
            delay: 每个字符之间的延迟（秒），用于文本类型的打字机效果

        Returns:
            SSE 事件流
        """
        # 获取对应的子类型
        sub_type = BaseFormatter.get_sub_type_for_parent_type(parent_type)

        # 检查数据格式
        if not isinstance(data, dict) or "data" not in data or not isinstance(data["data"], list):
            # 如果不是预期的格式，使用普通的 JSON 流
            async for event in SSEGenerator.generate_json_stream(data, parent_type, session_id, delay):
                yield event
            return

        # 获取标题和数组数据
        title = data.get("title", "看房单")
        array_data = data["data"]

        # 获取parent_type的英文值，与sub_type前半段保持一致
        parent_type_english = SSEGenerator._get_parent_type_english(parent_type)

        # 1. 先发送标题（作为text类型逐字符返回，添加打字机效果）
        # 处理空标题的情况
        if not title:
            pass  # 如果标题为空，直接跳过
        else:
            # 处理只有一个字符的标题
            if len(title) == 1:
                text_response = {
                    "id": session_id,
                    "parent_type": parent_type_english,
                    "sub_type": SubType.TypeText,
                    "content": title[0],
                    "finish": True
                }
                yield f"data: {json.dumps(text_response, ensure_ascii=False)}\n\n"
            else:
                # 处理第一个字符（无延迟）
                first_char = title[0]
                text_response = {
                    "id": session_id,
                    "parent_type": parent_type_english,
                    "sub_type": SubType.TypeText,
                    "content": first_char
                }
                yield f"data: {json.dumps(text_response, ensure_ascii=False)}\n\n"

                # 处理中间字符（有延迟）
                for char in title[1:-1]:
                    # 先添加延迟，再发送字符
                    if delay > 0:
                        await asyncio.sleep(delay)

                    text_response = {
                        "id": session_id,
                        "parent_type": parent_type_english,
                        "sub_type": SubType.TypeText,
                        "content": char
                    }
                    yield f"data: {json.dumps(text_response, ensure_ascii=False)}\n\n"

                # 处理标题的最后一个字符（带finish标记）
                if delay > 0:
                    await asyncio.sleep(delay)
                
                last_char = title[-1]
                text_response = {
                    "id": session_id,
                    "parent_type": parent_type_english,
                    "sub_type": SubType.TypeText,
                    "content": last_char,
                    "finish": True
                }
                yield f"data: {json.dumps(text_response, ensure_ascii=False)}\n\n"

        # 2. 逐个发送房源数据（作为json类型返回）
        for i, item in enumerate(array_data):
            # 判断是否为最后一个元素
            is_last = (i == len(array_data) - 1)
            
            json_response = {
                "id": session_id,
                "parent_type": parent_type_english,
                "sub_type": sub_type,
                "json": {
                    sub_type: item
                }
            }
            
            # 如果是最后一个元素，添加finish标记
            if is_last:
                json_response["finish"] = True
            
            yield f"data: {json.dumps(json_response, ensure_ascii=False)}\n\n"

        # 发送结束事件
        yield SSEDoneEvent().to_sse_format()

    @staticmethod
    async def generate_viewing_appointment_stream(
        viewing_data: Dict[str, Any],
        session_id: str = ""
    ) -> AsyncGenerator[str, None]:
        """
        生成看房单预约的多数据块SSE流

        Args:
            viewing_data: 看房单数据，格式为：
                {
                    "parent_type": "view_house",
                    "data": [
                        {"sub_type": "view_house_img", "content": {...}},
                        {"sub_type": "view_house_list", "content": [...]},
                        {"sub_type": "view_house_keeper", "content": {...}},
                        {"sub_type": "view_house_more", "content": " "}
                    ]
                }
            session_id: 会话ID，用于标识事件

        Returns:
            SSE 事件流
        """
        # 获取parent_type的英文值
        parent_type = viewing_data.get("parent_type", "view_house")
        parent_type_english = SSEGenerator._get_parent_type_english(parent_type)
        
        data_blocks = viewing_data.get("data", [])
        
        # 逐个处理数据块
        for i, block in enumerate(data_blocks):
            sub_type = block.get("sub_type", "")
            content = block.get("content", "")
            
            if sub_type == "view_house_list" and isinstance(content, list):
                # 对于view_house_list，参考generate_json_array_stream的实现方式
                for j, item in enumerate(content):
                    # 判断是否为最后一个元素
                    is_last = (j == len(content) - 1)
                    
                    json_response = {
                        "id": session_id,
                        "parent_type": parent_type_english,
                        "sub_type": sub_type,
                        "json": {
                            sub_type: item
                        }
                    }
                    
                    # 如果是最后一个元素，添加finish标记
                    if is_last:
                        json_response["finish"] = True
                    
                    yield f"data: {json.dumps(json_response, ensure_ascii=False)}\n\n"
                    
            elif sub_type == "view_house_more":
                # 对于view_house_more类型，保持原本的sub_type
                is_last = (i == len(data_blocks) - 1)
                response_data = {
                    "id": session_id,
                    "parent_type": parent_type_english,
                    "sub_type": sub_type,
                    "content": content
                }
                if is_last:
                    response_data["finish"] = True
                    
                yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
                
            else:
                # 其他类型（view_house_img, view_house_keeper）使用json格式，每种sub_type都带finish标记
                response_data = {
                    "id": session_id,
                    "parent_type": parent_type_english,
                    "sub_type": sub_type,
                    "json": {
                        sub_type: content
                    },
                    "finish": True
                }
                
                yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"

        # 发送结束事件
        yield SSEDoneEvent().to_sse_format()