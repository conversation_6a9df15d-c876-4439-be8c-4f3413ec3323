"""
提示配置模块

提供统一的提示文本和配置，避免重复定义。
"""

from enum import Enum
from typing import Dict, Any

from app.services.context_service import SessionState


class PromptType(str, Enum):
    """提示类型枚举"""
    ASK_LOCATION = "ask_location"
    ASK_BUDGET = "ask_budget"
    ASK_ROOM_TYPE = "ask_room_type"
    NO_HOUSES = "no_houses"
    SHOW_HOUSES = "v4"  # 使用v4版本的提示模板展示房源
    STANDARD = "standard"
    DIRECT_HOUSES = "v3"  # 直接展示房源的提示模板


# 参数询问配置
PARAMETER_CONFIGS = {
    "location": {
        "prompt_version": PromptType.ASK_LOCATION,
        "question": "您想在哪个区域或地点找房子？",
        "context_key": "location_asked",
        "session_state": SessionState.ASKING_LOCATION
    },
    "budget": {
        "prompt_version": PromptType.ASK_BUDGET,
        "question": "您的预算是多少？",
        "context_key": "budget_asked",
        "session_state": SessionState.ASKING_BUDGET
    },
    "room_type": {
        "prompt_version": PromptType.ASK_ROOM_TYPE,
        "question": "您需要整租还是合租？",
        "context_key": "room_type_asked",
        "session_state": SessionState.ASKING_ROOM_TYPE
    }
}


# 响应文本配置
RESPONSE_TEXTS = {
    PromptType.ASK_LOCATION: "您想在哪个区域或地点找房子？",
    PromptType.ASK_BUDGET: "您的预算是多少？",
    PromptType.ASK_ROOM_TYPE: "您需要整租还是合租？",
    PromptType.NO_HOUSES: "抱歉，没有找到符合您条件的房源。您可以尝试调整搜索条件，例如提高预算或扩大搜索范围。"
}


def get_parameter_config(param_type: str) -> Dict[str, Any]:
    """
    获取参数配置

    Args:
        param_type: 参数类型，如 "location"、"budget" 或 "room_type"

    Returns:
        参数配置字典
    """
    return PARAMETER_CONFIGS.get(param_type, {})


def get_response_text(prompt_type: str) -> str:
    """
    获取响应文本

    Args:
        prompt_type: 提示类型

    Returns:
        响应文本
    """
    return RESPONSE_TEXTS.get(prompt_type, "")
