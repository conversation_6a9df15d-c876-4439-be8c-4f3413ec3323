"""
中间件模块

提供应用级别的中间件，包括数据库连接管理、请求监控等。
"""

import time
import logging
import asyncio
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

class DatabaseConnectionMiddleware(BaseHTTPMiddleware):
    """数据库连接监控中间件 - 简化版，依赖自动恢复机制"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并监控数据库连接"""
        start_time = time.time()
        
        # 记录请求开始
        logger.info(f"请求开始: {request.method} {request.url.path}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求完成
            logger.info(f"请求完成: {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
            
            # 添加响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 记录异常
            process_time = time.time() - start_time
            logger.error(f"请求异常: {request.method} {request.url.path} - {str(e)} - {process_time:.3f}s")
            raise

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """记录请求详细信息"""
        start_time = time.time()
        
        # 记录请求详情
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        logger.info(f"请求详情: {client_ip} - {request.method} {request.url} - {user_agent}")
        
        try:
            response = await call_next(request)
            
            process_time = time.time() - start_time
            
            # 记录响应详情
            logger.info(f"响应详情: {response.status_code} - {process_time:.3f}s")
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"请求处理异常: {str(e)} - {process_time:.3f}s")
            raise 