import logging
import sys
from typing import List

from app.core.config import settings

# 日志格式
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 日志级别
LOG_LEVEL = logging.DEBUG if settings.DEBUG else logging.INFO

def setup_logging():
    """设置日志配置"""
    # 创建日志处理器
    handlers: List[logging.Handler] = [
        logging.StreamHandler(sys.stdout)
    ]

    # 配置日志
    logging.basicConfig(
        level=LOG_LEVEL,
        format=LOG_FORMAT,
        handlers=handlers
    )

    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)
    logging.getLogger("aiosqlite").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)

    # 返回根日志记录器
    return logging.getLogger()
