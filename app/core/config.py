import os
from typing import Any, Dict, Optional

from pydantic import field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# 加载.env文件
# 优先读取/app/conf/.env，不存在则读取默认.env
if os.path.exists('/app/conf/.env'):
    load_dotenv('/app/conf/.env')
else:
    load_dotenv()

class Settings(BaseSettings):
    """应用配置类"""

    # 应用配置
    APP_NAME: str = "AI House Search"
    DEBUG: bool = False

    # OpenAI配置
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    OPENAI_API_BASE: str = "https://api.openai.com/v1"
    OPENAI_API_VERSION: str = "2023-05-15"

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./ai_house_search.db"

    # 自如找房API配置
    HOUSE_API_BASE_URL: str = "http://ai-finder-bff.kq.ziroom.com"
    HOUSE_MARS_API_BASE_URL: str = "http://mars.kp.ziroom.com"

    # 房源搜索API
    HOUSE_SEARCH_API: str = "/rooms/"
    HOUSE_LOCATION_SEARCH_API: str = "/searchSug"
    HOUSE_BIZCIRCLES_API: str = "/bizcircles/"
    HOUSE_SUBWAY_API: str = "/subway/subwayStations"
    HOUSE_RESBLOCKS_API: str = "/resblocks"
    HOUSE_RESBLOCKS_DETAIL_API: str = "/mars/resblock/detail/id"
    HOUSE_LOCATION_SUG_API: str = "/search/sug"
    HOUSE_RESERVE_API: str = "/createReservation"

    # 默认城市编码
    DEFAULT_CITY_CODE: str = "110000"

    # 提示模板配置
    INTENT_PROMPT_VERSION: str = "v1"
    EXTRACTION_PROMPT_VERSION: str = "dynamic"  # 使用动态提取提示模板
    RESPONSE_PROMPT_VERSION: str = "v4"  # 使用更智能的响应提示模板
    ANALYSIS_PROMPT_VERSION: str = "v1"

    # 验证器
    @field_validator("DATABASE_URL", mode="before")
    def validate_database_url(cls, v: Optional[str]) -> Any:
        """验证数据库URL"""
        if not v:
            return "sqlite:///./ai_house_search.db"
        return v

    model_config = {
        "env_file": ".env" if not os.path.exists('/app/conf/.env') else '/app/conf/.env',
        "case_sensitive": True
    }

# 创建全局设置对象
settings = Settings()
