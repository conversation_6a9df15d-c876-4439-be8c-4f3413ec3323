"""
依赖注入模块

提供 FastAPI 依赖注入函数，用于管理服务实例和其他依赖。
"""

import logging
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.base import get_async_db
from app.services.chat_service import ChatService
from app.services.session_service import SessionService
from app.services.context_service import ContextService
from app.services.house_service import HouseService
from app.workflows.factory import WorkflowFactory

logger = logging.getLogger(__name__)

# 数据库会话依赖
async def get_db():
    """获取数据库会话依赖"""
    session = None
    try:
        async for session in get_async_db():
            yield session
            break  # 只获取一次会话
    except Exception as e:
        logger.error(f"数据库会话使用过程中出错: {str(e)}")
        # 在依赖注入层面不需要额外处理，get_async_db已经处理了清理
        raise

# 会话服务依赖 - 优化生命周期管理
async def get_session_service(db: AsyncSession = Depends(get_db)):
    """获取会话服务实例"""
    try:
        service = SessionService(db)
        yield service
    except Exception as e:
        logger.error(f"会话服务使用过程中出错: {str(e)}")
        raise
    finally:
        # 服务清理逻辑（如果需要）
        pass

# 上下文服务依赖 - 单例模式
_context_service_instance = None

def get_context_service():
    """获取上下文服务实例（单例）"""
    global _context_service_instance
    if _context_service_instance is None:
        _context_service_instance = ContextService()
    return _context_service_instance

# 房源服务依赖 - 单例模式
_house_service_instance = None

def get_house_service():
    """获取房源服务实例（单例）"""
    global _house_service_instance
    if _house_service_instance is None:
        _house_service_instance = HouseService()
    return _house_service_instance

# 工作流工厂依赖 - 单例模式
_workflow_factory_instance = None

def get_workflow_factory():
    """获取工作流工厂实例（单例）"""
    global _workflow_factory_instance
    if _workflow_factory_instance is None:
        _workflow_factory_instance = WorkflowFactory()
    return _workflow_factory_instance

# 聊天服务依赖 - 优化生命周期管理
async def get_chat_service(
    db: AsyncSession = Depends(get_db),
    session_service: SessionService = Depends(get_session_service),
    workflow_factory: WorkflowFactory = Depends(get_workflow_factory)
):
    """获取聊天服务实例"""
    try:
        service = ChatService(db, session_service, workflow_factory)
        yield service
    except Exception as e:
        logger.error(f"聊天服务使用过程中出错: {str(e)}")
        raise
    finally:
        # 服务清理逻辑（如果需要）
        pass
