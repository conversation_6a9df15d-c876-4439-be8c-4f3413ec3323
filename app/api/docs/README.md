# API 文档

本项目使用 FastAPI 的自动文档生成功能，提供了交互式的 API 文档。

## 访问文档

API 文档可以通过以下 URL 访问：

- Swagger UI: `/api/docs`
- ReDoc: `/api/redoc`
- OpenAPI JSON: `/api/openapi.json`

## 更新文档

FastAPI 会根据代码中的类型注解和文档字符串自动生成文档。要增强文档，可以：

1. 为 API 路由函数添加详细的文档字符串
2. 使用 Pydantic 模型定义请求和响应
3. 为路由添加示例和额外的元数据

## 文档结构

API 文档按标签组织：

- `chat`: 聊天交互接口
- `session`: 会话管理接口
- `system`: 系统信息接口

## 认证

目前，API 不需要认证。这在未来的版本中可能会改变。

## 最佳实践

1. 为所有 API 路由添加详细的文档字符串
2. 使用 Pydantic 模型定义请求和响应
3. 为模型字段添加描述和示例
4. 为路由添加标签，以便在文档中分组
5. 使用 `response_model` 参数指定响应模型

## 示例

### 创建会话

```bash
curl -X POST "http://localhost:8000/api/session/create" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123"}'
```

### 发送消息

```bash
curl -X POST "http://localhost:8000/api/chat/send" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your_session_id", "message": "我想在西二旗附近找房子"}'
```

### 获取会话信息

```bash
curl -X GET "http://localhost:8000/api/session/your_session_id"
```

### 获取对话历史

```bash
curl -X GET "http://localhost:8000/api/session/your_session_id/conversation"
```

## 参考资料

- [FastAPI 文档](https://fastapi.tiangolo.com/tutorial/metadata/)
- [Pydantic 文档](https://pydantic-docs.helpmanual.io/usage/schema/)
