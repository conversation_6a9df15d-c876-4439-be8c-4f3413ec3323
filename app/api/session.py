from typing import Dict, Any, Optional, List, Generic, TypeVar

import json
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from pydantic import BaseModel, Field
from app.services.session_service import SessionService
from app.core.dependencies import get_session_service

# 创建路由器
router = APIRouter(prefix="/api/session", tags=["session"])

# 泛型类型变量
T = TypeVar('T')

# 标准API响应格式
class ApiResponse(BaseModel, Generic[T]):
    """标准API响应格式"""
    code: int = Field(200, description="响应状态码")
    message: str = Field("success", description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

    class Config:
        json_schema_extra = {
            "example": {
                "code": 200,
                "message": "success",
                "data": {}
            }
        }

# 响应模型
class WelcomeConfig(BaseModel):
    """欢迎配置模型"""
    img: str = Field("https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg", description="欢迎图片")

class SessionData(BaseModel):
    """会话数据模型"""
    session_id: str = Field(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000")
    user_id: Optional[str] = Field(None, description="用户ID", example="user123")
    created_at: str = Field(..., description="创建时间", example="2023-01-01T12:00:00Z")
    updated_at: Optional[str] = Field(None, description="更新时间", example="2023-01-01T12:30:00Z")
    context: Optional[Dict[str, Any]] = Field(None, description="会话上下文")
    welcome: Optional[WelcomeConfig] = Field(None, description="欢迎配置")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "user_id": "user123",
                "created_at": "2023-01-01T12:00:00Z",
                "updated_at": "2023-01-01T12:30:00Z",
                "context": {
                    "location": "西二旗",
                    "location_type": "商圈",
                    "state": "filtering_in_progress"
                },
                "welcome": {
                    "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
                }
            }
        }

# 为了向后兼容，保留原有的SessionResponse
SessionResponse = ApiResponse[SessionData]

class MessageData(BaseModel):
    """消息数据模型"""
    role: str = Field(..., description="消息角色（user或assistant）", example="user")
    content: str = Field(..., description="消息内容", example="我想在西二旗附近找房子")
    timestamp: str = Field(..., description="消息时间戳", example="2023-01-01T12:00:00Z")

    class Config:
        json_schema_extra = {
            "example": {
                "role": "user",
                "content": "我想在西二旗附近找房子",
                "timestamp": "2023-01-01T12:00:00Z"
            }
        }

class ConversationData(BaseModel):
    """对话数据模型"""
    session_id: str = Field(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000")
    messages: List[MessageData] = Field(..., description="消息列表")
    context: Dict[str, Any] = Field(..., description="会话上下文")
    welcome: Optional[WelcomeConfig] = Field(None, description="欢迎配置")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000",
                "messages": [
                    {
                        "role": "user",
                        "content": "我想在西二旗附近找房子",
                        "timestamp": "2023-01-01T12:00:00Z"
                    },
                    {
                        "role": "assistant",
                        "content": "好的，我会帮您在西二旗附近找房子。您有什么特殊的要求吗？比如预算、户型等。",
                        "timestamp": "2023-01-01T12:00:10Z"
                    }
                ],
                "context": {
                    "location": "西二旗",
                    "location_type": "商圈",
                    "state": "filtering_in_progress"
                },
                "welcome": {
                    "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
                }
            }
        }

ConversationResponse = ApiResponse[ConversationData]

class SessionListData(BaseModel):
    """会话列表数据模型"""
    sessions: List[Dict[str, Any]] = Field(..., description="会话列表")
    total: int = Field(..., description="会话总数", example=42)

    class Config:
        json_schema_extra = {
            "example": {
                "sessions": [
                    {
                        "session_id": "123e4567-e89b-12d3-a456-426614174000",
                        "user_id": "user123",
                        "created_at": "2023-01-01T12:00:00Z",
                        "updated_at": "2023-01-01T12:30:00Z"
                    },
                    {
                        "session_id": "223e4567-e89b-12d3-a456-426614174001",
                        "user_id": "user123",
                        "created_at": "2023-01-02T12:00:00Z",
                        "updated_at": "2023-01-02T12:30:00Z"
                    }
                ],
                "total": 2
            }
        }

SessionListResponse = ApiResponse[SessionListData]

# 请求模型
class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    user_id: Optional[str] = Field(None, description="用户ID（可选）", example="user123")
    welcome: Optional[WelcomeConfig] = Field(None, description="欢迎配置（可选）")

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "user123",
                "welcome": {
                    "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
                }
            }
        }

@router.post("/create", response_model=SessionResponse, summary="创建会话", description="创建一个新的会话")
async def create_session(
    request: CreateSessionRequest,
    session_service: SessionService = Depends(get_session_service)
):
    """
    创建会话API

    创建一个新的会话，用于存储对话上下文和消息历史。可以选择性地关联一个用户ID和欢迎配置。

    - **user_id**: 用户ID（可选），用于关联会话与用户
    - **welcome**: 欢迎配置（可选），包含欢迎界面的相关配置

    **示例请求**:
    ```json
    {
        "user_id": "user123",
        "welcome": {
            "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
        }
    }
    ```

    **示例响应**:
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "session_id": "123e4567-e89b-12d3-a456-426614174000",
            "user_id": "user123",
            "created_at": "2023-01-01T12:00:00Z",
            "updated_at": null,
            "context": {
                "session_state": "idle"
            },
            "welcome": {
                "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
            }
        }
    }
    ```

    **可能的错误**:
    - 500: 服务器内部错误
    """
    # 创建会话
    session = await session_service.create_session(
        user_id=request.user_id
    )

    # 使用请求中的welcome字段或创建默认的欢迎配置
    welcome = request.welcome if request.welcome else WelcomeConfig()

    # 构建会话数据
    session_data = SessionData(
        session_id=session["session_id"],
        user_id=session["user_id"],
        created_at=session["created_at"],
        context=session["context"],
        welcome=welcome
    )

    # 返回标准格式响应
    return ApiResponse[SessionData](
        code=200,
        message="success",
        data=session_data
    )

@router.get("/list", response_model=SessionListResponse, summary="获取会话列表", description="获取会话列表，支持分页和按用户筛选")
async def list_sessions(
    skip: int = Query(0, description="跳过的记录数", ge=0),
    limit: int = Query(20, description="返回的记录数", ge=1, le=100),
    user_id: Optional[str] = Query(None, description="用户ID，如果提供，则只返回该用户的会话"),
    session_service: SessionService = Depends(get_session_service)
):
    """
    获取会话列表API

    获取系统中的会话列表，支持分页和按用户ID筛选。

    - **skip**: 跳过的记录数，用于分页
    - **limit**: 返回的记录数，用于分页
    - **user_id**: 用户ID（可选），如果提供，则只返回该用户的会话

    **示例请求**:
    ```
    GET /api/session/list?skip=0&limit=20&user_id=user123
    ```

    **示例响应**:
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "sessions": [
                {
                    "session_id": "123e4567-e89b-12d3-a456-426614174000",
                    "user_id": "user123",
                    "created_at": "2023-01-01T12:00:00Z",
                    "updated_at": "2023-01-01T12:30:00Z"
                },
                {
                    "session_id": "223e4567-e89b-12d3-a456-426614174001",
                    "user_id": "user123",
                    "created_at": "2023-01-02T12:00:00Z",
                    "updated_at": "2023-01-02T12:30:00Z"
                }
            ],
            "total": 2
        }
    }
    ```

    **可能的错误**:
    - 500: 服务器内部错误
    """
    # 获取会话列表
    sessions, total = await session_service.get_sessions(skip=skip, limit=limit, user_id=user_id)

    # 构建会话列表数据
    session_list_data = SessionListData(
        sessions=sessions,
        total=total
    )

    # 返回标准格式响应
    return ApiResponse[SessionListData](
        code=200,
        message="success",
        data=session_list_data
    )

# 请求模型
class GetSessionRequest(BaseModel):
    """获取会话请求模型"""
    session_id: str = Field(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000")

    class Config:
        json_schema_extra = {
            "example": {
                "session_id": "123e4567-e89b-12d3-a456-426614174000"
            }
        }

@router.post("/info", response_model=SessionResponse, summary="获取会话信息", description="使用POST方法获取指定会话的详细信息")
async def get_session_info(
    request: GetSessionRequest,
    session_service: SessionService = Depends(get_session_service)
):
    """
    获取会话信息API

    使用POST方法获取指定会话ID的详细信息，包括会话上下文和欢迎配置。
    此接口功能与GET /{session_id}相同，但使用POST方法，适用于某些需要使用POST请求的场景。

    - **session_id**: 会话ID

    **示例请求**:
    ```json
    {
        "session_id": "123e4567-e89b-12d3-a456-426614174000"
    }
    ```

    **示例响应**:
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "session_id": "123e4567-e89b-12d3-a456-426614174000",
            "user_id": "user123",
            "created_at": "2023-01-01T12:00:00Z",
            "updated_at": "2023-01-01T12:30:00Z",
            "context": {
                "location": "西二旗",
                "location_type": "商圈",
                "state": "filtering_in_progress"
            },
            "welcome": {
                "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
            }
        }
    }
    ```

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """
    # 获取会话
    session = await session_service.get_session(request.session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session {request.session_id} not found"
        )

    # 创建欢迎配置
    welcome = WelcomeConfig()

    # 构建会话数据
    session_data = SessionData(
        session_id=session["session_id"],
        user_id=session["user_id"],
        created_at=session["created_at"],
        updated_at=session["updated_at"],
        context=session["context"],
        welcome=welcome
    )

    # 返回标准格式响应
    return ApiResponse[SessionData](
        code=200,
        message="success",
        data=session_data
    )

@router.get("/{session_id}", response_model=SessionResponse, summary="获取会话", description="获取指定会话的详细信息")
async def get_session(
    session_id: str = Path(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000"),
    session_service: SessionService = Depends(get_session_service)
):
    """
    获取会话API

    获取指定会话ID的详细信息，包括会话上下文和欢迎配置。

    - **session_id**: 会话ID

    **示例请求**:
    ```
    GET /api/session/123e4567-e89b-12d3-a456-426614174000
    ```

    **示例响应**:
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "session_id": "123e4567-e89b-12d3-a456-426614174000",
            "user_id": "user123",
            "created_at": "2023-01-01T12:00:00Z",
            "updated_at": "2023-01-01T12:30:00Z",
            "context": {
                "location": "西二旗",
                "location_type": "商圈",
                "state": "filtering_in_progress"
            },
            "welcome": {
                "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
            }
        }
    }
    ```

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """
    # 获取会话
    session = await session_service.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session {session_id} not found"
        )

    # 创建欢迎配置
    welcome = WelcomeConfig()

    # 构建会话数据
    session_data = SessionData(
        session_id=session["session_id"],
        user_id=session["user_id"],
        created_at=session["created_at"],
        updated_at=session["updated_at"],
        context=session["context"],
        welcome=welcome
    )

    # 返回标准格式响应
    return ApiResponse[SessionData](
        code=200,
        message="success",
        data=session_data
    )

@router.get("/{session_id}/conversation", response_model=ConversationResponse, summary="获取对话历史", description="获取指定会话的对话历史")
async def get_conversation(
    session_id: str = Path(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000"),
    session_service: SessionService = Depends(get_session_service)
):
    """
    获取对话API

    获取指定会话的对话历史，包括用户消息、AI回复和欢迎配置。

    - **session_id**: 会话ID

    **示例请求**:
    ```
    GET /api/session/123e4567-e89b-12d3-a456-426614174000/conversation
    ```

    **示例响应**:
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "session_id": "123e4567-e89b-12d3-a456-426614174000",
            "messages": [
                {
                    "role": "user",
                    "content": "我想在西二旗附近找房子",
                    "timestamp": "2023-01-01T12:00:00Z"
                },
                {
                    "role": "assistant",
                    "content": "好的，我会帮您在西二旗附近找房子。您有什么特殊的要求吗？比如预算、户型等。",
                    "timestamp": "2023-01-01T12:00:10Z"
                }
            ],
            "context": {
                "location": "西二旗",
                "location_type": "商圈",
                "state": "filtering_in_progress"
            },
            "welcome": {
                "img": "https://rent-cdn.ziroom.com/007qm/a181cec366074918b12a71814a720e2f.jpg"
                "title": "为了更快找到理想小家，您可以尝试告诉我：",
            }
        }
    }
    ```

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """
    # 检查会话是否存在
    session = await session_service.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session {session_id} not found"
        )

    # 获取对话历史
    history = await session_service.get_conversation_history(session_id, limit=50)

    # 转换消息格式
    messages = []
    for msg in history:
        messages.append(MessageData(
            role=msg["role"],
            content=msg["content"],
            timestamp=""  # 这里需要从数据库获取时间戳
        ))

    # 创建欢迎配置
    welcome = WelcomeConfig()

    # 构建对话数据
    conversation_data = ConversationData(
        session_id=session_id,
        messages=messages,
        context=session["context"],
        welcome=welcome
    )

    # 返回标准格式响应
    return ApiResponse[ConversationData](
        code=200,
        message="success",
        data=conversation_data
    )

@router.get("/{session_id}/last_response", summary="获取最后一次API响应", description="获取指定会话的最后一次API响应")
async def get_last_response(
    session_id: str = Path(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000"),
    session_service: SessionService = Depends(get_session_service)
):
    """
    获取最后一次API响应

    获取指定会话的最后一次API响应，通常用于获取最近的搜索结果。

    - **session_id**: 会话ID

    **示例请求**:
    ```
    GET /api/session/123e4567-e89b-12d3-a456-426614174000/last_response
    ```

    **示例响应**:
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "rooms": [
                {
                    "id": "61001234",
                    "title": "西二旗 2室1厅 南北通透",
                    "price": "4500",
                    "area": "70",
                    "location": "西二旗地铁站附近",
                    "room_type": "2室1厅",
                    "photo": "https://example.com/photo.jpg"
                }
            ],
            "summary": "为您找到1套符合条件的房源"
        }
    }
    ```

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """
    # 获取会话
    session = await session_service.get_session(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session {session_id} not found"
        )

    # 获取最后一次API响应
    try:
        # 从会话上下文中获取搜索结果
        context = session["context"]
        if "search_result" in context and context["search_result"]:
            # 获取最后一次搜索结果
            last_search = await session_service.get_last_search_result(session_id)
            if last_search:
                return ApiResponse[Dict[str, Any]](
                    code=200,
                    message="success",
                    data=last_search
                )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting last response: {str(e)}"
        )

    # 如果没有找到搜索结果，返回空对象
    return ApiResponse[Dict[str, Any]](
        code=200,
        message="success",
        data={}
    )

@router.delete("/{session_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除会话", description="删除指定的会话及其所有相关数据")
async def delete_session(
    session_id: str = Path(..., description="会话ID", example="123e4567-e89b-12d3-a456-426614174000"),
    session_service: SessionService = Depends(get_session_service)
):
    """
    删除会话API

    删除指定的会话及其所有相关数据，包括消息历史和上下文。此操作不可撤销。

    - **session_id**: 要删除的会话ID

    **示例请求**:
    ```
    DELETE /api/session/123e4567-e89b-12d3-a456-426614174000
    ```

    **响应**:
    - 204: 删除成功（无内容）

    **可能的错误**:
    - 404: 会话不存在
    - 500: 服务器内部错误
    """
    # 删除会话
    success = await session_service.delete_session(session_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Session {session_id} not found"
        )
