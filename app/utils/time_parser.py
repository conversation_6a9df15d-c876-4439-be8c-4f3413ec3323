"""
时间解析工具模块

将用户输入的相对时间转换为具体的日期时间格式
"""

import re
from datetime import datetime, timedelta
from typing import Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class TimeParser:
    """时间解析器"""
    
    # 星期映射
    WEEKDAY_MAPPING = {
        "周一": 0, "星期一": 0, "礼拜一": 0,
        "周二": 1, "星期二": 1, "礼拜二": 1,
        "周三": 2, "星期三": 2, "礼拜三": 2,
        "周四": 3, "星期四": 3, "礼拜四": 3,
        "周五": 4, "星期五": 4, "礼拜五": 4,
        "周六": 5, "星期六": 5, "礼拜六": 5,
        "周日": 6, "星期日": 6, "礼拜日": 6, "周末": 6
    }
    
    # 时间段映射
    TIME_PERIOD_MAPPING = {
        "早上": "09:00", "上午": "10:00", "中午": "12:00",
        "下午": "14:00", "晚上": "19:00", "夜里": "20:00"
    }

    @staticmethod
    def parse_viewing_time(time_text: str) -> Optional[str]:
        """
        解析看房时间，转换为标准格式
        
        Args:
            time_text: 用户输入的时间文本，如"周日11点半"、"明天下午3点"
            
        Returns:
            标准格式的日期时间字符串，如"2025-01-12 11:30:00"，解析失败返回None
        """
        if not time_text or time_text == "null" or time_text == "flexible":
            return None
            
        try:
            now = datetime.now()
            
            # 处理"明天"、"后天"等相对日期
            relative_date = TimeParser._parse_relative_date(time_text, now)
            if relative_date:
                time_part = TimeParser._extract_time_from_text(time_text)
                if time_part:
                    return f"{relative_date.strftime('%Y-%m-%d')} {time_part}:00"
                else:
                    # 没有具体时间，默认上午10点
                    return f"{relative_date.strftime('%Y-%m-%d')} 10:00:00"
            
            # 处理星期几
            weekday_date = TimeParser._parse_weekday(time_text, now)
            if weekday_date:
                time_part = TimeParser._extract_time_from_text(time_text)
                if time_part:
                    return f"{weekday_date.strftime('%Y-%m-%d')} {time_part}:00"
                else:
                    # 没有具体时间，默认上午10点
                    return f"{weekday_date.strftime('%Y-%m-%d')} 10:00:00"
            
            # 处理时间段，默认为今天
            time_period = TimeParser._parse_time_period(time_text)
            if time_period:
                return f"{now.strftime('%Y-%m-%d')} {time_period}:00"
            
            # 处理纯时间，默认为今天
            time_part = TimeParser._extract_time_from_text(time_text)
            if time_part:
                return f"{now.strftime('%Y-%m-%d')} {time_part}:00"
            
            # 如果都无法解析，返回None
            logger.warning(f"无法解析时间文本: {time_text}")
            return None
            
        except Exception as e:
            logger.error(f"时间解析出错: {str(e)}, 输入: {time_text}")
            return None

    @staticmethod
    def _parse_relative_date(text: str, base_date: datetime) -> Optional[datetime]:
        """解析相对日期，如明天、后天"""
        if "明天" in text:
            return base_date + timedelta(days=1)
        elif "后天" in text:
            return base_date + timedelta(days=2)
        elif "大后天" in text:
            return base_date + timedelta(days=3)
        elif "今天" in text:
            return base_date
        return None

    @staticmethod
    def _parse_weekday(text: str, base_date: datetime) -> Optional[datetime]:
        """解析星期几，返回最近的该星期的日期"""
        for weekday_name, weekday_num in TimeParser.WEEKDAY_MAPPING.items():
            if weekday_name in text:
                # 计算到下一个该星期几的天数
                days_ahead = weekday_num - base_date.weekday()
                if days_ahead <= 0:  # 如果是今天或之前，则指向下周
                    days_ahead += 7
                return base_date + timedelta(days=days_ahead)
        return None

    @staticmethod
    def _parse_time_period(text: str) -> Optional[str]:
        """解析时间段，如上午、下午"""
        for period_name, default_time in TimeParser.TIME_PERIOD_MAPPING.items():
            if period_name in text:
                return default_time
        return None

    @staticmethod
    def _extract_time_from_text(text: str) -> Optional[str]:
        """从文本中提取具体时间"""
        # 匹配各种时间格式
        time_patterns = [
            r'(\d{1,2})[点:](\d{1,2})[分]?',  # 11点30分 或 11:30
            r'(\d{1,2})点半',                 # 11点半
            r'(\d{1,2})[点]',                 # 11点
            r'(\d{1,2}):(\d{2})',             # 11:30
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, text)
            if match:
                if len(match.groups()) == 2:
                    hour = int(match.group(1))
                    minute = int(match.group(2))
                elif "点半" in match.group(0):
                    hour = int(match.group(1))
                    minute = 30
                else:
                    hour = int(match.group(1))
                    minute = 0
                
                # 格式化为HH:MM
                return f"{hour:02d}:{minute:02d}"
        
        return None

    @staticmethod
    def format_viewing_time_display(parsed_time: str) -> str:
        """
        格式化显示用的时间文本
        
        Args:
            parsed_time: 解析后的时间字符串，如"2025-01-12 11:30:00"
            
        Returns:
            用户友好的时间显示，如"1月12日(周日) 11:30"
        """
        try:
            dt = datetime.strptime(parsed_time, "%Y-%m-%d %H:%M:%S")
            weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            weekday = weekdays[dt.weekday()]
            return f"{dt.month}月{dt.day}日({weekday}) {dt.strftime('%H:%M')}"
        except Exception:
            return parsed_time 