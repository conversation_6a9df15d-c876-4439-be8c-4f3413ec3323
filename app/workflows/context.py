"""
工作流上下文模块

提供结构化的工作流上下文类，用于在工作流步骤之间传递数据。
"""

from typing import Dict, Any, List, Optional, TYPE_CHECKING
from dataclasses import dataclass, field

if TYPE_CHECKING:
    from app.workflows.response_types import WorkflowResponse


@dataclass
class WorkflowContext:
    """
    工作流上下文

    用于在工作流步骤之间传递数据的结构化类。
    使用数据类提供更好的类型提示和代码补全。
    """
    # 会话相关
    session_id: str
    user_message: str
    token: str = ""  # 用户认证token

    # 会话数据
    session: Dict[str, Any] = field(default_factory=dict)
    history: List[Dict[str, Any]] = field(default_factory=list)

    # 会话服务
    session_service: Any = None

    # 意图识别结果
    intent: Optional[str] = None
    confidence: float = 0.0

    # 参数提取结果
    parameter_types: List[str] = field(default_factory=list)
    extracted_params: Dict[str, Any] = field(default_factory=dict)
    updated_context: Dict[str, Any] = field(default_factory=dict)

    # 参数检查结果
    missing_params: List[str] = field(default_factory=list)
    has_location: bool = False
    has_budget: bool = False
    has_room_type: bool = False

    # 流程控制
    early_return: bool = False  # 是否提前返回（缺少核心参数时）

    # 搜索结果
    search_result: Dict[str, Any] = field(default_factory=dict)

    # 响应生成结果
    prompt_version: str = "v1"
    response: Optional[str] = None  # 保持向后兼容，但推荐使用workflow_response
    workflow_response: Optional['WorkflowResponse'] = None  # 新的统一响应类型
    response_type: Optional[str] = None  # 响应类型：text, location_suggestion, house_list 等（已废弃）

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowContext':
        """
        从字典创建上下文

        Args:
            data: 包含上下文数据的字典

        Returns:
            工作流上下文实例
        """
        # 提取必需的参数
        session_id = data.get("session_id", "")
        user_message = data.get("user_message", "")

        # 创建上下文实例
        context = cls(
            session_id=session_id,
            user_message=user_message
        )

        # 更新其他字段
        for key, value in data.items():
            if hasattr(context, key) and key not in ["session_id", "user_message"]:
                setattr(context, key, value)

        return context

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典

        Returns:
            包含上下文数据的字典
        """
        return {
            key: getattr(self, key)
            for key in self.__dataclass_fields__.keys()
            if getattr(self, key) is not None
        }

    def to_response_dict(self) -> Dict[str, Any]:
        """
        转换为响应字典

        返回适合API响应的字典格式。

        Returns:
            包含响应数据的字典
        """
        return {
            "session_id": self.session_id,
            "intent": self.intent,
            "confidence": self.confidence,
            "extracted_params": self.extracted_params,
            "context": self.updated_context,
            "search_result": self.search_result or {"error": "No search results available"},
            "response": self.response or "",
            "prompt_version": self.prompt_version
        }
