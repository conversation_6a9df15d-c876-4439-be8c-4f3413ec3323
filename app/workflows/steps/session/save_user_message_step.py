"""
保存用户消息步骤模块

提供将用户消息保存到数据库的工作流步骤。
"""

import logging

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService
from app.services.context_service import SessionState

logger = logging.getLogger(__name__)


class SaveUserMessageStep(WorkflowStep):
    """
    保存用户消息步骤

    将用户消息保存到数据库，并更新会话状态为处理中。
    """

    def __init__(self, session_service: SessionService):
        """
        初始化保存用户消息步骤

        Args:
            session_service: 会话服务，用于保存消息和更新会话状态
        """
        super().__init__("保存用户消息")
        self.session_service = session_service

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        保存用户消息

        Args:
            context: 工作流上下文，包含会话ID、用户消息、意图和提取的参数

        Returns:
            原始上下文
        """
        # 检查是否已经保存过用户消息（避免重复保存）
        user_message_saved = getattr(context, '_user_message_saved', False)
        if user_message_saved:
            logger.info("✅ SaveUserMessageStep：用户消息已在前面的步骤中保存，跳过重复保存")
        else:
            # 从上下文中获取必要信息
            session_id = context.session_id
            user_message = context.user_message
            intent = context.intent
            extracted_params = context.extracted_params

            # 保存用户消息
            await self.session_service.add_message(
                session_id=session_id,
                role="user",
                content=user_message,
                intent=intent,
                extracted_params=extracted_params
            )

            # 标记已保存
            setattr(context, '_user_message_saved', True)
            logger.info("✅ SaveUserMessageStep：已保存用户消息到数据库")

        # 更新会话状态为处理中
        await self.session_service.update_session_context(
            context.session_id, {"session_state": SessionState.PROCESSING}
        )

        return context
