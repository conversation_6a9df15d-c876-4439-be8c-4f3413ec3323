"""
更新上下文步骤模块

提供将提取的参数合并到会话上下文中的工作流步骤。
"""

import logging

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService

logger = logging.getLogger(__name__)


class UpdateContextStep(WorkflowStep):
    """
    更新上下文步骤

    将提取的参数合并到会话上下文中，并更新工作流上下文。
    """

    def __init__(self, session_service: SessionService):
        """
        初始化更新上下文步骤

        Args:
            session_service: 会话服务，用于合并参数和更新会话上下文
        """
        super().__init__("更新上下文")
        self.session_service = session_service

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        更新会话上下文

        Args:
            context: 工作流上下文，包含会话ID和提取的参数

        Returns:
            更新了上下文的工作流上下文
        """
        # 从上下文中获取必要信息
        session_id = context.session_id
        extracted_params = context.extracted_params

        self.logger.info(f"使用提取的参数更新上下文: {extracted_params}")

       

        # 获取当前完整的会话上下文（包含询问状态等）
        session_data = await self.session_service.get_session(session_id)
        if session_data:
            current_context = session_data.get("context", {})
            self.logger.info(f"合并前的当前上下文: {current_context}")
        else:
            self.logger.warning(f"未找到会话: {session_id}")
            current_context = {}

        # 合并提取的参数到会话上下文
        updated_context = await self.session_service.merge_extracted_params(
            session_id, extracted_params
        )

        # 重要：确保工作流上下文包含完整的会话状态（包括询问状态）
        # 而不仅仅是提取的参数
        context.updated_context = updated_context

        # 记录更新后的关键信息
        self.logger.info(f"更新后的完整上下文: {updated_context}")
        self.logger.info(f"询问状态 - location_asked: {updated_context.get('location_asked', 0)}")
        self.logger.info(f"询问状态 - budget_asked: {updated_context.get('budget_asked', 0)}")
        self.logger.info(f"询问状态 - room_type_asked: {updated_context.get('room_type_asked', 0)}")
        self.logger.info(f"参数状态 - location: {updated_context.get('location', '未找到')}")
        self.logger.info(f"参数状态 - budget: {updated_context.get('budget', '未找到')}")
        self.logger.info(f"参数状态 - room_type: {updated_context.get('room_type', '未找到')}")

        return context
