"""
获取会话步骤模块

提供从数据库获取会话信息和对话历史的工作流步骤。
"""

import logging

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService

logger = logging.getLogger(__name__)


class GetSessionStep(WorkflowStep):
    """
    获取会话步骤

    从数据库获取会话信息和对话历史，并更新上下文。
    """

    def __init__(self, session_service: SessionService):
        """
        初始化获取会话步骤

        Args:
            session_service: 会话服务，用于获取会话信息和对话历史
        """
        super().__init__("获取会话")
        self.session_service = session_service

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        获取会话信息

        Args:
            context: 工作流上下文，包含会话ID

        Returns:
            更新了会话信息和对话历史的上下文
        """
        # 从上下文中获取会话ID
        session_id = context.session_id

        if not session_id:
            raise ValueError("会话ID是必需的")

        # 获取会话信息
        session = await self.session_service.get_session(session_id)
        if not session:
            raise ValueError(f"未找到会话: {session_id}")

        # 获取对话历史
        history = await self.session_service.get_conversation_history(session_id)

        # 更新上下文
        context.session = session
        context.history = history
        
        # 重要：将session中的context数据初始化到context.updated_context中
        # 这样后续步骤就可以访问到之前存储的会话上下文数据（如house_list等）
        session_context = session.get("context", {})
        context.updated_context = session_context.copy()  # 使用copy避免直接引用
        
        self.logger.info(f"加载会话上下文: session_id={session_id}")
        self.logger.info(f"会话上下文包含house_list: {'house_list' in session_context}")
        if 'house_list' in session_context:
            self.logger.info(f"house_list长度: {len(session_context['house_list'])}")

        return context
