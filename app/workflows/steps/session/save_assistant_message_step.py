"""
保存助手消息步骤模块

提供将助手的响应消息保存到数据库的工作流步骤。
"""

import logging

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService
from app.services.context_service import SessionState

logger = logging.getLogger(__name__)


class SaveAssistantMessageStep(WorkflowStep):
    """
    保存助手消息步骤

    将助手的响应消息保存到数据库，并更新会话状态为空闲。
    只在非流式模式下且有响应时执行。
    """

    def __init__(self, session_service: SessionService):
        """
        初始化保存助手消息步骤

        Args:
            session_service: 会话服务，用于保存消息和更新会话状态
        """
        super().__init__("保存助手消息")
        self.session_service = session_service

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        保存助手消息

        Args:
            context: 工作流上下文，包含会话ID和响应内容

        Returns:
            原始上下文
        """
        # 从上下文中获取必要信息
        session_id = context.session_id
        response = context.response
        # 只在有响应时保存消息
        if response:
            # 保存助手消息
            await self.session_service.add_message(
                session_id=session_id,
                role="assistant",
                content=response
            )

            # 获取当前会话状态
            session_data = await self.session_service.get_session(session_id)
            current_state = session_data.get("context", {}).get("session_state", "idle")

            # 只有当当前状态不是browsing时，才更新为idle
            if current_state != "browsing":
                self.logger.info(f"更新会话状态为idle（当前状态: {current_state}）")
                await self.session_service.update_session_context(
                    session_id, {"session_state": SessionState.IDLE}
                )
            else:
                self.logger.info(f"保留会话状态为browsing")

        return context

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只在有响应时执行，且没有提前返回时执行。

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 如果已经提前返回，说明消息已经被手动保存了，不需要再次保存
        if context.early_return:
            self.logger.info("检测到early_return=True，跳过SaveAssistantMessageStep（消息已手动保存）")
            return False
            
        # 有响应就执行
        return context.response is not None
