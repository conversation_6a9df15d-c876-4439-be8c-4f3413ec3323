"""
意图识别步骤模块

提供识别用户意图的工作流步骤。
"""

import logging

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.workflows.constants import IntentGroup, Intent
from app.llm.intent import IntentRecognizer
from app.llm.chitchat import ChitchatHand<PERSON>
from app.services.session_service import SessionService
from app.workflows.response_types import create_text_response

logger = logging.getLogger(__name__)


class RecognizeIntentStep(WorkflowStep):
    """
    识别意图步骤

    识别用户消息的意图，如搜索房源、询问价格等。
    使用LLM进行意图识别，支持各种表达方式和同义词。
    """

    def __init__(self, intent_recognizer: IntentRecognizer, session_service: SessionService):
        """
        初始化识别意图步骤

        Args:
            intent_recognizer: 意图识别器，用于识别用户意图
            session_service: 会话服务，用于保存消息
        """
        super().__init__("识别意图")
        self.intent_recognizer = intent_recognizer
        self.session_service = session_service
        self.chitchat_handler = ChitchatHandler(session_service)

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        识别用户消息的意图

        Args:
            context: 工作流上下文，包含用户消息和对话历史

        Returns:
            更新了意图的上下文
        """
        # 获取必要信息
        user_message = context.user_message
        history = context.history

        # 识别意图
        intent, confidence = await self.intent_recognizer.recognize_intent(
            user_input=user_message,
            conversation_history=history
        )

        # 记录意图和置信度
        self.logger.info(f"识别到的意图: {intent}, 置信度: {confidence}")

        # 设置上下文
        context.intent = intent
        context.confidence = confidence

        # 特殊处理闲聊意图
        if intent == "chitchat":
            self.logger.info("检测到闲聊意图，使用闲聊处理器处理")
            # 使用独立的闲聊处理器处理
            response = await self.chitchat_handler.handle_chitchat(
                user_message=user_message,
                session_id=context.session_id,
                conversation_history=history
            )

            # 创建统一的工作流响应
            context.workflow_response = create_text_response(
                content=response,
                session_state="chitchat",
                delay=0.05,
                requires_typewriter=True
            )

            # 保持向后兼容
            context.response = response
            context.early_return = True
            return context

        # 检查其他意图是否支持
        if intent not in IntentGroup.SUPPORTED:
            self.logger.info(f"意图 {intent} 暂不支持")

            # 根据不同意图类型设置回复内容
            if intent == Intent.ZIROOM_BUSINESS_INQUIRY:
                response_text = "小木当前只支持找房，暂无法回答相关问题，后续将持续优化并完善。有找房需求的话，可以告诉小木哦～"
            elif intent == Intent.RENTAL_POLICY_INQUIRY:
                response_text = "小木当前只支持找房，暂无法回答相关问题，后续将持续优化并完善。如果您有找房需求，我很乐意为您提供帮助！"
            elif intent == Intent.COMPLAINT:
                response_text = "抱歉让您体验不好，小木当前专注高效找房。如需投诉，可以在APP-我的-右上角客服联系人工客服。有找房需求的话，也欢迎告诉小木！"
            elif intent == Intent.PROHIBITED_CONTENT:
                response_text = "抱歉，小木当前专注高效找房。如果有找房需求的话，可以告诉小木～"
            else:
                response_text = "当前只支持找房，若有其他需求请联系人工客服。"

            # 创建统一的工作流响应
            context.workflow_response = create_text_response(
                content=response_text,
                session_state="unsupported_intent",
                delay=0.05,
                requires_typewriter=True
            )

            # 保持向后兼容
            context.response = response_text
                
            # 统一处理：保存消息并设置提前返回
            await self.session_service.add_message(
                session_id=context.session_id,
                role="assistant",
                content=context.response
            )
            self.logger.info(f"✅ RecognizeIntentStep：已保存{intent}意图回复到数据库")
            context.early_return = True

        return context
