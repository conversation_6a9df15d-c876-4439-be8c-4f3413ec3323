"""
小区咨询处理步骤模块

专门处理用户对小区设施、环境、配套等详细信息的咨询。
"""

import logging
import json
from typing import Dict, Any, List

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.house_service import HouseService
from app.services.session_service import SessionService
from app.llm.client import generate_completion
from app.workflows.response_types import create_text_response

logger = logging.getLogger(__name__)


class ProcessCommunityInquiryStep(WorkflowStep):
    """
    处理小区咨询步骤
    
    当用户询问具体小区的设施、环境、配套等信息时执行此步骤。
    主要功能：
    1. 从用户输入中提取小区名称
    2. 调用house_service获取小区数据
    3. 使用LLM基于数据回答用户问题
    """

    def __init__(self, house_service: HouseService, session_service: SessionService):
        """
        初始化小区咨询处理步骤
        
        Args:
            house_service: 房源服务，用于获取小区数据
            session_service: 会话服务，用于保存消息
        """
        super().__init__("处理小区咨询")
        self.house_service = house_service
        self.session_service = session_service
        self.logger = logging.getLogger(__name__)

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        处理小区咨询

        Args:
            context: 工作流上下文

        Returns:
            更新后的上下文
        """
        user_message = context.user_message
        session_id = context.session_id

        self.logger.info(f"开始处理小区咨询: {user_message}")

        try:
            # 1. 获取小区名称（应该已经由前面的步骤提取好了）
            community_name = await self._extract_community_name(user_message, context)
            
            if not community_name:
                # 没有位置信息，不执行小区咨询，让后续的参数收集流程处理
                self.logger.info("没有找到位置信息，跳过小区咨询处理")
                return context
            
            # 2. 获取小区数据
            community_data = await self._get_community_data(community_name)
            
            if not community_data:
                # 没有找到小区数据
                response = f"抱歉，我没有找到「{community_name}」的详细信息。您可以换个小区名称试试，或者告诉我您想了解什么类型的信息。"

                # 创建统一的工作流响应
                context.workflow_response = create_text_response(
                    content=response,
                    session_state="community_inquiry_no_data",
                    delay=0.05,
                    requires_typewriter=True
                )

                # 保持向后兼容
                context.response = response
                await self.session_service.add_message(
                    session_id=session_id,
                    role="assistant",
                    content=response
                )
                context.early_return = True
                return context
            
            # 3. 使用LLM生成回答
            response = await self._generate_community_response(user_message, community_name, community_data)

            # 4. 创建统一的工作流响应
            context.workflow_response = create_text_response(
                content=response,
                session_state="community_inquiry",
                delay=0.05,
                requires_typewriter=True
            )

            # 保持向后兼容
            context.response = response
            await self.session_service.add_message(
                session_id=session_id,
                role="assistant",
                content=response
            )

            # 标记提前返回
            context.early_return = True
            self.logger.info(f"✅ 小区咨询处理完成: {community_name}")
            
        except Exception as e:
            self.logger.error(f"处理小区咨询异常: {str(e)}", exc_info=True)
            response = "抱歉，处理您的小区咨询时出现了问题，请稍后再试。"

            # 创建统一的工作流响应
            context.workflow_response = create_text_response(
                content=response,
                session_state="community_inquiry_error",
                delay=0.05,
                requires_typewriter=True
            )

            # 保持向后兼容
            context.response = response
            await self.session_service.add_message(
                session_id=session_id,
                role="assistant",
                content=response
            )
            context.early_return = True
        
        return context

    async def _extract_community_name(self, user_message: str, context: WorkflowContext) -> str:
        """
        从上下文中获取小区名称
        
        Args:
            user_message: 用户输入
            context: 工作流上下文
            
        Returns:
            提取的小区名称，如果没有找到返回空字符串
        """
        try:
            # 优先从updated_context中获取已经提取好的位置信息
            if hasattr(context, 'updated_context') and context.updated_context:
                context_location = context.updated_context.get('location')
                if context_location:
                    self.logger.info(f"从上下文中获取位置: {context_location}")
                    return context_location
            
            # 如果updated_context中没有，检查extracted_params
            if hasattr(context, 'extracted_params') and context.extracted_params:
                extracted_location = context.extracted_params.get('location')
                if extracted_location and extracted_location != 'null':
                    self.logger.info(f"从提取参数中获取位置: {extracted_location}")
                    return extracted_location
            
            self.logger.warning(f"未能从上下文中获取小区名称")
            return ""
            
        except Exception as e:
            self.logger.error(f"获取小区名称异常: {str(e)}")
            return ""

    async def _get_community_data(self, community_name: str) -> Dict[str, Any]:
        """
        获取小区数据
        
        Args:
            community_name: 小区名称
            
        Returns:
            小区数据字典
        """
        try:
            # 构造搜索参数
            search_params = {
                "location": community_name,
                "location_type": "小区",
                "room_or_resblock": "resblock"  # 查询小区信息
            }
            
            self.logger.info(f"查询小区数据: {search_params}")
            
            # 调用house_service搜索
            search_result = await self.house_service.search_houses(search_params)
            
            self.logger.info(f"获取到小区数据: {bool(search_result)}")
            
            if search_result and "data" in search_result:
                return search_result
            
            return {}
            
        except Exception as e:
            self.logger.error(f"获取小区数据异常: {str(e)}")
            return {}

    async def _generate_community_response(self, user_question: str, community_name: str, community_data: Dict[str, Any]) -> str:
        """
        使用LLM生成小区咨询回答
        
        Args:
            user_question: 用户问题
            community_name: 小区名称
            community_data: 小区数据
            
        Returns:
            LLM生成的回答
        """
        try:
            # 构建数据摘要
            data_summary = self._build_data_summary(community_data)
            
            # 构建提示
            system_prompt = f"""你是小木，专业的小区信息咨询助手。基于以下小区数据回答用户关于「{community_name}」的问题：

{data_summary}

回答要求：
1. 基于数据和房屋建筑常识回答问题
2. 关于电梯：6层及以下建筑一般没有电梯，7层及以上一般有电梯
3. 如果数据中缺少信息但可以根据常识推断，请给出推断并建议联系管家确认
4. 如果完全无法回答，说："小木也不知道这个信息，请您换个问题试试～"
5. 语气要简洁友好，不要超过80字"""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_question}
            ]
            
            self.logger.info(f"发送LLM请求生成小区咨询回答")
            
            response = await generate_completion(
                messages=messages,
                temperature=0.3  # 较低温度保证准确性
            )
            
            # 解析响应
            if hasattr(response, 'choices') and len(response.choices) > 0:
                answer = response.choices[0].message.content.strip()
            elif isinstance(response, dict) and 'choices' in response:
                answer = response['choices'][0]['message']['content'].strip()
            else:
                raise ValueError("Unexpected response format")
            
            self.logger.info(f"LLM生成回答成功，长度: {len(answer)}")
            return answer
            
        except Exception as e:
            self.logger.error(f"LLM生成回答异常: {str(e)}")
            return "小木也不知道这个信息，请您换个问题试试～"

    def _build_data_summary(self, community_data: Dict[str, Any]) -> str:
        """
        构建小区数据摘要
        
        Args:
            community_data: 小区数据
            
        Returns:
            数据摘要字符串
        """
        try:
            if not community_data or "data" not in community_data:
                return "暂无该小区的详细信息。"
            
            data = community_data["data"]
            
            # 固定的数据结构：{"data": {"summary": "...", "rooms": [...], "promotionRooms": null}}
            rooms = data.get("rooms", [])
            promotion_rooms = data.get("promotionRooms") or []
            all_rooms = rooms + promotion_rooms
            
            if not all_rooms:
                return "暂无该小区的房源信息。"
            
            return self._format_rooms_data(all_rooms)
            
        except Exception as e:
            self.logger.error(f"构建数据摘要异常: {str(e)}")
            return "暂无该小区的详细信息。"

    def _format_rooms_data(self, rooms: List[Dict[str, Any]]) -> str:
        """
        格式化房源数据，提取小区信息
        
        Args:
            rooms: 房源列表
            
        Returns:
            格式化后的小区信息
        """
        if not rooms:
            return "暂无房源信息。"
        
        # 基本信息（从第一套房源获取）
        first_room = rooms[0]
        resblock_name = first_room.get("resblock_name", "未知小区")
        district_name = first_room.get("district_name", "")
        bizcircle_name = first_room.get("bizcircle_name", "")
        
        summary_parts = [
            f"小区名称：{resblock_name}",
            f"位置：{district_name} {bizcircle_name}",
            f"在售房源：{len(rooms)}套"
        ]
        
        # 价格范围
        prices = [room.get("price") for room in rooms if room.get("price")]
        if prices:
            summary_parts.append(f"价格范围：{min(prices)}-{max(prices)}元/月")
        
        # 楼层信息
        floor_totals = [room.get("floor_total") for room in rooms if room.get("floor_total")]
        if floor_totals:
            # 转换为整数并找最大值
            valid_floors = []
            for floor in floor_totals:
                try:
                    if isinstance(floor, str):
                        valid_floors.append(int(floor))
                    elif isinstance(floor, (int, float)):
                        valid_floors.append(int(floor))
                except (ValueError, TypeError):
                    continue
            
            if valid_floors:
                max_floor = max(valid_floors)
                summary_parts.append(f"楼层：{max_floor}层建筑")
        
        # 地铁信息
        subway_info = first_room.get("subway_station_info", "")
        if subway_info:
            summary_parts.append(f"交通：{subway_info}")
        
        # 配套设施（合并所有标签）
        all_tags = []
        for room in rooms:
            tags = room.get("tags", [])
            for tag in tags:
                if isinstance(tag, dict) and "title" in tag:
                    all_tags.append(tag["title"])
        
        if all_tags:
            unique_tags = list(set(all_tags))[:5]  # 只取前5个
            summary_parts.append(f"配套设施：{', '.join(unique_tags)}")
        
        return "\n".join(summary_parts)



    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤
        
        Args:
            context: 工作流上下文
            
        Returns:
            如果意图是community_inquiry且有位置信息则执行
        """
        # 首先检查意图
        if context.intent != "community_inquiry":
            return False
        
        # 检查是否有位置信息
        has_location = False
        
        # 从updated_context中检查
        if hasattr(context, 'updated_context') and context.updated_context:
            has_location = bool(context.updated_context.get('location'))
        
        # 从extracted_params中检查
        if not has_location and hasattr(context, 'extracted_params') and context.extracted_params:
            location = context.extracted_params.get('location')
            has_location = bool(location and location != 'null')
        
        should_execute = has_location
        
        if should_execute:
            self.logger.info("检测到小区咨询意图且有位置信息，执行小区咨询处理步骤")
        else:
            self.logger.info(f"小区咨询意图但缺少位置信息，跳过处理，让参数收集流程处理")
        
        return should_execute 