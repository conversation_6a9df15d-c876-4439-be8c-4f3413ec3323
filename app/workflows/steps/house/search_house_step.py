"""
房源搜索步骤模块

提供搜索房源的工作流步骤。
"""

import logging
from typing import Dict, <PERSON><PERSON>
import csv
from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.services.house_service import HouseService
from app.services.session_service import SessionService
from app.services.context_service import SessionState

logger = logging.getLogger(__name__)


bizcircle_ai_summaries: Dict[str, Tuple[str, str]] = {}

def load_bizcircle_ai_summaries():
    with open('scripts/bizcircle_data.csv', 'r') as f:
        reader = csv.reader(f)
        next(reader) # skip header
        for row in reader:
            bizcircle_ai_summaries[row[0]] = (row[1], row[2])
    return bizcircle_ai_summaries

load_bizcircle_ai_summaries()

class SearchHouseStep(WorkflowStep):
    """
    搜索房源步骤

    根据上下文中的参数搜索符合条件的房源。
    支持分页和各种搜索条件。
    """

    def __init__(self, house_service: HouseService, session_service: SessionService):
        """
        初始化搜索房源步骤

        Args:
            house_service: 房源服务，用于搜索房源
        """
        super().__init__("搜索房源")
        self.house_service = house_service
        self.session_service = session_service

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        搜索房源

        Args:
            context: 工作流上下文，包含搜索参数

        Returns:
            添加了搜索结果的上下文
        """
        # 从上下文中获取必要信息
        session_id = context.session_id
        updated_context = context.updated_context

        # 更新会话状态为搜索中
        if session_id:
            await self.session_service.update_session_context(
                session_id, {"session_state": SessionState.BROWSING}
            )

        # 确保所有参数都从上下文中传递到搜索参数中
        for key, value in context.updated_context.items():
            if value is not None:
                updated_context[key] = value
                self.logger.info(f"使用参数 {key}: {value}")

        # 如果是继续搜索意图，记录当前的分页参数
        if context.intent == "continue_search" and "current_page" in updated_context:
            self.logger.info(f"继续搜索，使用分页参数: current_page={updated_context['current_page']}")

        # 搜索房源
        search_result = await self.house_service.search_houses(updated_context)
        
        # 处理小区房源数据存储
        self._handle_resblock_house_data(updated_context, search_result)
        
        # 重要：如果存储了house_list，需要立即保存到数据库
        if 'house_list' in updated_context and session_id:
            try:
                await self.house_service.session_service.update_session_context(
                    session_id, {"house_list": updated_context["house_list"]}
                )
                self.logger.info(f"✅ house_list已保存到数据库: 共{len(updated_context['house_list'])}套房源")
            except Exception as e:
                self.logger.error(f"保存house_list到数据库失败: {str(e)}", exc_info=True)

        # 如果是商圈推荐, 注入额外信息
        print(f'updated_context: {updated_context}')
        if updated_context.get("location_type") == "行政区":
            print(f'商圈推荐: {search_result}')
            for item in search_result["data"]:
                bizcircle_ai_summary = bizcircle_ai_summaries.get(item["bizcircle_name"])
                if bizcircle_ai_summary:
                    print(f'bizcircle_ai_summary: {bizcircle_ai_summary}')
                    item["description2"] = bizcircle_ai_summary[0]
                    item['highlight2'] = bizcircle_ai_summary[1]

        # 更新上下文
        context.search_result = search_result
        
        # 确保updated_context被传递
        context.updated_context = updated_context
        self.logger.info(f"✅ 更新上下文完成，updated_context包含house_list: {'house_list' in updated_context}")
        if 'house_list' in updated_context:
            self.logger.info(f"updated_context中house_list长度: {len(updated_context['house_list'])}")

        # 更新会话状态为浏览中
        if session_id:
            self.logger.info(f"更新会话状态为浏览中: session_id={session_id}, state={SessionState.BROWSING}")
            try:
                # 注意：这里不要重新赋值updated_context，避免覆盖house_list数据
                await self.house_service.session_service.update_session_context(
                    session_id, {"session_state": SessionState.BROWSING}
                )
                self.logger.info(f"会话状态更新成功")
            except Exception as e:
                self.logger.error(f"更新会话状态失败: {str(e)}", exc_info=True)

        return context

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只有在意图是搜索房源且所有必要参数都已提供时才执行。
        如果是继续搜索意图，则直接执行。

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 获取意图和参数状态
        intent = context.intent
        has_location = context.has_location
        has_budget = context.has_budget
        has_room_type = context.has_room_type
        extracted_params = context.extracted_params if hasattr(context, 'extracted_params') else {}
        updated_context = context.updated_context if hasattr(context, 'updated_context') else {}

        # 检查当前消息中是否已经提取到了参数
        extracted_location = "location" in extracted_params and extracted_params["location"]
        extracted_budget = "budget" in extracted_params and extracted_params["budget"]
        extracted_room_type = "room_type" in extracted_params and extracted_params["room_type"]

        # 检查上下文中是否已经有了参数
        context_location = "location" in updated_context and updated_context["location"]
        context_budget = "budget" in updated_context and updated_context["budget"]
        context_room_type = "room_type" in updated_context and updated_context["room_type"]

        # 综合判断是否有所有必要参数
        has_all_location = has_location or extracted_location or context_location
        has_all_budget = has_budget or extracted_budget or context_budget
        has_all_room_type = has_room_type or extracted_room_type or context_room_type

        # 如果是继续搜索意图，直接执行
        if intent == "continue_search":
            self.logger.info("检测到继续搜索意图，直接执行搜索步骤")
            return True

        # 只有在意图是搜索房源且所有必要参数都已提供时才执行
        should_execute = (intent == "search_house" and
                         has_all_location and
                         has_all_budget and
                         has_all_room_type)

        self.logger.info(f"是否执行搜索房源步骤: {should_execute}, 意图: {intent}, 有位置: {has_all_location}, 有预算: {has_all_budget}, 有户型: {has_all_room_type}")

        return should_execute

    def _handle_resblock_house_data(self, context: dict, search_result: dict):
        """
        处理小区房源数据存储

        Args:
            context: 上下文字典
            search_result: 搜索结果
        """
        # 只有小区类型才处理house_list存储
        if context.get("location_type") != "小区":
            self.logger.info(f"非小区类型 (location_type={context.get('location_type')})，跳过house_list处理")
            return
        
        self.logger.info(f"=== 处理小区房源数据存储 ===")
        self.logger.info(f"search_result是否存在: {bool(search_result)}")
        
        if not search_result:
            self.logger.warning("没有搜索结果，无法存储house_list")
            return
        
        self.logger.info("条件满足：是小区类型且有搜索结果")
        
        # 提取房源数据
        house_data = []
        if "data" in search_result and isinstance(search_result["data"], list):
            # 处理房源列表数据
            house_data = search_result["data"]
            self.logger.info(f"从列表格式提取房源数据: {len(house_data)}套")
        elif "data" in search_result and isinstance(search_result["data"], dict):
            # 处理嵌套的房源数据
            data = search_result["data"]
            rooms = data.get("rooms", []) or []
            promotion_rooms = data.get("promotionRooms", []) or []
            house_data = rooms + promotion_rooms
            self.logger.info(f"从嵌套格式提取房源数据: rooms={len(rooms)}, promotionRooms={len(promotion_rooms)}, 总计={len(house_data)}套")
        else:
            self.logger.warning(f"未识别的搜索结果格式: {type(search_result.get('data'))}")
        
        # 只保存必要的字段到context中
        if house_data:
            # 提取必要字段：resblock_id, house_id, name
            simplified_house_list = []
            for house in house_data:
                simplified_house = {}
                
                # 必要字段
                if house.get("resblock_id"):
                    simplified_house["resblock_id"] = house["resblock_id"]
                if house.get("house_id"):
                    simplified_house["house_id"] = house["house_id"]
                    
                name = house.get("name")
                if name:
                    simplified_house["name"] = name
                
                # 只有包含必要字段才添加
                if simplified_house.get("resblock_id") and simplified_house.get("house_id"):
                    simplified_house_list.append(simplified_house)
                else:
                    self.logger.warning(f"房源缺少必要字段，跳过: {house.get('resblock_id')}, {house.get('house_id')}")
            
            context["house_list"] = simplified_house_list
            self.logger.info(f"✅ 成功存储简化的房源数据到context: 共{len(simplified_house_list)}套房源")
        else:
            self.logger.info("❌ 没有找到房源数据，清空house_list")
            context["house_list"] = []
        
        self.logger.info(f"处理完成后context中是否有house_list: {'house_list' in context}")
        self.logger.info(f"===============================")
