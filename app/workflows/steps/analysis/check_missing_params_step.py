"""
检查缺失参数步骤模块

提供检查搜索房源所需的关键参数是否完整的工作流步骤。
"""

import logging

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.workflows.constants import Intent

logger = logging.getLogger(__name__)


class CheckMissingParamsStep(WorkflowStep):
    """
    检查缺失参数步骤

    检查搜索房源所需的关键参数是否完整，包括位置、预算和户型。
    如果参数不完整，标记缺失的参数，以便后续步骤询问用户。
    同时尝试从提取的参数中补充可能未更新到上下文的参数。
    """

    def __init__(self):
        """初始化检查缺失参数步骤"""
        super().__init__("检查缺失参数")

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        检查缺失参数并尝试补充

        Args:
            context: 工作流上下文，包含更新后的上下文和提取的参数

        Returns:
            更新了参数状态的上下文
        """
        # 获取上下文和提取的参数
        updated_context = context.updated_context
        extracted_params = context.extracted_params

        self.logger.info(f"检查上下文中的缺失参数: {updated_context}")

        # 检查关键参数是否存在
        has_location = "location" in updated_context and updated_context["location"]
        has_budget = "budget" in updated_context and updated_context["budget"]
        has_room_type = "room_type" in updated_context and updated_context["room_type"]

        # 记录当前参数状态
        self.logger.info(f"位置参数: {updated_context.get('location', '未找到')}")
        self.logger.info(f"预算参数: {updated_context.get('budget', '未找到')}")
        self.logger.info(f"户型参数: {updated_context.get('room_type', '未找到')}")

        # 尝试从提取的参数中补充缺失的参数
        self._try_update_location(updated_context, extracted_params, has_location)
        self._try_update_budget(updated_context, extracted_params, has_budget)
        self._try_update_room_type(updated_context, extracted_params, has_room_type)

        # 重新检查参数状态
        has_location = "location" in updated_context and updated_context["location"]
        has_budget = "budget" in updated_context and updated_context["budget"]
        has_room_type = "room_type" in updated_context and updated_context["room_type"]

        # 记录缺失的参数
        missing_params = []
        if not has_location:
            missing_params.append("location")
        if not has_budget:
            missing_params.append("budget")
        if not has_room_type:
            missing_params.append("room_type")

        # 更新上下文
        context.missing_params = missing_params
        context.has_location = has_location
        context.has_budget = has_budget
        context.has_room_type = has_room_type
        context.updated_context = updated_context

        self.logger.info(f"缺失参数: {missing_params}, 有位置: {has_location}, 有预算: {has_budget}, 有户型: {has_room_type}")

        return context

    def _try_update_location(self, updated_context, extracted_params, has_location):
        """尝试更新位置参数"""
        if not has_location and "location" in extracted_params and extracted_params["location"] != "null" and extracted_params["location"] is not None:
            location = extracted_params["location"]
            # 只有当位置参数有实际值时才更新
            if location and isinstance(location, str) and location.strip():
                self.logger.info(f"在提取的参数中找到位置，但未在上下文中更新: {location}")
                # 更新位置参数
                updated_context["location"] = location
                self.logger.info(f"手动更新位置参数: {updated_context['location']}")
            else:
                self.logger.warning(f"位置参数为空或无效: {location}")

    def _try_update_budget(self, updated_context, extracted_params, has_budget):
        """尝试更新预算参数"""
        if not has_budget and "budget" in extracted_params and extracted_params["budget"] != "null" and extracted_params["budget"] is not None:
            self.logger.info(f"在提取的参数中找到预算，但未在上下文中更新: {extracted_params['budget']}")
            # 尝试手动更新预算参数
            budget = extracted_params["budget"]
            if isinstance(budget, str) and "-" in budget:
                try:
                    # 处理范围格式的预算 (例如: "3000-5000")
                    min_val, max_val = budget.split("-")
                    min_val = min_val.strip()
                    max_val = max_val.strip()

                    # 处理单位 (例如: "3k-5k")
                    if max_val.lower().endswith("k"):
                        max_val = max_val.lower().replace("k", "")
                        max_val = float(max_val) * 1000

                    if min_val.lower().endswith("k"):
                        min_val = min_val.lower().replace("k", "")
                        min_val = float(min_val) * 1000

                    # 更新预算参数为API所需格式
                    updated_context["budget"] = f"{int(float(min_val))},{int(float(max_val))}"
                    self.logger.info(f"手动更新预算参数: {updated_context['budget']}")
                except Exception as e:
                    self.logger.error(f"处理预算参数时出错: {str(e)}")
            else:
                # 直接更新预算参数
                updated_context["budget"] = budget
                self.logger.info(f"直接更新预算参数: {updated_context['budget']}")

    def _try_update_room_type(self, updated_context, extracted_params, has_room_type):
        """尝试更新户型参数"""
        if not has_room_type and "room_type" in extracted_params and extracted_params["room_type"] != "null" and extracted_params["room_type"] is not None:
            room_type = extracted_params["room_type"]
            # 只有当户型参数有实际值时才更新
            if room_type and isinstance(room_type, str) and room_type.strip():
                self.logger.info(f"在提取的参数中找到户型，但未在上下文中更新: {room_type}")
                # 更新户型参数
                updated_context["room_type"] = room_type
                self.logger.info(f"手动更新户型参数: {updated_context['room_type']}")
            else:
                self.logger.warning(f"户型参数为空或无效: {room_type}")

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只有在意图是搜索房源或继续搜索时才检查缺失参数。

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        intent = context.intent

        should_execute = intent == Intent.SEARCH_HOUSE or intent == Intent.CONTINUE_SEARCH

        if should_execute:
            self.logger.info("意图是搜索房源或继续搜索，执行参数检查")
        else:
            self.logger.info(f"意图不是搜索房源或继续搜索 ({intent})，跳过参数检查")

        return should_execute
