"""
参数分析步骤模块

提供分析用户输入中包含哪些参数类型的工作流步骤。
"""

import logging
from typing import List

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.workflows.constants import IntentGroup
from app.llm.analysis import ParameterAnalyzer

logger = logging.getLogger(__name__)


class AnalyzeParametersStep(WorkflowStep):
    """
    分析参数步骤

    分析用户输入中包含哪些参数类型，如位置、预算、户型等。
    同时处理分页请求，设置继续搜索意图。
    """

    def __init__(self, parameter_analyzer: ParameterAnalyzer):
        """
        初始化分析参数步骤

        Args:
            parameter_analyzer: 参数分析器，用于分析用户输入中的参数类型
        """
        super().__init__("分析参数")
        self.parameter_analyzer = parameter_analyzer
        self.logger = logging.getLogger(__name__)

    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        分析用户输入中包含哪些参数类型

        Args:
            context: 工作流上下文，包含用户消息和对话历史

        Returns:
            更新了参数类型的上下文
        """
        # 从上下文中获取必要信息
        user_message = context.user_message
        conversation_history = context.history

        self.logger.info(f"分析用户消息中的参数: {user_message}")

        # 分析参数类型
        parameter_types = await self.parameter_analyzer.analyze_parameters(
            user_input=user_message,
            conversation_history=conversation_history
        )

        self.logger.info(f"分析出的参数类型: {parameter_types}")

        # 更新上下文
        context.parameter_types = parameter_types

        return context

    def should_execute(self, context: WorkflowContext) -> bool:
        """
        判断是否应该执行此步骤

        只有在有用户消息且意图是找房相关时才执行。
        找房相关意图包括：search_house, continue_search, community_inquiry

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        # 首先检查是否有用户消息
        if not context.user_message:
            return False

        # 检查意图是否已识别
        if not hasattr(context, 'intent') or not context.intent:
            return False

        # 只有找房相关意图才执行参数分析
        should_execute = context.intent in IntentGroup.HOUSING_RELATED

        if not should_execute:
            self.logger.info(f"意图不是找房相关 ({context.intent})，跳过参数分析")

        return should_execute
