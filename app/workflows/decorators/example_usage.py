"""
装饰器使用示例

展示如何使用消息保存装饰器来简化工作流步骤中的消息保存逻辑。
"""

import logging
from typing import Dict, Any

from app.workflows.base import WorkflowStep
from app.workflows.context import WorkflowContext
from app.workflows.decorators import auto_save_user_message, auto_save_assistant_message, auto_save_messages, MessageSavingMixin
from app.services.session_service import SessionService


class ExampleWorkflowStep(WorkflowStep):
    """
    示例工作流步骤 - 使用装饰器自动保存消息
    """
    
    def __init__(self, session_service: SessionService):
        super().__init__("示例步骤")
        self.session_service: SessionService = session_service
    
    @auto_save_user_message()
    @auto_save_assistant_message()
    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        执行步骤 - 装饰器会自动处理消息保存
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        # 纯业务逻辑，不需要手动保存消息
        if context.user_message == "测试":
            context.response = "这是一个测试回复"
            context.early_return = True
            return context
        
        # 正常处理逻辑
        context.response = "处理完成"
        return context


class ExampleWorkflowStepWithCombinedDecorator(WorkflowStep):
    """
    示例工作流步骤 - 使用组合装饰器
    """
    
    def __init__(self, session_service: SessionService):
        super().__init__("示例步骤（组合装饰器）")
        self.session_service: SessionService = session_service
    
    @auto_save_messages(save_user=True, save_assistant=True)
    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        执行步骤 - 使用组合装饰器
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        # 业务逻辑
        context.response = "使用组合装饰器的回复"
        context.early_return = True
        return context


class ExampleWorkflowStepWithMixin(WorkflowStep, MessageSavingMixin):
    """
    示例工作流步骤 - 使用混入类手动控制消息保存
    """
    
    def __init__(self, session_service: SessionService):
        super().__init__("示例步骤（混入类）")
        self.session_service: SessionService = session_service
    
    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        执行步骤 - 使用混入类手动控制消息保存时机
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        # 手动保存用户消息
        await self.save_user_message(context)
        
        # 业务逻辑
        if context.user_message == "特殊处理":
            context.response = "特殊处理的回复"
            # 手动保存助手消息
            await self.save_assistant_message(context)
            context.early_return = True
            return context
        
        # 正常处理
        context.response = "正常处理的回复"
        return context


class ExampleWorkflowStepOnlyUserMessage(WorkflowStep):
    """
    示例工作流步骤 - 只保存用户消息
    """
    
    def __init__(self, session_service: SessionService):
        super().__init__("示例步骤（只保存用户消息）")
        self.session_service: SessionService = session_service
    
    @auto_save_user_message()
    async def execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        执行步骤 - 只保存用户消息，不保存助手消息
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        # 业务逻辑
        # 这里不会自动保存助手消息，因为没有使用 @auto_save_assistant_message()
        context.response = "只保存用户消息的回复"
        return context


# 使用示例
async def example_usage() -> None:
    """
    装饰器使用示例
    """
    from app.services.session_service import SessionService
    from sqlalchemy.ext.asyncio import AsyncSession
    
    # 假设有一个数据库会话和会话服务
    # db_session: AsyncSession = ...
    # session_service = SessionService(db_session)
    
    # 创建工作流上下文
    context = WorkflowContext(
        session_id="test_session",
        user_message="测试消息"
    )
    
    # 使用不同的装饰器示例
    # step1 = ExampleWorkflowStep(session_service)
    # result1 = await step1.execute(context)
    
    # step2 = ExampleWorkflowStepWithCombinedDecorator(session_service)
    # result2 = await step2.execute(context)
    
    # step3 = ExampleWorkflowStepWithMixin(session_service)
    # result3 = await step3.execute(context)
    
    print("装饰器使用示例完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
