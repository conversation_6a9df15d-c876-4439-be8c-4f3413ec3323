"""
消息保存装饰器模块

提供自动保存用户消息和助手消息的装饰器，简化工作流步骤中的消息保存逻辑。
"""

import logging
from functools import wraps
from typing import Callable, Any, Awaitable, Optional

from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService

# 定义类型变量
WorkflowStepMethod = Callable[[Any, WorkflowContext], Awaitable[WorkflowContext]]


def auto_save_user_message(session_service_attr: str = "session_service") -> Callable[[WorkflowStepMethod], WorkflowStepMethod]:
    """
    自动保存用户消息的装饰器

    在工作流步骤执行前自动保存用户消息，确保即使步骤提前返回也能保存用户消息。

    Args:
        session_service_attr: 会话服务在步骤实例中的属性名，默认为"session_service"

    Usage:
        @auto_save_user_message()
        async def execute(self, context: WorkflowContext) -> WorkflowContext:
            # 业务逻辑
            pass
    """
    def decorator(execute_func: WorkflowStepMethod) -> WorkflowStepMethod:
        @wraps(execute_func)
        async def wrapper(self: Any, context: WorkflowContext) -> WorkflowContext:
            logger: logging.Logger = logging.getLogger(f"{self.__class__.__name__}.auto_save_user_message")

            # 获取会话服务，确保类型安全
            session_service: Optional[SessionService] = getattr(self, session_service_attr, None)
            if session_service is None:
                logger.warning(f"未找到会话服务属性: {session_service_attr}")
                return await execute_func(self, context)

            # 检查是否已经保存过用户消息（避免重复保存）
            user_message_saved: bool = getattr(context, '_user_message_saved', False)
            if not user_message_saved:
                try:
                    await session_service.add_message(
                        session_id=context.session_id,
                        role="user",
                        content=context.user_message,
                        intent=context.intent,
                        extracted_params=context.extracted_params
                    )
                    # 标记已保存，避免重复保存
                    setattr(context, '_user_message_saved', True)
                    logger.info(f"✅ {self.__class__.__name__}：已保存user消息到数据库")
                except Exception as e:
                    logger.error(f"保存用户消息失败: {str(e)}")

            # 执行原始方法
            return await execute_func(self, context)

        return wrapper
    return decorator


def auto_save_assistant_message(session_service_attr: str = "session_service") -> Callable[[WorkflowStepMethod], WorkflowStepMethod]:
    """
    自动保存助手消息的装饰器

    在工作流步骤执行后，如果设置了early_return且有响应内容，自动保存助手消息。

    Args:
        session_service_attr: 会话服务在步骤实例中的属性名，默认为"session_service"

    Usage:
        @auto_save_assistant_message()
        async def execute(self, context: WorkflowContext) -> WorkflowContext:
            # 业务逻辑
            context.response = "回复内容"
            context.early_return = True
            return context
    """
    def decorator(execute_func: WorkflowStepMethod) -> WorkflowStepMethod:
        @wraps(execute_func)
        async def wrapper(self: Any, context: WorkflowContext) -> WorkflowContext:
            logger: logging.Logger = logging.getLogger(f"{self.__class__.__name__}.auto_save_assistant_message")

            # 执行原始方法
            result_context: WorkflowContext = await execute_func(self, context)

            # 如果设置了early_return且有响应内容，保存助手消息
            if result_context.early_return and result_context.response:
                session_service: Optional[SessionService] = getattr(self, session_service_attr, None)
                if session_service is not None:
                    try:
                        await session_service.add_message(
                            session_id=result_context.session_id,
                            role="assistant",
                            content=result_context.response
                        )
                        logger.info(f"✅ {self.__class__.__name__}：已保存assistant消息到数据库")
                    except Exception as e:
                        logger.error(f"保存助手消息失败: {str(e)}")
                else:
                    logger.warning(f"未找到会话服务属性: {session_service_attr}")

            return result_context

        return wrapper
    return decorator


def auto_save_messages(
    save_user: bool = True,
    save_assistant: bool = True,
    session_service_attr: str = "session_service"
) -> Callable[[WorkflowStepMethod], WorkflowStepMethod]:
    """
    自动保存用户和助手消息的组合装饰器

    Args:
        save_user: 是否保存用户消息
        save_assistant: 是否保存助手消息
        session_service_attr: 会话服务在步骤实例中的属性名

    Usage:
        @auto_save_messages(save_user=True, save_assistant=True)
        async def execute(self, context: WorkflowContext) -> WorkflowContext:
            # 业务逻辑
            pass
    """
    def decorator(execute_func: WorkflowStepMethod) -> WorkflowStepMethod:
        # 应用装饰器链
        decorated_func: WorkflowStepMethod = execute_func

        if save_assistant:
            decorated_func = auto_save_assistant_message(session_service_attr)(decorated_func)

        if save_user:
            decorated_func = auto_save_user_message(session_service_attr)(decorated_func)

        return decorated_func

    return decorator


class MessageSavingMixin:
    """
    消息保存混入类

    为工作流步骤提供便捷的消息保存方法。
    """

    # 类型注解：期望子类有这些属性
    session_service: SessionService
    logger: logging.Logger

    async def save_user_message(self, context: WorkflowContext) -> None:
        """
        保存用户消息

        Args:
            context: 工作流上下文
        """
        session_service: Optional[SessionService] = getattr(self, 'session_service', None)
        if session_service is not None:
            try:
                await session_service.add_message(
                    session_id=context.session_id,
                    role="user",
                    content=context.user_message,
                    intent=context.intent,
                    extracted_params=context.extracted_params
                )
                setattr(context, '_user_message_saved', True)

                logger: Optional[logging.Logger] = getattr(self, 'logger', None)
                if logger is not None:
                    logger.info(f"✅ {self.__class__.__name__}：已保存user消息到数据库")
            except Exception as e:
                logger = getattr(self, 'logger', None)
                if logger is not None:
                    logger.error(f"保存用户消息失败: {str(e)}")

    async def save_assistant_message(self, context: WorkflowContext) -> None:
        """
        保存助手消息

        Args:
            context: 工作流上下文
        """
        session_service: Optional[SessionService] = getattr(self, 'session_service', None)
        if session_service is not None and context.response:
            try:
                await session_service.add_message(
                    session_id=context.session_id,
                    role="assistant",
                    content=context.response
                )

                logger: Optional[logging.Logger] = getattr(self, 'logger', None)
                if logger is not None:
                    logger.info(f"✅ {self.__class__.__name__}：已保存assistant消息到数据库")
            except Exception as e:
                logger = getattr(self, 'logger', None)
                if logger is not None:
                    logger.error(f"保存助手消息失败: {str(e)}")
