"""
聊天工作流模块

提供聊天相关的工作流实现。
使用模块化的步骤和结构化的上下文对象，减少重复代码，提高可读性和可维护性。
"""

from typing import Dict, Any, Union

from app.workflows.base import Workflow
from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService
from app.llm.intent import IntentRecognizer
from app.llm.extraction import ParameterExtractor

from app.llm.analysis import ParameterAnalyzer
from app.llm.response import ResponseGenerator
from app.services.house_service import HouseService
# 导入工作流步骤
from app.workflows.steps import (
    # 会话相关步骤
    GetSessionStep,
    SaveUserMessageStep,
    UpdateContextStep,
    SaveAssistantMessageStep,

    # 意图相关步骤
    RecognizeIntentStep,
    ProcessViewingAppointmentStep,
    ProcessCommunityInquiryStep,

    # 参数分析相关步骤
    AnalyzeParametersStep,
    CheckMissingParamsStep,

    # 参数提取相关步骤
    ExtractParametersStep,
    ProcessLocationStep,

    # 房源相关步骤
    SearchHouseStep,

    # 响应相关步骤
    AskParameterStep,
    GenerateResponseStep,
)


class ChatWorkflow(Workflow):
    """
    聊天工作流

    处理用户消息的主要工作流，包括：
    1. 获取会话信息
    2. 识别用户意图
    3. 分析和提取参数
    4. 保存用户消息和更新上下文
    5. 检查缺失参数并询问
    6. 搜索房源
    7. 生成响应
    8. 保存助手消息

    使用统一的步骤和条件执行逻辑，减少重复代码。
    """

    def __init__(self,
                 session_service: SessionService,
                 intent_recognizer: IntentRecognizer,
                 parameter_analyzer: ParameterAnalyzer,
                 parameter_extractor: ParameterExtractor,
                 response_generator: ResponseGenerator,
                 house_service: HouseService):
        """
        初始化聊天工作流

        Args:
            session_service: 会话服务，用于管理会话状态和历史记录
            intent_recognizer: 意图识别器，用于识别用户意图
            parameter_analyzer: 参数分析器，用于分析用户输入中的参数类型
            parameter_extractor: 参数提取器，用于从用户输入中提取参数
            response_generator: 响应生成器，用于生成回复
            house_service: 房源服务，用于搜索房源
        """
        # 创建工作流步骤，按照处理流程的逻辑顺序排列
        steps = [
            # 第一阶段：会话准备
            GetSessionStep(session_service),

            # 第二阶段：理解用户意图和参数
            RecognizeIntentStep(intent_recognizer, session_service),
            AnalyzeParametersStep(parameter_analyzer),
            ExtractParametersStep(parameter_extractor),
            ProcessLocationStep(session_service),  # 处理位置参数
            
            # 第三阶段：处理特殊意图（在参数提取后执行）
            ProcessCommunityInquiryStep(house_service, session_service),
            ProcessViewingAppointmentStep(parameter_extractor, session_service),
            # 第四阶段：保存用户消息和更新上下文
            SaveUserMessageStep(session_service),
            UpdateContextStep(session_service),

            # 第四阶段：检查参数完整性
            CheckMissingParamsStep(),

            # 第五阶段：参数收集或执行搜索（使用统一的参数询问步骤）
            AskParameterStep(session_service, "location"),  # 如果缺少位置参数，询问位置
            AskParameterStep(session_service, "budget"),    # 如果有位置但缺少预算，询问预算
            AskParameterStep(session_service, "room_type"), # 如果有位置和预算但缺少户型，询问户型
            
            SearchHouseStep(house_service, session_service),                # 如果参数完整，搜索房源

            # 第六阶段：生成响应（使用统一的响应生成步骤）
            GenerateResponseStep(response_generator, house_service, session_service),

            # 第七阶段：保存助手消息
            SaveAssistantMessageStep(session_service)
        ]

        super().__init__(steps, name="ChatWorkflow")



