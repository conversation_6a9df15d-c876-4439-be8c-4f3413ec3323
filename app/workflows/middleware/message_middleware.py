"""
消息保存中间件模块

提供自动保存用户消息和助手消息的中间件，避免在每个工作流步骤中重复编写消息保存逻辑。
"""

import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any

from app.workflows.context import WorkflowContext
from app.services.session_service import SessionService


class MessageMiddleware(ABC):
    """
    消息中间件基类
    
    提供在工作流步骤执行前后自动保存消息的功能。
    """
    
    def __init__(self, session_service: SessionService):
        """
        初始化消息中间件
        
        Args:
            session_service: 会话服务，用于保存消息
        """
        self.session_service = session_service
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def before_execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        在步骤执行前调用
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        pass
    
    @abstractmethod
    async def after_execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        在步骤执行后调用
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        pass


class UserMessageMiddleware(MessageMiddleware):
    """
    用户消息保存中间件
    
    在工作流步骤执行前自动保存用户消息，确保即使步骤提前返回也能保存用户消息。
    """
    
    def __init__(self, session_service: SessionService, save_position: str = "before"):
        """
        初始化用户消息中间件
        
        Args:
            session_service: 会话服务
            save_position: 保存位置，"before"表示在步骤执行前保存，"after"表示在步骤执行后保存
        """
        super().__init__(session_service)
        self.save_position = save_position
        self._user_message_saved = False  # 标记是否已保存用户消息
    
    async def before_execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        在步骤执行前保存用户消息
        """
        if self.save_position == "before" and not self._user_message_saved:
            await self._save_user_message(context)
        return context
    
    async def after_execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        在步骤执行后保存用户消息
        """
        if self.save_position == "after" and not self._user_message_saved:
            await self._save_user_message(context)
        return context
    
    async def _save_user_message(self, context: WorkflowContext) -> None:
        """
        保存用户消息到数据库
        """
        try:
            await self.session_service.add_message(
                session_id=context.session_id,
                role="user",
                content=context.user_message,
                intent=context.intent,
                extracted_params=context.extracted_params
            )
            self._user_message_saved = True
            self.logger.info(f"✅ UserMessageMiddleware：已保存user消息到数据库")
        except Exception as e:
            self.logger.error(f"保存用户消息失败: {str(e)}")


class AssistantMessageMiddleware(MessageMiddleware):
    """
    助手消息保存中间件
    
    在工作流步骤执行后自动保存助手消息。
    """
    
    async def before_execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        在步骤执行前不做任何操作
        """
        return context
    
    async def after_execute(self, context: WorkflowContext) -> WorkflowContext:
        """
        在步骤执行后保存助手消息
        """
        if context.response and context.early_return:
            await self._save_assistant_message(context)
        return context
    
    async def _save_assistant_message(self, context: WorkflowContext) -> None:
        """
        保存助手消息到数据库
        """
        try:
            await self.session_service.add_message(
                session_id=context.session_id,
                role="assistant",
                content=context.response
            )
            self.logger.info(f"✅ AssistantMessageMiddleware：已保存assistant消息到数据库")
        except Exception as e:
            self.logger.error(f"保存助手消息失败: {str(e)}")


class MessageSavingWorkflowStep:
    """
    支持消息保存中间件的工作流步骤混入类
    
    为工作流步骤提供自动消息保存功能。
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.middlewares: list[MessageMiddleware] = []
    
    def add_middleware(self, middleware: MessageMiddleware) -> None:
        """
        添加中间件
        
        Args:
            middleware: 消息中间件实例
        """
        self.middlewares.append(middleware)
    
    async def execute_with_middleware(self, context: WorkflowContext) -> WorkflowContext:
        """
        带中间件的执行方法
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        # 执行前置中间件
        for middleware in self.middlewares:
            context = await middleware.before_execute(context)
        
        # 执行原始逻辑
        context = await self.execute_business_logic(context)
        
        # 执行后置中间件
        for middleware in self.middlewares:
            context = await middleware.after_execute(context)
        
        return context
    
    @abstractmethod
    async def execute_business_logic(self, context: WorkflowContext) -> WorkflowContext:
        """
        执行业务逻辑
        
        子类需要实现此方法，将原来的execute逻辑移到这里。
        
        Args:
            context: 工作流上下文
            
        Returns:
            更新后的上下文
        """
        pass
