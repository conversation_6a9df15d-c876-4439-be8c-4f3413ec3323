"""
工作流常量模块

定义工作流中使用的常量，避免重复定义。
"""

from enum import Enum
from typing import List


class Intent(str, Enum):
    """意图枚举"""
    SEARCH_HOUSE = "search_house"                    # 搜索房源
    CONTINUE_SEARCH = "continue_search"              # 继续搜索
    COMMUNITY_INQUIRY = "community_inquiry"          # 小区咨询
    VIEWING_APPOINTMENT = "viewing_appointment"      # 预约看房
    CHITCHAT = "chitchat"                           # 闲聊
    ZIROOM_BUSINESS_INQUIRY = "ziroom_business_inquiry"  # 自如业务咨询
    RENTAL_POLICY_INQUIRY = "rental_policy_inquiry"     # 租房政策咨询
    COMPLAINT = "complaint"                          # 投诉
    PROHIBITED_CONTENT = "prohibited_content"        # 违禁内容


class IntentGroup:
    """意图分组"""
    
    # 找房相关意图
    HOUSING_RELATED: List[str] = [
        Intent.SEARCH_HOUSE,
        Intent.CONTINUE_SEARCH,
        Intent.COMMUNITY_INQUIRY
    ]
    
    # 支持的意图（需要特殊处理的意图）
    SUPPORTED: List[str] = [
        Intent.SEARCH_HOUSE,
        Intent.CONTINUE_SEARCH,
        Intent.COMMUNITY_INQUIRY,
        Intent.VIEWING_APPOINTMENT,
        Intent.CHITCHAT
    ]
    
    # 不支持的意图（需要返回提示信息的意图）
    UNSUPPORTED: List[str] = [
        Intent.ZIROOM_BUSINESS_INQUIRY,
        Intent.RENTAL_POLICY_INQUIRY,
        Intent.COMPLAINT,
        Intent.PROHIBITED_CONTENT
    ]


class SessionState(str, Enum):
    """会话状态枚举"""
    IDLE = "idle"                                   # 空闲状态
    PROCESSING = "processing"                       # 处理中
    BROWSING = "browsing"                          # 浏览房源
    CHITCHAT = "chitchat"                          # 闲聊状态
    COMMUNITY_INQUIRY = "community_inquiry"         # 小区咨询
    VIEWING_APPOINTMENT = "viewing_appointment"     # 预约看房
    UNSUPPORTED_INTENT = "unsupported_intent"      # 不支持的意图


class ParameterType(str, Enum):
    """参数类型枚举"""
    LOCATION = "location"                          # 位置
    BUDGET = "budget"                              # 预算
    ROOM_TYPE = "room_type"                        # 户型
    BED_ROOM = "bed_room"                          # 卧室数量
    LOCATION_TYPE = "location_type"                # 位置类型
    VIEWING_TIME = "viewing_time"                  # 看房时间


class RequiredParameters:
    """必需参数定义"""
    
    # 搜索房源必需的参数
    HOUSE_SEARCH: List[str] = [
        ParameterType.LOCATION,
        ParameterType.BUDGET,
        ParameterType.ROOM_TYPE
    ]
    
    # 预约看房必需的参数
    VIEWING_APPOINTMENT: List[str] = [
        ParameterType.VIEWING_TIME
    ]
