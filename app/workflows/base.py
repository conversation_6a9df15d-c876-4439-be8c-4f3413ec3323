"""
工作流基础模块

提供工作流和工作流步骤的基类，用于构建可组合的业务流程。
简化版本使用结构化的上下文对象，提高代码可读性和可维护性。
"""

from abc import ABC, abstractmethod
import logging
from typing import Dict, Any, List, Optional, Union, TypeVar

from app.workflows.context import WorkflowContext

# 定义类型变量，用于泛型类型提示
T = TypeVar('T', bound=WorkflowContext)


class WorkflowStep(ABC):
    """
    工作流步骤基类

    每个步骤负责处理工作流中的一个特定任务，接收上下文并返回更新后的上下文。
    步骤可以通过重写should_execute方法来决定是否应该执行。
    """

    def __init__(self, name: Optional[str] = None):
        """
        初始化工作流步骤

        Args:
            name: 步骤名称，默认为类名
        """
        self.name = name or self.__class__.__name__
        self.logger = logging.getLogger(self.name)

    @abstractmethod
    async def execute(self, context: Union[Dict[str, Any], WorkflowContext]) -> Union[Dict[str, Any], WorkflowContext]:
        """
        执行步骤，处理上下文并返回更新后的上下文

        Args:
            context: 工作流上下文，可以是字典或WorkflowContext对象

        Returns:
            更新后的上下文
        """
        pass

    def should_execute(self, context: Union[Dict[str, Any], WorkflowContext]) -> bool:
        """
        判断是否应该执行此步骤

        默认总是执行，子类可以重写此方法以提供条件判断。

        Args:
            context: 工作流上下文

        Returns:
            如果应该执行此步骤，则返回True；否则返回False
        """
        return True


class Workflow:
    """
    工作流基类

    工作流由一系列步骤组成，按顺序执行这些步骤来完成特定的业务流程。
    每个步骤可以选择性地执行，取决于其should_execute方法的返回值。
    """

    def __init__(self, steps: List[WorkflowStep], name: Optional[str] = None):
        """
        初始化工作流

        Args:
            steps: 工作流步骤列表
            name: 工作流名称，默认为类名
        """
        self.steps = steps
        self.name = name or self.__class__.__name__
        self.logger = logging.getLogger(self.name)

    async def execute(self, initial_context: Union[Dict[str, Any], WorkflowContext]) -> Union[Dict[str, Any], WorkflowContext]:
        """
        执行工作流中的所有步骤

        按顺序执行满足执行条件的步骤，跳过不满足条件的步骤。

        Args:
            initial_context: 初始上下文，可以是字典或WorkflowContext对象

        Returns:
            最终上下文
        """
        # 复制上下文以避免修改原始对象
        if isinstance(initial_context, dict):
            context = initial_context.copy()
        else:
            # 如果是WorkflowContext对象，创建一个新的实例
            context = initial_context

        self.logger.info(f"开始执行工作流: {self.name}")

        # 执行每个步骤
        for i, step in enumerate(self.steps):
            # 检查是否应该执行此步骤
            if not step.should_execute(context):
                self.logger.debug(f"跳过步骤 {i+1}/{len(self.steps)}: {step.name}")
                continue

            self.logger.debug(f"执行步骤 {i+1}/{len(self.steps)}: {step.name}")

            try:
                # 执行步骤并更新上下文
                context = await step.execute(context)

                # 检查是否需要提前结束工作流
                if isinstance(context, WorkflowContext) and context.early_return:
                    self.logger.info(f"工作流提前结束: {self.name}")
                    break

            except Exception as e:
                self.logger.error(f"步骤 {step.name} 执行出错: {str(e)}")
                raise

        self.logger.info(f"工作流执行完成: {self.name}")
        return context
