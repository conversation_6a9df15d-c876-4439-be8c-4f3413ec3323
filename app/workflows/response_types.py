"""
工作流响应类型模块

定义统一的工作流响应类型体系，消除响应生成逻辑中的复杂判断。
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass


class ResponseType(str, Enum):
    """响应类型枚举"""
    TEXT = "text"                           # 纯文本响应
    HOUSE_LIST = "house_list"              # 房源列表响应
    LOCATION_SUGGESTION = "location_suggestion"  # 位置建议响应
    VIEWING_APPOINTMENT = "viewing_appointment"  # 看房单响应
    BIZCIRCLE_RECOMMENDATION = "bizcircle_recommendation"  # 商圈推荐响应


class ResponseFormat(str, Enum):
    """响应格式枚举"""
    PLAIN_TEXT = "plain_text"              # 纯文本格式
    JSON_DATA = "json_data"                # JSON数据格式
    MARKDOWN = "markdown"                  # Markdown格式


@dataclass
class ResponseMetadata:
    """响应元数据"""
    session_state: Optional[str] = None     # 会话状态
    location_type: Optional[str] = None     # 位置类型
    parent_type: Optional[str] = None       # SSE父类型
    delay: float = 0.05                     # 打字机效果延迟
    requires_typewriter: bool = True        # 是否需要打字机效果


@dataclass
class WorkflowResponse:
    """
    统一的工作流响应类型
    
    所有工作流步骤都应该返回此类型，而不是直接设置 context.response
    """
    response_type: ResponseType             # 响应类型
    format: ResponseFormat                  # 响应格式
    content: Union[str, Dict[str, Any], List[Dict[str, Any]]]  # 响应内容
    metadata: ResponseMetadata              # 响应元数据
    
    def __post_init__(self):
        """初始化后处理"""
        # 根据响应类型自动设置默认元数据
        if not self.metadata.parent_type:
            self.metadata.parent_type = self._get_default_parent_type()
    
    def _get_default_parent_type(self) -> str:
        """根据响应类型获取默认的SSE父类型"""
        type_mapping = {
            ResponseType.TEXT: "requirement_communication",
            ResponseType.HOUSE_LIST: None,  # 需要根据location_type动态确定
            ResponseType.LOCATION_SUGGESTION: "location_suggestion",
            ResponseType.VIEWING_APPOINTMENT: "view_house",
            ResponseType.BIZCIRCLE_RECOMMENDATION: "borough"
        }
        return type_mapping.get(self.response_type, "requirement_communication")
    
    def is_json_format(self) -> bool:
        """判断是否为JSON格式"""
        return self.format == ResponseFormat.JSON_DATA
    
    def is_text_format(self) -> bool:
        """判断是否为文本格式"""
        return self.format in [ResponseFormat.PLAIN_TEXT, ResponseFormat.MARKDOWN]
    
    def get_sse_parent_type(self, location_type: Optional[str] = None) -> str:
        """
        获取SSE响应的父类型

        Args:
            location_type: 位置类型，用于房源列表响应

        Returns:
            SSE父类型
        """
        if self.response_type == ResponseType.HOUSE_LIST and location_type:
            # 房源列表需要根据位置类型动态确定父类型
            try:
                from app.sse.constants import MappingRegistry
                return MappingRegistry.get_parent_type_by_location_type(location_type)
            except ImportError:
                # 如果导入失败，使用默认值
                return "requirement_communication"

        return self.metadata.parent_type or "requirement_communication"


# 便捷的响应构造函数

def create_text_response(
    content: str,
    session_state: Optional[str] = None,
    delay: float = 0.05,
    requires_typewriter: bool = True
) -> WorkflowResponse:
    """创建文本响应"""
    return WorkflowResponse(
        response_type=ResponseType.TEXT,
        format=ResponseFormat.PLAIN_TEXT,
        content=content,
        metadata=ResponseMetadata(
            session_state=session_state,
            delay=delay,
            requires_typewriter=requires_typewriter
        )
    )


def create_house_list_response(
    houses_data: Dict[str, Any],
    location_type: str,
    session_state: str = "browsing"
) -> WorkflowResponse:
    """创建房源列表响应"""
    return WorkflowResponse(
        response_type=ResponseType.HOUSE_LIST,
        format=ResponseFormat.JSON_DATA,
        content=houses_data,
        metadata=ResponseMetadata(
            session_state=session_state,
            location_type=location_type,
            delay=0.1,
            requires_typewriter=False
        )
    )


def create_location_suggestion_response(
    suggestions_data: Dict[str, Any]
) -> WorkflowResponse:
    """创建位置建议响应"""
    return WorkflowResponse(
        response_type=ResponseType.LOCATION_SUGGESTION,
        format=ResponseFormat.JSON_DATA,
        content=suggestions_data,
        metadata=ResponseMetadata(
            delay=0.1,
            requires_typewriter=False
        )
    )


def create_viewing_appointment_response(
    appointment_data: Dict[str, Any]
) -> WorkflowResponse:
    """创建看房单响应"""
    return WorkflowResponse(
        response_type=ResponseType.VIEWING_APPOINTMENT,
        format=ResponseFormat.JSON_DATA,
        content=appointment_data,
        metadata=ResponseMetadata(
            delay=0.05,
            requires_typewriter=False
        )
    )


def create_bizcircle_recommendation_response(
    bizcircle_data: Dict[str, Any]
) -> WorkflowResponse:
    """创建商圈推荐响应"""
    return WorkflowResponse(
        response_type=ResponseType.BIZCIRCLE_RECOMMENDATION,
        format=ResponseFormat.MARKDOWN,
        content=bizcircle_data,  # 原始数据，由SSE生成器转换为Markdown
        metadata=ResponseMetadata(
            location_type="行政区",
            delay=0.05,
            requires_typewriter=True
        )
    )
