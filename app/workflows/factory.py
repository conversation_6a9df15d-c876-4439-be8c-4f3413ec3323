"""
工作流工厂模块

提供创建和配置工作流的工厂类。
精简版本使用统一的工作流和结构化的上下文对象，减少重复代码，提高可读性和可维护性。
"""

from typing import Optional, Dict, Any, Union

from sqlalchemy.ext.asyncio import AsyncSession

from app.llm.intent import IntentRecognizer
from app.llm.extraction import ParameterExtractor

from app.llm.analysis import ParameterAnalyzer
from app.llm.response import ResponseGenerator
from app.services.session_service import SessionService
from app.services.house_service import HouseService
from app.workflows.chat_workflow import ChatWorkflow
from app.workflows.context import WorkflowContext


class WorkflowFactory:
    """
    工作流工厂类

    负责创建和缓存工作流实例，避免重复创建相同的实例。
    提供统一的接口来处理用户消息，选择合适的工作流并执行。
    使用结构化的上下文对象来提高代码可读性和可维护性。
    """

    # 工作流缓存
    _chat_workflow_cache = {}

    def create_chat_workflow(self, db: AsyncSession, session_service: Optional[SessionService] = None) -> ChatWorkflow:
        """
        创建聊天工作流

        如果已经为当前数据库会话创建了工作流，则返回缓存的实例。
        否则，创建新的工作流实例并缓存它。

        Args:
            db: 数据库会话
            session_service: 会话服务实例，如果为None则创建新的实例

        Returns:
            配置好的聊天工作流
        """
        # 检查缓存
        cache_key = (id(db), id(session_service) if session_service else None)
        if cache_key in self._chat_workflow_cache:
            return self._chat_workflow_cache[cache_key]

        # 创建服务和组件
        if session_service is None:
            session_service = SessionService(db)

        intent_recognizer = IntentRecognizer()
        parameter_analyzer = ParameterAnalyzer()
        parameter_extractor = ParameterExtractor()
        response_generator = ResponseGenerator()
        house_service = HouseService(session_service)

        # 创建工作流
        workflow = ChatWorkflow(
            session_service=session_service,
            intent_recognizer=intent_recognizer,
            parameter_analyzer=parameter_analyzer,
            parameter_extractor=parameter_extractor,
            response_generator=response_generator,
            house_service=house_service
        )

        # 缓存工作流
        self._chat_workflow_cache[cache_key] = workflow

        return workflow



    async def process_message(self, db: AsyncSession, session_id: str,
                             user_message: str,
                             session_service: Optional[SessionService] = None,
                             token: str = "") -> Dict[str, Any]:
        """
        处理用户消息

        使用工作流处理用户消息，创建结构化上下文并执行工作流。

        Args:
            db: 数据库会话
            session_id: 会话ID
            user_message: 用户消息
            session_service: 会话服务实例，如果为None则创建新的实例
            token: 用户认证token

        Returns:
            处理结果上下文（字典格式）
        """
        # 创建结构化上下文
        context = WorkflowContext(
            session_id=session_id,
            user_message=user_message,
            token=token
        )

        # 设置会话服务
        context.session_service = session_service

        # 创建聊天工作流
        workflow = self.create_chat_workflow(db, session_service)

        # 执行工作流
        result_context = await workflow.execute(context)

        # 将结构化上下文转换为字典返回
        if isinstance(result_context, WorkflowContext):
            return result_context.to_response_dict()
        else:
            # 兼容处理，如果返回的不是结构化上下文
            return result_context
