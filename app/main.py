import logging
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.middleware import DatabaseConnectionMiddleware, RequestLoggingMiddleware
from app.api import chat, session
from app.db.base import Base, engine

# 设置日志
logger = setup_logging()

# 创建数据库表
def create_tables():
    Base.metadata.create_all(bind=engine)

# 创建应用
app = FastAPI(
    title=settings.APP_NAME,
    description="AI找房系统API - 提供聊天、会话管理和房源搜索功能",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加数据库连接监控中间件
app.add_middleware(DatabaseConnectionMiddleware)

# 添加请求日志中间件（仅在调试模式下）
if settings.DEBUG:
    app.add_middleware(RequestLoggingMiddleware)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 设置模板
templates = Jinja2Templates(directory="templates")

# 注册路由
app.include_router(chat.router)
app.include_router(session.router)

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"code": "500", "message": "系统异常"}
    )

# 根路由
@app.get("/", summary="首页", description="返回应用首页")
async def root(request: Request):
    """
    应用首页

    返回AI找房系统的Web界面首页。

    **返回**:
    - HTML页面
    """
    return templates.TemplateResponse(
        "index.html",
        {"request": request, "title": settings.APP_NAME}
    )

# 健康检查
@app.get("/health")
async def health():
    return {"status": "ok"}

# 数据库连接池健康检查
@app.get("/health/db")
async def health_db():
    """数据库连接池健康检查"""
    try:
        from app.db.base import check_pool_health
        pool_status = await check_pool_health()
        
        # 计算健康状态
        pool_size = pool_status["pool_size"]
        checked_out = pool_status["checked_out"]
        usage_rate = pool_status["usage_rate"]
        
        status = "healthy"
        if usage_rate > 0.8:
            status = "warning"
        if usage_rate > 0.95:
            status = "critical"
        
        return {
            "status": status,
            "pool_status": pool_status,
            "usage_rate": f"{usage_rate:.2%}",
            "message": f"连接池使用率: {usage_rate:.2%}"
        }
    except Exception as e:
        logger.error(f"数据库健康检查失败: {str(e)}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}"
        }

# 版本信息
@app.get("/version")
async def version():
    return {
        "app_name": settings.APP_NAME,
        "version": "0.1.0",
        "intent_prompt_version": settings.INTENT_PROMPT_VERSION,
        "extraction_prompt_version": settings.EXTRACTION_PROMPT_VERSION,
        "response_prompt_version": settings.RESPONSE_PROMPT_VERSION,
    }

# 启动事件
@app.on_event("startup")
async def startup_event():
    logger.info("Starting up application")
    create_tables()

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down application")

# 主函数
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
