import logging
from typing import Dict, Any, Tuple, List
import json

from app.llm.client import generate_completion
from app.llm.prompts.intent import get_intent_prompt
from app.core.config import settings

logger = logging.getLogger(__name__)

class IntentRecognizer:
    """意图识别器"""

    INTENT_MAPPING = {
        "1": "search_house",        # 找房需求
        "2": "viewing_appointment", # 预约看房
        "3": "ziroom_business_inquiry",    # 自如相关业务咨询
        "4": "rental_policy_inquiry",      # 租房/买卖政策相关
        "5": "complaint",           # 投诉反馈
        "6": "prohibited_content",  # 违禁词
        "7": "chitchat",            # 闲聊问答
        "8": "continue_search",     # 继续搜索
        "9": "community_inquiry",   # 小区咨询
    }

    @staticmethod
    def _simplify_conversation_history(conversation_history: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        为意图识别精简对话历史，只保留用户消息和assistant的简短摘要
        
        Args:
            conversation_history: 原始对话历史
            
        Returns:
            精简后的对话历史
        """
        simplified_history = []
        
        for message in conversation_history:
            role = message.get("role", "")
            content = message.get("content", "")
            
            if role == "user":
                # 用户消息保持原样
                simplified_history.append(message)
            elif role == "assistant":
                # 检查是否是JSON房源数据
                try:
                    json_data = json.loads(content)
                    if isinstance(json_data, dict) and "title" in json_data and "data" in json_data:
                        # 这是房源数据，使用极简且中性的摘要，避免LLM角色混淆
                        # 不使用"已为您推荐X套房源"这样的表述，因为会误导LLM
                        simplified_content = "系统已处理搜索请求"
                        simplified_history.append({
                            "role": "assistant",
                            "content": simplified_content
                        })
                    else:
                        # 其他JSON数据，保持原样
                        simplified_history.append(message)
                except (json.JSONDecodeError, TypeError):
                    # 不是JSON数据，检查长度
                    if len(content) > 200:
                        # 内容过长，截取前100字符
                        simplified_content = content[:100] + "..."
                        simplified_history.append({
                            "role": "assistant", 
                            "content": simplified_content
                        })
                    else:
                        # 内容适中，保持原样
                        simplified_history.append(message)
        
        return simplified_history



    @staticmethod
    async def recognize_intent(
            user_input: str,
        conversation_history: List[Dict[str, str]],
        prompt_version: str = None
    ) -> Tuple[str, float]:
        """
        识别用户意图

        Args:
            user_input: 用户输入
            conversation_history: 对话历史
            prompt_version: 提示模板版本

        Returns:
            意图和置信度的元组
        """
        # 如果未指定版本，使用配置中的版本
        if prompt_version is None:
            prompt_version = settings.INTENT_PROMPT_VERSION

        try:
            # 获取提示模板
            prompt = get_intent_prompt(prompt_version)

            # 准备消息
            messages = [
                {"role": "system", "content": prompt["system_prompt"]},
            ]

            # 精简对话历史并添加
            if conversation_history:
                simplified_history = IntentRecognizer._simplify_conversation_history(conversation_history)
                messages.extend(simplified_history[-3:])  # 只使用最近3轮对话
                
                # 记录精简效果
                original_length = sum(len(msg.get("content", "")) for msg in conversation_history[-3:])
                simplified_length = sum(len(msg.get("content", "")) for msg in simplified_history[-3:])
                logger.info(f"对话历史精简: {original_length} -> {simplified_length} 字符")
                
                # 添加详细调试日志
                logger.info(f"原始对话历史最近3轮: {conversation_history[-3:]}")
                logger.info(f"精简后对话历史: {simplified_history[-3:]}")

            # 添加用户输入
            messages.append({"role": "user", "content": user_input})

            # 添加强制约束消息，确保只返回数字
            messages.append({
                "role": "system", 
                "content": "请严格按照指令，只返回1-9之间的一个数字，不要返回任何其他内容。您是意图识别器，不是房源推荐系统。"
            })
            
            # 记录完整的消息内容用于调试
            logger.info(f"发送给LLM的完整消息: {messages}")

            # 调用LLM
            response = await generate_completion(
                messages=messages,
                temperature=0.1,  # 低温度以获得更确定的结果
            )

            # 解析响应
            # 尝试两种方式访问响应内容，兼容不同版本的 OpenAI API
            try:
                # 对象方式访问（旧版本 API）
                if hasattr(response, 'choices') and len(response.choices) > 0:
                    intent_number = response.choices[0].message.content.strip()
                # 字典方式访问（新版本 API）
                elif isinstance(response, dict) and 'choices' in response:
                    intent_number = response['choices'][0]['message']['content'].strip()
                else:
                    raise ValueError("Unexpected response format")
            except Exception as e:
                logger.error(f"Error parsing response: {str(e)}, response type: {type(response)}")
                intent_number = ""

            # 验证响应是否为数字1-9
            if intent_number in ["1", "2", "3", "4", "5", "6", "7", "8", "9"]:
                intent = IntentRecognizer.INTENT_MAPPING.get(intent_number, "chitchat")
                logger.info(f"Recognized intent: {intent} (number: {intent_number})")
                return intent, 1.0
            else:
                logger.warning(f"Invalid intent number: {intent_number}, defaulting to chitchat")
                return "chitchat", 0.5

        except Exception as e:
            logger.error(f"Error recognizing intent: {str(e)}")
            return "chitchat", 0.0
