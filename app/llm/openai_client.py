import logging
from typing import Dict, List, Any, Optional, Union

import openai
from openai import AsyncOpenAI

from app.core.config import settings

# 配置OpenAI API密钥
openai.api_key = settings.OPENAI_API_KEY

# 创建异步客户端
async_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

# 获取日志记录器
logger = logging.getLogger(__name__)

async def generate_chat_completion(
    messages: List[Dict[str, str]],
    model: str = settings.OPENAI_MODEL,
    temperature: float = 0.7,
    max_tokens: Optional[int] = None,
    functions: Optional[List[Dict[str, Any]]] = None,
    function_call: Optional[Union[str, Dict[str, str]]] = None,
) -> Dict[str, Any]:
    """
    生成聊天完成
    
    Args:
        messages: 消息列表
        model: 模型名称
        temperature: 温度参数
        max_tokens: 最大令牌数
        functions: 函数定义列表
        function_call: 函数调用指令
        
    Returns:
        OpenAI API响应
    """
    try:
        # 准备请求参数
        params = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
        }
        
        # 添加可选参数
        if max_tokens:
            params["max_tokens"] = max_tokens
        if functions:
            params["functions"] = functions
        if function_call:
            params["function_call"] = function_call
            
        # 发送请求
        logger.debug(f"Sending request to OpenAI: {params}")
        response = await async_client.chat.completions.create(**params)
        logger.debug(f"Received response from OpenAI: {response}")
        
        # 返回响应
        return response
    
    except Exception as e:
        logger.error(f"Error calling OpenAI API: {str(e)}")
        raise
