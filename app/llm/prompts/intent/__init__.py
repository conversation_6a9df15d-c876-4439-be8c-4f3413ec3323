from enum import Enum
from typing import Dict, Any

# 当前使用的版本
CURRENT_VERSION = "v1"

class IntentPromptVersion(str, Enum):
    V1 = "v1"
    
# 导入所有版本
from app.llm.prompts.intent.v1 import INTENT_PROMPT as V1_INTENT_PROMPT

# 版本映射
INTENT_PROMPTS = {
    IntentPromptVersion.V1: V1_INTENT_PROMPT,
}

def get_intent_prompt(version: str = CURRENT_VERSION) -> Dict[str, Any]:
    """获取指定版本的意图识别提示"""
    return INTENT_PROMPTS.get(version, INTENT_PROMPTS[CURRENT_VERSION])
