"""
参数分析提示模板
"""

from typing import Dict, Any

from app.llm.prompts.analysis.v1 import ANALYSIS_PROMPT

# 提示模板映射
ANALYSIS_PROMPTS = {
    "v1": ANALYSIS_PROMPT
}

def get_analysis_prompt(version: str = "v1") -> Dict[str, Any]:
    """
    获取指定版本的参数分析提示模板
    
    Args:
        version: 提示模板版本
        
    Returns:
        提示模板
    """
    if version not in ANALYSIS_PROMPTS:
        raise ValueError(f"Invalid analysis prompt version: {version}")
    
    return ANALYSIS_PROMPTS[version]
