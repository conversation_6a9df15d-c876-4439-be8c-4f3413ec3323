"""
动态参数提取提示模板生成器
根据需要提取的参数类型动态生成提示模板
"""

import logging
from typing import Dict, Any, Set, List

from app.llm.prompts.extraction.v1 import EXTRACTION_PROMPT

logger = logging.getLogger(__name__)

# 参数定义映射
PARAMETER_DEFINITIONS = {
    "location": """<parameter name="location">
<rules>
<rule>优先：如果输入的文本格式是 "名称 - 类型 (详情)" 这种模式，提取第一个 " - " 前面的部分作为完整的核心区域名称。</rule>
<rule>核心区域提取：提取核心区域名称，去除位置修饰词，但保留完整的地名</rule>
<rule>完整地址单元保护：必须保留包含"小区"、"花园"、"公寓"、"家园"、"苑"、"城"、"大厦"、"广场"、"中心"、"村"、"庄"、"里"、"街"、"路"、"巷"、"胡同"等地址单元的完整名称</rule>
<rule>地址单元示例：
  - "花家地小区" → "花家地小区"（保留完整名称）
  - "回龙观东大街" → "回龙观东大街"（保留完整名称）
  - "望京花园" → "望京花园"（保留完整名称）
  - "清华大学" → "清华大学"（保留完整名称）
  - "国贸中心" → "国贸中心"（保留完整名称）
  - "三里屯SOHO" → "三里屯SOHO"（保留完整名称）
</rule>
<rule>修饰词过滤：去除"附近"、"周边"、"沿线"、"区域"、"地区"、"一带"、"周围"、"旁边"、"边上"、"范围内"、"左右"、"这边"、"那边"等修饰词，但不能破坏地址单元的完整性</rule>
<rule>地铁相关处理：
  - "地铁X号线沿线" → 提取"X号线"
  - "X号线附近" → 提取"X号线"  
  - "地铁X站附近" → 提取"X站"
  - "X站周边" → 提取"X站"
  - "到X站地铁3站" → 提取"X站"
</rule>
<rule>示例转换：
  - "花家地小区附近" → "花家地小区"（保留完整小区名）
  - "三里屯附近" → "三里屯"
  - "国贸周边" → "国贸"
  - "1号线沿线" → "1号线"
  - "东直门地铁站附近" → "东直门"
  - "中关村一带" → "中关村"
  - "望京区域" → "望京"
  - "朝阳门旁边" → "朝阳门"
  - "到百度大厦步行10分钟" → "百度大厦"（保留完整建筑名）
  - "地铁到国贸3站" → "国贸"
  - "清华园附近" → "清华园"（保留完整地名）
</rule>
<rule>保留完整名称：保留行政区划（如"朝阳区"、"海淀区"）和完整地标名称</rule>
<rule>若未提及位置，输出"null"</rule>
</rules>
</parameter>""",
    "budget": """<parameter name="budget">
<rules>
<rule>统一格式为"最小值,最大值"</rule>
<rule>转换单位：k=千，w=万（如5k=5000, 3w=30000）</rule>
<rule>处理范围表述（如"3-4k"→"3000,4000"，"2000-5000"→"2000,5000"，"3,8000"→"3000,8000"）</rule>
<rule>处理"X到Y"格式（如"3000到5000"→"3000,5000"，"3k到5k"→"3000,5000"）</rule>
<rule>处理"X至Y"格式（如"3000至5000"→"3000,5000"，"3k至5k"→"3000,5000"）</rule>
<rule>处理"X~Y"格式（如"3000~5000"→"3000,5000"，"3k~5k"→"3000,5000"）</rule>
<rule>处理"X-Y"格式（如"3000-5000"→"3000,5000"，"3k-5k"→"3000,5000"）</rule>
<rule>单一值转为上限区间（如5000→"0,5000"）</rule>
<rule>提取预算如"X左右", 则在X上下浮动1000元，（如"5000左右"→"4000,6000"）</rule>
<rule>如提供了准确数字, 如"4000", 则转化为不多于4000，（如 "4000"->"0,4000）</rule>
<rule>处理模糊表述（如"最多8k"→"0,8000"，"不超过5000"→"0,5000"，"5000以内"→"0,5000"）</rule>
<rule>处理下限表述（如"至少3000"→"3000,10000"，"3000以上"→"3000,10000"）</rule>
<rule>特别注意：当用户输入"3,8000"这样的格式时，应理解为"3000-8000"，即"3000,8000"</rule>
<rule>若未提及预算，输出"null"</rule>
<rule>仅当明确针对预算使用"价格不限"、"预算不限"、"钱不是问题"、"花多少都行"、"租金不限"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对预算，则不要输出"any"</rule>
</rules>
</parameter>""",

    "room_type": """<parameter name="room_type">
<rules>
<rule>仅区分"整租"与"合租"两种类型</rule>
<rule>若用户明确提到"整租"、"全租"、"独立公寓"、"单间"、"单独的房子"，输出"整租"</rule>
<rule>若用户明确提到"合租"、"分租"、"合住"、"共享公寓"，输出"合租"</rule>
<rule>若用户提到"单独租"、"独立"、"一个人住"、"自己住"、"独居"、"不和别人住"、"整套"、"整个房子"、"独立卫生间"、"独立厨房"、"独门独户"等表述，视为"整租"</rule>
<rule>若用户提到"和别人一起住"、"合住"、"分租"、"室友"、"合伙"、"拼房"、"拼租"、"共享"、"合用"、"合租房"、"床位"、"次卧"、"主卧"等表述，视为"合租"</rule>
<rule>若用户提到"单人间"、"独立房间"但没有明确表示是否与他人共享公共区域，需要根据上下文判断</rule>
<rule>若用户提到"一居"、"一室"、"单间"等，通常视为"整租"，除非上下文明确表示是合租</rule>
<rule>若用户提到"多居室中的一间"、"三居室中的主卧"等，视为"合租"</rule>
<rule>若无法确定用户意图，或用户未明确表达，输出"null"</rule>
<rule>若未提及租房类型，输出"null"</rule>
</rules>
</parameter>""",

    "area": """<parameter name="area">
<rules>
<rule>统一格式为"最小值,最大值"</rule>
<rule>处理范围表示（如"10-15平方米"→"10,15"）</rule>
<rule>处理上限表示（如"50平以内"、"小于50平"→"0,50"）</rule>
<rule>处理下限表示（如"40平以上"→"40,120"，"大于40平"→"40,120"）</rule>
<rule>处理具体数值（如"50平米"→"50,50"）</rule>
<rule>处理模糊表述（如"50平左右"→"45,55"）</rule>
<rule>若未提及面积，输出"null"</rule>
<rule>仅当明确针对面积使用"面积不限"、"大小无所谓"、"多大都行"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对面积，则不要输出"any"</rule>
</rules>
</parameter>""",

    "bed_room": """<parameter name="bed_room">
<rules>
<rule>提取居室数量，统一输出为数字：1, 2, 3, 4, 5</rule>
<rule>转换中文数字（如"一居"→"1"，"两居"→"2"，"三居"→"3"，"四居"→"4"，"五居"→"5"）</rule>
<rule>从"x室y厅"格式中提取x的值（如"两室一厅"→"2"，"三室两厅"→"3"，"一室一厅"→"1"）</rule>
<rule>识别"x居室"、"x居"、"x房"格式（如"3居室"→"3"，"2居"→"2"，"4房"→"4"）</rule>
<rule>识别"x卧"格式（如"两卧"→"2"，"三卧室"→"3"）</rule>
<rule>识别"单间"、"一间"、"独立房间"等表述为"1"</rule>
<rule>识别"两间"、"三间"等表述（如"两间房"→"2"）</rule>
<rule>识别"大户型"、"复式"、"跃层"、"loft"等特殊户型，根据上下文判断居室数量</rule>
<rule>识别"主卧"、"次卧"等表述，通常表示合租中的一个房间，不直接对应居室数量</rule>
<rule>若用户同时提到多个居室数量（如"2居或3居"），优先选择较小的数值</rule>
<rule>若未提及居室数量，输出"null"</rule>
<rule>仅当明确针对居室使用"居室不限"、"几室都行"、"不限几居"、"户型不限"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对居室，则不要输出"any"</rule>
</rules>
</parameter>""",

    "face": """<parameter name="face">
<rules>
<rule>识别标准朝向表述：东、南、西、北</rule>
<rule>将朝向转换为对应数字编码：东 1, 南 2, 西 3, 北 4</rule>
<rule>处理组合朝向，用逗号分隔（如"东南朝向"→"1,2"，"南北通透"→"2,4"，"东西向"→"1,3"）</rule>
<rule>识别含有"朝向"、"向"、"朝"、"面向"、"采光"、"通风"、"通透"等关键词的表达</rule>
<rule>识别"南向"、"朝南"、"向南"、"南朝向"等表述为"2"</rule>
<rule>识别"东向"、"朝东"、"向东"、"东朝向"等表述为"1"</rule>
<rule>识别"西向"、"朝西"、"向西"、"西朝向"等表述为"3"</rule>
<rule>识别"北向"、"朝北"、"向北"、"北朝向"等表述为"4"</rule>
<rule>识别"东南向"、"朝东南"、"向东南"、"东南朝向"等表述为"1,2"</rule>
<rule>识别"东北向"、"朝东北"、"向东北"、"东北朝向"等表述为"1,4"</rule>
<rule>识别"西南向"、"朝西南"、"向西南"、"西南朝向"等表述为"3,2"</rule>
<rule>识别"西北向"、"朝西北"、"向西北"、"西北朝向"等表述为"3,4"</rule>
<rule>识别"南北通透"、"南北向"、"南北朝向"等表述为"2,4"</rule>
<rule>识别"东西通透"、"东西向"、"东西朝向"等表述为"1,3"</rule>
<rule>识别"采光好"、"阳光充足"、"光线好"等表述，通常暗示南向，可输出"2"</rule>
<rule>若未提及朝向，输出"null"</rule>
<rule>仅当明确针对朝向使用"朝向不重要"、"朝向无所谓"、"朝哪都行"、"朝向不限"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对朝向，则不要输出"any"</rule>
</rules>
</parameter>""",

    "transport": """<parameter name="transport">
<rules>
<rule>提取用户提到的通勤方式</rule>
<rule>将"地铁"、"公交"、"公共交通"、"公交车"、"轨道交通"、"公交地铁"等映射为"transit"</rule>
<rule>将"步行"、"走路"、"走"、"徒步"、"走着去"等映射为"walk"</rule>
<rule>将"开车"、"驾车"、"自驾"、"汽车"、"私家车"、"打车"、"出租车"、"滴滴"等映射为"drive"</rule>
<rule>将"骑车"、"自行车"、"单车"、"电动车"、"共享单车"、"骑自行车"、"骑单车"、"骑电动车"等映射为"ride"</rule>
<rule>特别注意提取"到X地点Y分钟"、"Y分钟到X地点"、"骑车到X不超过Y分钟"等通勤表述中的通勤方式</rule>
<rule>如果用户提到"通勤"、"上班"、"下班"等词但没有明确交通方式，默认为"transit"</rule>
<rule>若未提及通勤方式，输出"null"</rule>
<rule>仅当明确针对通勤方式使用"交通方式不限"、"怎么去都行"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对通勤方式，则不要输出"any"</rule>
</rules>
</parameter>""",

    "distance": """<parameter name="distance">
<rules>
<rule>提取用户提到的距离数值，如"5公里内"、"3km以内"</rule>
<rule>统一格式为数字，不带单位，如"5"、"3"</rule>
<rule>若表述为"X公里内"、"X公里以内"、"小于X公里"，则提取X值</rule>
<rule>将"千米"、"km"、"公里"等单位统一转换处理</rule>
<rule>若表述为"X分钟车程"、"X分钟路程"，根据交通工具估算距离（步行约4km/h，驾车约30km/h）</rule>
<rule>若未提及距离，输出"null"</rule>
<rule>仅当明确针对距离使用"距离不限"、"多远都行"、"远近无所谓"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对距离，则不要输出"any"</rule>
</rules>
</parameter>""",

    "transport_minutes": """<parameter name="transport_minutes">
<rules>
<rule>提取用户提到的通勤时间，如"30分钟内"、"20分钟车程"、"不超过15分钟"</rule>
<rule>统一格式为数字，不带单位，如"30"、"20"</rule>
<rule>若表述为"X分钟内"、"X分钟以内"、"小于X分钟"、"不超过X分钟"、"少于X分钟"，则提取X值</rule>
<rule>处理"半小时"(30分钟)、"一刻钟"(15分钟)、"一小时"(60分钟)等表述</rule>
<rule>处理"X小时"格式，转换为分钟数(X*60)</rule>
<rule>特别注意提取"到X地点Y分钟"、"Y分钟到X地点"、"骑车到X不超过Y分钟"等通勤表述中的时间</rule>
<rule>若未提及通勤时间，输出"null"</rule>
<rule>仅当明确针对通勤时间使用"时间不限"、"多久都行"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对通勤时间，则不要输出"any"</rule>
</rules>
</parameter>""",

    "checkin_date": """<parameter name="checkin_date">
<rules>
<rule>提取用户提到的入住时间</rule>
<rule>尽量转换为具体日期格式：YYYY-MM-DD</rule>
<rule>处理相对时间表述，如"下周"、"下个月"、"明年"等，如果没表达具体年份，默认是今年2025年</rule>
<rule>若表述为"尽快"、"立即"、"马上"等，输出"immediate"</rule>
<rule>若未提及入住时间，输出"null"</rule>
<rule>仅当明确针对入住时间使用"时间不限"、"随时可以"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对入住时间，则不要输出"any"</rule>
</rules>
</parameter>""",

    # 新增的特色需求参数
    "layout": """<parameter name="layout">
<rules>
<rule>识别特色户型需求，主要包括独立卫生间和独立阳台</rule>
<rule>若用户提到"独立卫生间"、"带卫生间"、"独卫"、"主卧独卫"、"独立洗手间"、"私人卫生间"等，输出"1"（独立卫生间）</rule>
<rule>若用户提到"独立阳台"、"带阳台"、"私人阳台"、"阳台房间"、"有阳台的卧室"等，输出"2"（独立阳台）</rule>
<rule>若同时提到独立卫生间和独立阳台，用|分隔，如"1|2"</rule>
<rule>若未提及特色户型需求，输出"null"</rule>
</rules>
</parameter>""",

    "feature": """<parameter name="feature">
<rules>
<rule>识别房源特色需求，包括高层视野、起居室、智能家居、南北通透、loft等</rule>
<rule>若用户提到"高层"、"楼层高"、"视野好"、"不要低楼层"、"高一点的楼层"等，输出"71"（高层视野）</rule>
<rule>若用户提到"带起居室"、"套间"、"套房"、"带客厅的合租"、"起居室"等，输出"61"（带起居室）</rule>
<rule>若用户提到"智能家居"、"全屋智能"、"智能设备"、"智能化"等，输出"63"（全屋智能）</rule>
<rule>若用户提到"南北通透"、"采光好"、"通风好"、"全明户型"、"透气"等，输出"72"（南北通透）</rule>
<rule>若用户提到"loft"、"复式"、"跃层"、"挑高"等，输出"44"（loft）</rule>
<rule>若同时提到多个特色，用|分隔，如"72|63"</rule>
<rule>若未提及房源特色，输出"null"</rule>
</rules>
</parameter>""",

    "viewing_time": """<parameter name="viewing_time">
<rules>
<rule>提取用户提到的看房时间，适用于预约看房意图</rule>
<rule>识别具体时间表述：如"明天下午"、"周六上午"、"下周三"、"这个周末"</rule>
<rule>识别相对时间：如"明天"、"后天"、"下周"、"这周"、"周末"</rule>
<rule>识别时间段：如"上午"、"下午"、"晚上"、"中午"、"早上"</rule>
<rule>识别具体时点：如"10点"、"下午3点"、"晚上7点"、"上午9点半"</rule>
<rule>识别工作日/周末：如"工作日"、"周末"、"平时"、"非工作日"</rule>
<rule>识别特殊时间：如"有空的时候"、"方便的时候"、"随时"、"任何时间"</rule>
<rule>组合时间表述：如"明天下午3点"、"周六上午10点"、"下周三晚上"</rule>
<rule>时间范围表述：如"下午2-4点"、"周末任意时间"、"工作日晚上"</rule>
<rule>若用户明确提到时间相关词汇，提取完整时间表述，保持原始表达</rule>
<rule>若未提及任何时间信息，输出"null"</rule>
<rule>若用户表达"时间不限"、"什么时候都行"、"随便什么时候"等，输出"flexible"</rule>
</rules>
</parameter>""",

    "version": """<parameter name="version">
<rules>
<rule>识别产品风格需求，包括友家和心舍系列</rule>
<rule>若用户明确提到"友家7.0"或在合租场景下提到"好看"、"装修好"、"品质高"等，输出"7"（友家7.0）</rule>
<rule>若用户明确提到"友家6.0"，输出"6"（友家6.0）</rule>
<rule>若用户明确提到"心舍3.0"或在整租场景下提到"好看"、"装修好"、"品质高"等，输出"34"（心舍3.0）</rule>
<rule>若用户明确提到"心舍2.0"，输出"21"（心舍2.0）</rule>
<rule>若用户明确提到"心舍1.0"，输出"17"（心舍1.0）</rule>
<rule>若用户抱怨"装修太丑"、"装修不好"、"要好看的房子"等，根据租房类型推荐对应的高端产品</rule>
<rule>若未提及产品风格，输出"null"</rule>
</rules>
</parameter>""",

    "feature_house": """<parameter name="feature_house">
<rules>
<rule>识别小区特色需求</rule>
<rule>若用户提到"新小区"、"新一点的小区"、"不要老小区"、"小区太老"、"新建小区"等，输出"8"（新小区）</rule>
<rule>若未提及小区特色，输出"null"</rule>
</rules>
</parameter>""",

    "lease_duration": """<parameter name="lease_duration">
<rules>
<rule>识别租期需求，包括长租、月租、季租</rule>
<rule>若用户提到"长租1年"、"租1年"、"一年租期"等，输出"62"（长租1年）</rule>
<rule>若用户提到"长租2年"、"租2年"、"两年租期"等，输出"63"（长租2年）</rule>
<rule>若用户提到"长租3年"、"租3年"、"三年租期"等，输出"64"（长租3年）</rule>
<rule>若用户提到"1个月"、"月租"、"短租1个月"等，输出"67"（1个月）</rule>
<rule>若用户提到"2个月"、"短租2个月"等，输出"68"（2个月）</rule>
<rule>若用户提到"3个月"、"短租3个月"等，输出"69"（3个月）</rule>
<rule>若用户提到"4个月"、"季租4个月"等，输出"70"（4个月）</rule>
<rule>若用户提到"5个月"、"季租5个月"等，输出"71"（5个月）</rule>
<rule>若用户提到"6个月"、"季租6个月"、"半年"等，输出"72"（6个月）</rule>
<rule>若用户提到"短租"但未明确月数，默认输出"67"（1个月）</rule>
<rule>若未提及租期，输出"null"</rule>
</rules>
</parameter>""",

    "roommate": """<parameter name="roommate">
<rules>
<rule>识别室友性别要求</rule>
<rule>若用户提到"全女"、"都是女生"、"女室友"、"不要男室友"、"只要女生"等，输出"1"（全女）</rule>
<rule>若用户提到"全男"、"都是男生"、"男室友"、"不要女室友"、"只要男生"等，输出"2"（全男）</rule>
<rule>若未提及室友性别要求，输出"null"</rule>
</rules>
</parameter>""",

    "heating": """<parameter name="heating">
<rules>
<rule>识别供暖形式需求</rule>
<rule>若用户提到"集体供暖"、"集中供暖"、"统一供暖"、"小区供暖"等，输出"3"（集体供暖）</rule>
<rule>若用户提到"独立供暖"、"自采暖"、"独立采暖"、"自己控制"等，输出"2"（独立供暖）</rule>
<rule>若用户提到"中央供暖"、"中央空调供暖"等，输出"1"（中央供暖）</rule>
<rule>若用户明确表示"不要自采暖"、"不要独立供暖"，则推荐集体供暖，输出"3"</rule>
<rule>若未提及供暖形式，输出"null"</rule>
</rules>
</parameter>""",

    "tag": """<parameter name="tag">
<rules>
<rule>识别房源状态和设施需求</rule>
<rule>若用户提到"可预定"、"提前预定"、"不用立即签约"、"可以预约"等，输出"9"（可预定）</rule>
<rule>若用户提到"有电梯"、"带电梯"、"不要爬楼梯"、"电梯房"等，输出"13"（有电梯）</rule>
<rule>若同时提到多个状态，用|分隔，如"9|13"</rule>
<rule>若未提及房源状态，输出"null"</rule>
</rules>
</parameter>""",

    "roommate_count": """<parameter name="roommate_count">
<rules>
<rule>识别合租人数要求</rule>
<rule>若用户提到"2户合住"、"两个人合租"、"不要人太多"、"人少一点"等，输出"9"（2户合住）</rule>
<rule>若用户提到"3户合住"、"三个人合租"等，输出"10"（3户合住）</rule>
<rule>若用户抱怨"合租人数太多"、"室友太多"，默认推荐2户合住，输出"9"</rule>
<rule>若未提及合租人数，输出"null"</rule>
</rules>
</parameter>""",

    # 保留原有参数
    "room_or_resblock": """<parameter name="room_or_resblock">
<rules>
<rule>判断用户意向寻找的是具体房源，还是小区</rule>
<rule>若判断用户表达的是小区，则为"resblock"</rule>
<rule>若没有特别提及小区相关，则为"room"</rule>
<rule>该字段不支持"any"值，始终输出具体类型</rule>
</rules>
</parameter>""",

    "leasetype": """<parameter name="leasetype">
<rules>
<rule>租期超过1年为长租，用数字2表示</rule>
<rule>租期在1年及1年以内为短租，用数字1表示</rule>
<rule>若明确提到"短租"、"短期"、"临时"等词，输出"1"</rule>
<rule>若明确提到"长租"、"长期"、"常住"等词，输出"2"</rule>
<rule>若提到具体月数或天数，如"6个月"、"300天"，根据是否超过365天判断</rule>
<rule>若未提及租期，输出"null"</rule>
<rule>仅当明确针对租期使用"租期不限"、"长租短租都行"、"不限制租期"等表达时，才输出"any"</rule>
<rule>若用户表达中的"随便"、"不限"等词不是明确针对租期，则不要输出"any"</rule>
</rules>
</parameter>""",

    "resblock": """<parameter name="resblock">
<rules>
<rule>提取用户明确提到的小区名称</rule>
<rule>若未提及具体小区，输出"null"</rule>
</rules>
</parameter>""",

    "house_indices": """<parameter name="house_indices">
<rules>
<rule>提取用户想要看房的房源序号列表，适用于预约看房意图</rule>
<rule>识别明确序号：如"第1套"、"第2、3套"、"1,2"、"1和2"、"1 2"等，输出对应数字列表</rule>
<rule>识别中文数字：如"第一套"、"第二、三套"等，转换为阿拉伯数字</rule>
<rule>识别范围表达：如"前两套"→[1,2]、"前三套"→[1,2,3]、"最前面两套"→[1,2]</rule>
<rule>识别排除表达：如"除了第一套，其他都要"→根据房源总数返回除第1套外的所有序号</rule>
<rule>识别全选表达：如"全部"、"所有"、"都要"、"都看"→返回所有房源序号</rule>
<rule>识别指代表达：如"这套"、"那套"、"上面那套"→默认为[1]</rule>
<rule>识别条件表达：如"便宜的"、"面积大的"、"朝南的"→根据房源信息筛选对应序号</rule>
<rule>识别复合表达：如"1,2 周日10点半"→提取序号部分[1,2]</rule>
<rule>输出格式为数字数组，如[1,3]表示第1套和第3套</rule>
<rule>若无法确定具体房源，输出"null"</rule>
</rules>
</parameter>"""
}

def generate_dynamic_prompt(parameter_types: Set[str] = None) -> Dict[str, Any]:
    """
    根据需要提取的参数类型动态生成提示模板

    Args:
        parameter_types: 需要提取的参数类型集合，如果为None则提取所有参数

    Returns:
        动态生成的提示模板
    """
    # 如果未指定参数类型，则使用完整的提示模板
    if not parameter_types:
        return EXTRACTION_PROMPT

    # 构建参数定义
    parameter_definitions = []
    for param_type in parameter_types:
        if param_type in PARAMETER_DEFINITIONS:
            parameter_definitions.append(PARAMETER_DEFINITIONS[param_type])

    # 如果没有找到任何参数定义，则使用完整的提示模板
    if not parameter_definitions:
        logger.warning("No parameter definitions found, using full prompt template")
        return EXTRACTION_PROMPT

    # 构建系统提示
    system_prompt = """<parameter_extraction>
<!-- 参数定义和提取规则 -->

{}
</parameter_extraction>

请根据上述规则，从用户输入中提取所有相关参数。对于未提及的参数，请输出"null"。""".format("\n\n".join(parameter_definitions))

    # 根据参数类型选择合适的函数定义
    functions = _get_functions_for_parameters(parameter_types)

    # 创建动态提示模板
    dynamic_prompt = {
        "system_prompt": system_prompt,
        "functions": functions
    }

    logger.info(f"Generated dynamic prompt for parameter types: {parameter_types}")

    return dynamic_prompt

def _get_functions_for_parameters(parameter_types: Set[str]) -> List[Dict[str, Any]]:
    """
    根据参数类型获取合适的函数定义

    Args:
        parameter_types: 需要提取的参数类型集合

    Returns:
        函数定义列表
    """
    # 如果包含预约看房相关的参数，使用预约看房函数
    viewing_params = {"house_indices", "viewing_time"}
    if parameter_types and viewing_params.intersection(parameter_types):
        return [{
            "name": "extract_viewing_parameters",
            "description": "从用户输入中提取预约看房的关键参数",
            "parameters": {
                "type": "object",
                "properties": {
                    "house_indices": {
                        "type": "array",
                        "items": {"type": "integer"},
                        "description": "用户想要看房的房源序号列表，如[1,3]表示第1套和第3套"
                    },
                    "viewing_time": {
                        "type": "string",
                        "description": "看房时间，如'明天下午'、'周六上午'、'下周三'等"
                    }
                }
            }
        }]

    # 否则使用默认的房源搜索函数
    return EXTRACTION_PROMPT["functions"]
