from enum import Enum
from typing import Dict, Any, Set

# 当前使用的版本
CURRENT_VERSION = "v1"

class ExtractionPromptVersion(str, Enum):
    V1 = "v1"
    DYNAMIC = "dynamic"

# 导入所有版本
from app.llm.prompts.extraction.v1 import EXTRACTION_PROMPT as V1_EXTRACTION_PROMPT
from app.llm.prompts.extraction.dynamic import generate_dynamic_prompt

# 版本映射
EXTRACTION_PROMPTS = {
    ExtractionPromptVersion.V1: V1_EXTRACTION_PROMPT,
}

def get_extraction_prompt(version: str = CURRENT_VERSION, parameter_types: Set[str] = None) -> Dict[str, Any]:
    """
    获取指定版本的参数提取提示

    Args:
        version: 提示模板版本
        parameter_types: 需要提取的参数类型集合，如果为None则提取所有参数

    Returns:
        提示模板
    """
    # 如果是动态版本，则根据参数类型生成提示模板
    if version == ExtractionPromptVersion.DYNAMIC:
        return generate_dynamic_prompt(parameter_types)

    # 否则返回指定版本的提示模板
    return EXTRACTION_PROMPTS.get(version, EXTRACTION_PROMPTS[CURRENT_VERSION])
