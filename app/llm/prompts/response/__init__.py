from enum import Enum
from typing import Dict, Any

# 当前使用的版本
CURRENT_VERSION = "v1"

class ResponsePromptVersion(str, Enum):
    V1 = "v1"
    
# 导入所有版本
from app.llm.prompts.response.v1 import RESPONSE_PROMPT as V1_RESPONSE_PROMPT

# 版本映射
RESPONSE_PROMPTS = {
    ResponsePromptVersion.V1: V1_RESPONSE_PROMPT,
}

def get_response_prompt(version: str = CURRENT_VERSION) -> Dict[str, Any]:
    """获取指定版本的回复生成提示"""
    return RESPONSE_PROMPTS.get(version, RESPONSE_PROMPTS[CURRENT_VERSION])
