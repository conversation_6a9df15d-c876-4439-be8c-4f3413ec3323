"""
参数分析器模块

用于快速分析用户输入中包含哪些参数类型
"""

import json
import logging
from typing import Dict, Any, List, Set

from app.llm.client import generate_completion
from app.llm.prompts.analysis import get_analysis_prompt
from app.core.config import settings

logger = logging.getLogger(__name__)

class ParameterAnalyzer:
    """参数分析器"""

    async def analyze_parameters(
        self,
        user_input: str,
        conversation_history: List[Dict[str, str]] = None,  # 保留参数但不使用，以保持API兼容性
        prompt_version: str = None
    ) -> Set[str]:
        """
        分析用户输入中包含哪些参数类型

        注意：此函数只分析当前用户输入，不考虑对话历史，以避免参数类型混淆

        Args:
            user_input: 用户输入
            conversation_history: 对话历史（为保持API兼容性而保留，但不再使用）
            prompt_version: 提示模板版本

        Returns:
            参数类型集合
        """

        # 如果未指定版本，使用配置中的版本
        if prompt_version is None:
            prompt_version = settings.ANALYSIS_PROMPT_VERSION

        try:
            # 获取提示模板
            prompt = get_analysis_prompt(prompt_version)

            # 准备消息 - 只使用当前用户输入，不考虑对话历史
            messages = [
                {"role": "system", "content": prompt["system_prompt"]},
                {"role": "user", "content": user_input}
            ]

            # 调用LLM
            response = await generate_completion(
                messages=messages,
                functions=prompt["functions"],
                function_call={"name": "analyze_parameters"},
                temperature=0.1  # 使用低温度以获得更确定的结果
            )

            # 解析响应
            function_call_args = None

            try:
                # 检查是否是对象类型（旧版本API）
                if hasattr(response, 'choices') and len(response.choices) > 0:
                    message = response.choices[0].message
                    if hasattr(message, 'function_call') and message.function_call:
                        function_call_args = message.function_call.arguments
                # 检查是否是字典类型（新版本API）
                elif isinstance(response, dict) and 'choices' in response:
                    if response['choices'][0]['message'].get('function_call'):
                        function_call_args = response['choices'][0]['message']['function_call']['arguments']
            except Exception as e:
                logger.error(f"Error parsing response: {str(e)}, response type: {type(response)}")

            if function_call_args:
                args = json.loads(function_call_args)
                parameter_types = set(args.get("parameter_types", []))
                logger.info(f"Analyzed parameter types: {parameter_types}")
                return parameter_types

            return set()

        except Exception as e:
            logger.error(f"Error analyzing parameters: {str(e)}")
            return set()
