import json
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator

from app.llm.client import generate_completion
from app.llm.prompts.response import get_response_prompt
from app.core.config import settings
from app.core.prompt_config import get_response_text, PromptType

logger = logging.getLogger(__name__)

class ResponseGenerator:
    """回复生成器"""

    @staticmethod
    async def generate_response(
        conversation_history: List[Dict[str, str]],
        search_parameters: Dict[str, Any],
        houses_info: str = "",
        prompt_version: str = None,
        stream: bool = False
    ) -> str:
        """
        生成回复（主要处理未知情况和异常情况）

        Args:
            conversation_history: 对话历史
            search_parameters: 搜索参数
            houses_info: 房源信息（通常为空，因为有房源时不会走这个逻辑）
            prompt_version: 提示模板版本
            stream: 是否流式输出

        Returns:
            生成的回复
        """
        # 处理特殊的提示模板版本
        response_text = get_response_text(prompt_version)
        if response_text:
            return response_text

        # 如果未指定版本，使用配置中的版本
        if prompt_version is None:
            prompt_version = settings.RESPONSE_PROMPT_VERSION

        try:
            # 获取提示模板
            prompt = get_response_prompt(prompt_version)

            # 格式化搜索参数
            formatted_params = ResponseGenerator._format_search_parameters(search_parameters)

            # 替换模板中的参数
            system_prompt = prompt["system_prompt"].replace(
                "{{search_parameters}}", formatted_params
            )

            # 准备消息
            messages = [
                {"role": "system", "content": system_prompt},
            ]

            # 添加对话历史
            if conversation_history:
                messages.extend(conversation_history[-5:])  # 只使用最近5轮对话

            # 调用LLM
            response = await generate_completion(
                messages=messages,
                temperature=0.7,
                stream=stream
            )

            if not stream:
                # 解析响应
                try:
                    # 检查是否是对象类型（旧版本API）
                    if hasattr(response, 'choices') and len(response.choices) > 0:
                        return response.choices[0].message.content
                    # 检查是否是字典类型（新版本API）
                    elif isinstance(response, dict) and 'choices' in response:
                        return response['choices'][0]['message']['content']
                    else:
                        logger.error(f"Unexpected response format: {type(response)}")
                        return "当前只支持找房哦，请告诉我您的找房需求。"
                except Exception as e:
                    logger.error(f"Error parsing response: {str(e)}, response type: {type(response)}")
                    return "当前只支持找房哦，请告诉我您的找房需求。"
            else:
                # 流式响应直接返回
                return response

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return "当前只支持找房哦，请告诉我您的找房需求。"

    @staticmethod
    async def generate_response_stream(
        conversation_history: List[Dict[str, str]],
        search_parameters: Dict[str, Any],
        houses_info: str = "",
        prompt_version: str = None
    ) -> AsyncGenerator[str, None]:
        """
        流式生成回复（主要处理未知情况和异常情况）

        Args:
            conversation_history: 对话历史
            search_parameters: 搜索参数
            houses_info: 房源信息（通常为空）
            prompt_version: 提示模板版本

        Returns:
            生成的回复流
        """
        # 处理特殊的提示模板版本
        response_text = get_response_text(prompt_version)
        if response_text:
            yield response_text
            return

        try:
            # 获取流式响应
            stream_response = await ResponseGenerator.generate_response(
                conversation_history=conversation_history,
                search_parameters=search_parameters,
                houses_info=houses_info,
                prompt_version=prompt_version,
                stream=True
            )

            # 逐块返回
            async for chunk in stream_response:
                # 检查是否是字典类型（新版本API）
                if isinstance(chunk, dict) and chunk.get('choices') and chunk['choices'][0].get('delta', {}).get('content'):
                    yield chunk['choices'][0]['delta']['content']
                # 检查是否是对象类型（旧版本API）
                elif hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta
                    if hasattr(delta, 'content') and delta.content:
                        yield delta.content

        except Exception as e:
            logger.error(f"Error generating stream response: {str(e)}")
            yield "当前只支持找房哦，请告诉我您的找房需求。"

    @staticmethod
    def _format_search_parameters(params: Dict[str, Any]) -> str:
        """格式化搜索参数为可读文本"""
        if not params:
            return "暂无搜索条件"

        formatted = []

        # 位置
        if params.get("location") and params["location"] != "any":
            formatted.append(f"位置: {params['location']}")

        # 预算
        if params.get("budget") and params["budget"] != "any":
            try:
                min_price, max_price = params["budget"].split(",")
                if min_price == "0":
                    formatted.append(f"预算: {max_price}元以内")
                else:
                    formatted.append(f"预算: {min_price}-{max_price}元")
            except:
                formatted.append(f"预算: {params['budget']}")

        # 户型
        if params.get("room_type") and params["room_type"] != "any":
            formatted.append(f"户型: {params['room_type']}")

        # 居室
        if params.get("bed_room") and params["bed_room"] != "any":
            formatted.append(f"居室: {params['bed_room']}居")

        # 面积
        if params.get("area") and params["area"] != "any":
            try:
                min_area, max_area = params["area"].split(",")
                if min_area == "0":
                    formatted.append(f"面积: {max_area}平米以内")
                elif max_area == "120":  # 假设120是上限
                    formatted.append(f"面积: {min_area}平米以上")
                else:
                    formatted.append(f"面积: {min_area}-{max_area}平米")
            except:
                formatted.append(f"面积: {params['area']}")

        # 朝向
        if params.get("face") and params["face"] != "any":
            face_map = {"1": "东", "2": "南", "3": "西", "4": "北"}
            try:
                faces = [face_map.get(f, f) for f in params["face"].split(",")]
                formatted.append(f"朝向: {''.join(faces)}")
            except:
                formatted.append(f"朝向: {params['face']}")

        # 租期
        if params.get("leasetype") and params["leasetype"] != "any":
            if params["leasetype"] == "1":
                formatted.append("租期: 短租")
            elif params["leasetype"] == "2":
                formatted.append("租期: 长租")
            else:
                formatted.append(f"租期: {params['leasetype']}")

        # 入住时间
        if params.get("checkin_date") and params["checkin_date"] != "any":
            if params["checkin_date"] == "immediate":
                formatted.append("入住时间: 立即")
            else:
                formatted.append(f"入住时间: {params['checkin_date']}")

        # 通勤
        transport_info = []
        if params.get("transport") and params["transport"] != "any":
            transport_map = {
                "transit": "公共交通",
                "walk": "步行",
                "drive": "驾车",
                "ride": "骑行"
            }
            transport_info.append(transport_map.get(params["transport"], params["transport"]))

        if params.get("transport_minutes") and params["transport_minutes"] != "any":
            transport_info.append(f"{params['transport_minutes']}分钟")

        if transport_info:
            formatted.append(f"通勤: {' '.join(transport_info)}")

        # 小区
        if params.get("resblock") and params["resblock"] != "any":
            formatted.append(f"小区: {params['resblock']}")

        return "\n".join(formatted)
