"""
闲聊处理器模块

提供智能闲聊功能，根据上下文进行对话并适度引导用户找房。
"""

import logging
from typing import List, Dict, Any

from app.llm.client import generate_completion
from app.services.session_service import SessionService

logger = logging.getLogger(__name__)


class ChitchatHandler:
    """
    闲聊处理器
    
    负责处理用户的闲聊意图，包括：
    1. 根据上下文进行智能闲聊
    2. 适度引导用户找房（不要每轮都引导）
    3. 保存闲聊消息到数据库
    """

    def __init__(self, session_service: SessionService):
        """
        初始化闲聊处理器

        Args:
            session_service: 会话服务，用于保存消息
        """
        self.session_service = session_service
        self.logger = logging.getLogger(__name__)

    async def handle_chitchat(
        self,
        user_message: str,
        session_id: str,
        conversation_history: List[Dict[str, str]]
    ) -> str:
        """
        处理闲聊对话

        Args:
            user_message: 用户消息
            session_id: 会话ID
            conversation_history: 对话历史

        Returns:
            生成的闲聊回复
        """
        try:
            # 生成闲聊回复
            response = await self._generate_chitchat_response(
                user_message, conversation_history
            )

            # 保存闲聊回复到数据库
            await self.session_service.add_message(
                session_id=session_id,
                role="assistant",
                content=response
            )
            
            self.logger.info(f"✅ ChitchatHandler：已保存闲聊回复到数据库")
            
            return response

        except Exception as e:
            self.logger.error(f"处理闲聊时出错: {str(e)}")
            # 返回默认回复
            default_response = "很高兴和您聊天！我主要是帮您找房的，有什么找房需求可以告诉我哦～"
            
            # 保存默认回复
            await self.session_service.add_message(
                session_id=session_id,
                role="assistant", 
                content=default_response
            )
            
            return default_response

    async def _generate_chitchat_response(
        self,
        user_message: str,
        conversation_history: List[Dict[str, str]]
    ) -> str:
        """
        使用LLM生成闲聊回复

        Args:
            user_message: 用户消息
            conversation_history: 对话历史

        Returns:
            生成的闲聊回复
        """
        # 分析对话历史，判断是否需要引导找房
        should_guide = self._should_guide_to_housing(conversation_history)
        
        # 构建系统提示
        system_prompt = self._build_system_prompt(should_guide)
        
        # 准备消息
        messages = [
            {"role": "system", "content": system_prompt}
        ]
        
        # 添加最近的对话历史（最多5轮）
        if conversation_history:
            recent_history = conversation_history[-5:]
            messages.extend(recent_history)
        
        # 添加用户当前消息
        messages.append({"role": "user", "content": user_message})
        
        # 调用LLM生成回复
        response = await generate_completion(
            messages=messages,
            temperature=0.7,  # 稍高温度让对话更自然
        )
        
        # 提取回复内容
        if hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content.strip()
        elif isinstance(response, dict) and 'choices' in response:
            content = response['choices'][0]['message']['content'].strip()
        else:
            raise ValueError("无法解析LLM响应")
            
        return content

    def _should_guide_to_housing(self, conversation_history: List[Dict[str, str]]) -> bool:
        """
        判断是否应该引导用户找房
        
        根据对话历史判断是否需要引导：
        1. 如果最近3轮对话没有提到找房相关内容，则引导
        2. 如果已经连续引导过，则暂停引导
        
        Args:
            conversation_history: 对话历史
            
        Returns:
            是否应该引导找房
        """
        if not conversation_history:
            return True  # 没有历史记录，可以引导
            
        # 获取最近3轮对话
        recent_messages = conversation_history[-6:]  # 3轮用户+助手消息
        
        # 检查最近的助手消息是否已经包含引导
        assistant_messages = [
            msg for msg in recent_messages 
            if msg.get("role") == "assistant"
        ]
        
        # 如果最近的助手消息中包含找房相关词汇，说明已经引导过
        housing_keywords = ["找房", "房源", "租房", "需求", "预算", "位置", "户型"]
        
        for msg in assistant_messages[-2:]:  # 检查最近2条助手消息
            content = msg.get("content", "").lower()
            if any(keyword in content for keyword in housing_keywords):
                return False  # 最近已经引导过，不再引导
                
        return True  # 可以引导

    def _build_system_prompt(self, should_guide: bool) -> str:
        """
        构建系统提示词
        
        Args:
            should_guide: 是否需要引导找房
            
        Returns:
            系统提示词
        """
        base_prompt = """你是小木，一个友好的AI助手，主要帮助用户找房。你的特点：

1. 性格特点：
   - 友好、耐心、有亲和力
   - 说话自然，不过于正式
   - 能够理解和回应用户的情感

2. 对话原则：
   - 能够进行日常闲聊，让用户感到舒适
   - 回复要简洁自然，不要太长
   - 避免重复或机械化的表达

3. 专业定位：
   - 你的主要功能是帮助用户找房
   - 但不要显得过于功能性，要有人情味"""

        if should_guide:
            base_prompt += """
   - 在合适的时候可以自然地提及找房话题，但不要强硬推销
   - 引导要自然、不突兀"""
        else:
            base_prompt += """
   - 当前专注于闲聊，不需要特别提及找房话题"""

        base_prompt += """

请根据用户的消息进行自然的回复。回复要简洁、友好且符合你的人设。"""

        return base_prompt 