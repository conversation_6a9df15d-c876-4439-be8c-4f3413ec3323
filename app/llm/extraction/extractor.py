import json
import logging
import re
from typing import Dict, Any, List, Set

from app.core.config import settings
from app.llm.client import generate_completion

from app.llm.prompts.extraction import get_extraction_prompt, ExtractionPromptVersion

logger = logging.getLogger(__name__)

class ParameterExtractor:
    """参数提取器"""

    def __init__(self):
        """初始化参数提取器"""


    @staticmethod
    def _simplify_conversation_for_extraction(conversation_history: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """
        为参数提取精简对话历史，保留引用信息但移除技术冗余
        
        Args:
            conversation_history: 原始对话历史
            
        Returns:
            精简后的对话历史
        """
        simplified_history = []
        
        for message in conversation_history:
            role = message.get("role", "")
            content = message.get("content", "")
            
            if role == "user":
                # 用户消息保持原样
                simplified_history.append(message)
            elif role == "assistant":
                # 检查是否是JSON房源数据
                try:
                    json_data = json.loads(content)
                    if isinstance(json_data, dict) and "title" in json_data and "data" in json_data:
                        # 这是房源数据，提取关键信息保持引用能力
                        simplified_content = ParameterExtractor._extract_key_info_for_extraction(json_data)
                        simplified_history.append({
                            "role": "assistant",
                            "content": simplified_content
                        })
                    else:
                        # 其他JSON数据，保持原样
                        simplified_history.append(message)
                except (json.JSONDecodeError, TypeError):
                    # 不是JSON数据，检查长度
                    if len(content) > 800:
                        # 内容过长，截取前400字符
                        simplified_content = content[:400] + "..."
                        simplified_history.append({
                            "role": "assistant", 
                            "content": simplified_content
                        })
                    else:
                        # 内容适中，保持原样
                        simplified_history.append(message)
        
        return simplified_history

    @staticmethod
    def _extract_key_info_for_extraction(json_data: Dict[str, Any]) -> str:
        """
        从房源JSON数据中提取关键信息，保持用户引用能力
        
        Args:
            json_data: 房源JSON数据
            
        Returns:
            包含关键信息的文本
        """
        title = json_data.get("title", "房源推荐")
        houses = json_data.get("data", [])
        
        if not houses:
            return f"推荐：{title}（暂无房源）"
        
        # 构建简化的房源列表，保留关键信息以支持用户引用
        simplified_text = f"推荐：{title}\n"
        
        for i, house in enumerate(houses[:8], 1):  # 最多显示8套房源
            # 提取关键字段
            resblock_name = house.get("resblock_name", "未知小区")
            price = house.get("price", "")
            bedroom = house.get("bedroom", "")
            district_name = house.get("district_name", "")
            bizcircle_name = house.get("bizcircle_name", "")
            
            # 构建简化描述
            location_info = f"{district_name}{bizcircle_name}" if district_name and bizcircle_name else (district_name or bizcircle_name or "")
            room_info = f"{bedroom}居" if bedroom else ""
            price_info = f"{price}元/月" if price else ""
            
            simplified_text += f"{i}. {resblock_name}"
            if location_info:
                simplified_text += f"({location_info})"
            if room_info:
                simplified_text += f" {room_info}"
            if price_info:
                simplified_text += f" {price_info}"
            simplified_text += "\n"
        
        if len(houses) > 8:
            simplified_text += f"...还有{len(houses) - 8}套\n"
        
        return simplified_text.strip()

    async def extract_parameters(
        self,
        user_input: str,
        conversation_history: List[Dict[str, str]],
        current_context: Dict[str, Any],
        prompt_version: str = None,
        parameter_types: Set[str] = None
    ) -> Dict[str, Any]:
        """
        从用户输入中提取参数

        Args:
            user_input: 用户输入
            conversation_history: 对话历史
            current_context: 当前上下文
            prompt_version: 提示模板版本
            parameter_types: 需要提取的参数类型集合，如果为None则提取所有参数

        Returns:
            提取的参数
        """
        # 如果未指定版本，使用配置中的版本
        if prompt_version is None:
            # 如果指定了参数类型，使用动态提示模板
            if parameter_types:
                prompt_version = ExtractionPromptVersion.DYNAMIC
            else:
                prompt_version = settings.EXTRACTION_PROMPT_VERSION

        try:
            # 获取提示模板
            prompt = get_extraction_prompt(prompt_version, parameter_types)

            # 准备消息
            messages = [
                {"role": "system", "content": prompt["system_prompt"]},
            ]

            # 添加对话历史（精简版本，保留引用能力）
            if conversation_history:
                simplified_history = self._simplify_conversation_for_extraction(conversation_history)
                messages.extend(simplified_history[-5:])  # 只使用最近5轮对话
                
                # 记录精简效果
                original_length = sum(len(msg.get("content", "")) for msg in conversation_history[-5:])
                simplified_length = sum(len(msg.get("content", "")) for msg in simplified_history[-5:])
                logger.info(f"参数提取对话历史精简: {original_length} -> {simplified_length} 字符")

            # 添加当前上下文
            context_str = json.dumps(current_context, ensure_ascii=False)
            messages.append({"role": "system", "content": f"当前上下文: {context_str}"})

            # 添加用户输入
            messages.append({"role": "user", "content": user_input})

            # 如果指定了参数类型，则添加到提示中
            if parameter_types:
                param_list = ", ".join(parameter_types)
                messages.append({
                    "role": "system",
                    "content": f"请只提取以下参数: {param_list}。其他参数请返回null。"
                })
                logger.info(f"Extracting only these parameter types: {parameter_types}")

            # 调用LLM
            response = await generate_completion(
                messages=messages,
                functions=prompt["functions"],
                function_call={"name": "extract_house_parameters"}
            )

            # 解析响应
            function_call_args = None

            try:
                # 检查是否是对象类型（旧版本API）
                if hasattr(response, 'choices') and len(response.choices) > 0:
                    message = response.choices[0].message
                    if hasattr(message, 'function_call') and message.function_call:
                        function_call_args = message.function_call.arguments
                # 检查是否是字典类型（新版本API）
                elif isinstance(response, dict) and 'choices' in response:
                    if response['choices'][0]['message'].get('function_call'):
                        function_call_args = response['choices'][0]['message']['function_call']['arguments']
            except Exception as e:
                logger.error(f"Error parsing response: {str(e)}, response type: {type(response)}")

            logger.info(f"Function call args: {function_call_args}")

            if function_call_args:
                args = json.loads(function_call_args)
                logger.info(f"LLM extracted parameters: {args}")

                # 直接处理预算参数
                if "budget" in args and args["budget"] != "null" and (not parameter_types or "budget" in parameter_types):
                    budget = args["budget"]
                    logger.info(f"Processing budget in extractor: {budget}")

                    try:
                        # 处理 "3-8k" 这样的格式
                        if "-" in budget:
                            min_val, max_val = budget.split("-")
                            min_val = min_val.strip()
                            max_val = max_val.strip()

                            # 处理单位
                            if max_val.lower().endswith("k"):
                                max_val = max_val.lower().replace("k", "")
                                max_val = float(max_val) * 1000

                            if min_val.lower().endswith("k"):
                                min_val = min_val.lower().replace("k", "")
                                min_val = float(min_val) * 1000

                            # 更新预算参数
                            args["budget"] = f"{int(float(min_val))},{int(float(max_val))}"
                            logger.info(f"Converted budget in extractor: {args['budget']}")

                        # 处理 "3,8000" 这样的格式 (应该是 3000-8000)
                        elif "," in budget and not budget.startswith("0,"):
                            parts = budget.split(",")
                            if len(parts) == 2:
                                # 检查是否是价格范围
                                try:
                                    min_val = int(parts[0])
                                    max_val = int(parts[1])

                                    # 如果最小值是个位数，且最大值是4位或5位数，可能是错误格式
                                    if min_val < 10 and (max_val >= 1000 and max_val <= 99999):
                                        # 将最小值转换为千位数
                                        min_val = min_val * 1000
                                        args["budget"] = f"{min_val},{max_val}"
                                        logger.info(f"Fixed budget format from {budget} to {args['budget']}")
                                except ValueError:
                                    # 不是数字，保持原样
                                    pass
                    except Exception as e:
                        logger.error(f"Error processing budget in extractor: {str(e)}")

                # 增强参数提取
                enhanced_params = args.copy()

                # 增强房型参数提取
                if not parameter_types or "room_type" in parameter_types:
                    enhanced_params = self._enhance_room_type_parameters(user_input, enhanced_params)

                # 增强预算参数提取
                if not parameter_types or "budget" in parameter_types:
                    enhanced_params = self._enhance_budget_parameters(user_input, enhanced_params)

                logger.info(f"Enhanced parameters: {enhanced_params}")
                return enhanced_params

            return {}

        except Exception as e:
            logger.error(f"Error extracting parameters: {str(e)}")
            return {}



    def _enhance_budget_parameters(self, user_input: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强预算相关参数

        Args:
            user_input: 用户输入
            params: LLM提取的参数

        Returns:
            增强后的参数
        """
        # 如果已经有预算参数，检查格式并修正
        if "budget" in params and params["budget"] != "null":
            budget = params["budget"]
            logger.info(f"Enhancing budget parameter: {budget}")

            try:
                # 处理 "3,8000" 这样的格式 (应该是 3000-8000)
                if "," in budget and not budget.startswith("0,"):
                    parts = budget.split(",")
                    if len(parts) == 2:
                        try:
                            min_val = int(parts[0])
                            max_val = int(parts[1])

                            # 如果最小值是个位数，且最大值是4位或5位数，可能是错误格式
                            if min_val < 10 and (max_val >= 1000 and max_val <= 99999):
                                # 将最小值转换为千位数
                                min_val = min_val * 1000
                                params["budget"] = f"{min_val}-{max_val}"
                                logger.info(f"Fixed budget format from {budget} to {params['budget']}")
                        except ValueError:
                            # 不是数字，保持原样
                            pass

                # 处理 "3-8k" 这样的格式
                if "-" in params["budget"]:
                    min_val, max_val = params["budget"].split("-")
                    min_val = min_val.strip()
                    max_val = max_val.strip()

                    # 处理单位
                    if max_val.lower().endswith("k"):
                        max_val = max_val.lower().replace("k", "")
                        max_val = float(max_val) * 1000

                    if min_val.lower().endswith("k"):
                        min_val = min_val.lower().replace("k", "")
                        min_val = float(min_val) * 1000

                    # 更新预算参数，使用连字符而不是逗号
                    params["budget"] = f"{int(float(min_val))}-{int(float(max_val))}"
                    logger.info(f"Enhanced budget parameter: {params['budget']}")
            except Exception as e:
                logger.error(f"Error enhancing budget parameter: {str(e)}")

        # 如果没有预算参数，尝试从用户输入中提取
        elif not params.get("budget") or params.get("budget") == "null":
            # 预算范围模式
            budget_range_patterns = [
                # 3000-5000
                r'(\d+)(?:\s*[-~至到])\s*(\d+)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 3k-5k
                r'(\d+)(?:k|K|千)(?:\s*[-~至到])\s*(\d+)(?:k|K|千)?(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 3000到5000
                r'(\d+)(?:\s*(?:到|至))\s*(\d+)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 3,8000 (错误格式，应该是3000-8000)
                r'(\d{1})(?:\s*[,，])\s*(\d{4,5})(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)'
            ]

            # 单一预算模式
            single_budget_patterns = [
                # 5000元
                r'(?:预算|租金|价格|房租|租房|租|花|出|付)(?:\s*)(\d+)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 5k
                r'(?:预算|租金|价格|房租|租房|租|花|出|付)(?:\s*)(\d+)(?:k|K|千)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 最多5000
                r'(?:最多|不超过|小于|低于|少于|以内|以下|不到|不高于)(?:\s*)(\d+)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 最多5k
                r'(?:最多|不超过|小于|低于|少于|以内|以下|不到|不高于)(?:\s*)(\d+)(?:k|K|千)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)',
                # 5000左右
                r'(\d+)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)(?:\s*(?:左右|上下|附近|差不多))',
                # 5k左右
                r'(\d+)(?:k|K|千)(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)(?:\s*(?:左右|上下|附近|差不多))'
            ]

            # 检查预算范围模式
            for pattern in budget_range_patterns:
                match = re.search(pattern, user_input)
                if match:
                    min_val = match.group(1)
                    max_val = match.group(2)

                    # 处理单位
                    if 'k' in match.group(0).lower() or 'K' in match.group(0) or '千' in match.group(0):
                        if 'k' in min_val.lower() or 'K' in min_val or '千' in min_val:
                            min_val = min_val.lower().replace('k', '').replace('K', '').replace('千', '')
                            min_val = float(min_val) * 1000
                        else:
                            min_val = float(min_val)

                        if 'k' in max_val.lower() or 'K' in max_val or '千' in max_val:
                            max_val = max_val.lower().replace('k', '').replace('K', '').replace('千', '')
                            max_val = float(max_val) * 1000
                        else:
                            max_val = float(max_val)
                    else:
                        min_val = float(min_val)
                        max_val = float(max_val)

                    # 处理 "3,8000" 这样的格式
                    if pattern == r'(\d{1})(?:\s*[,，])\s*(\d{4,5})(?:\s*(?:元|块|块钱|人民币|每月|一个月|月租|月付|月)?)':
                        min_val = min_val * 1000

                    params["budget"] = f"{int(min_val)}-{int(max_val)}"
                    logger.info(f"Extracted budget range: {params['budget']}")
                    break

            # 如果没有找到预算范围，检查单一预算模式
            if not params.get("budget") or params.get("budget") == "null":
                for pattern in single_budget_patterns:
                    match = re.search(pattern, user_input)
                    if match:
                        val = match.group(1)

                        # 处理单位
                        if 'k' in val.lower() or 'K' in val or '千' in val:
                            val = val.lower().replace('k', '').replace('K', '').replace('千', '')
                            val = float(val) * 1000
                        else:
                            val = float(val)

                        # 如果包含"左右"，创建一个范围
                        if "左右" in match.group(0) or "上下" in match.group(0) or "附近" in match.group(0) or "差不多" in match.group(0):
                            lower = max(int(val) - 1000, 0)
                            upper = int(val) + 1000
                            params["budget"] = f"{lower}-{upper}"
                        # 如果包含"最多"等词，设置为上限
                        elif any(keyword in match.group(0) for keyword in ["最多", "不超过", "小于", "低于", "少于", "以内", "以下", "不到", "不高于"]):
                            params["budget"] = f"0-{int(val)}"
                        # 否则，设置为精确值
                        else:
                            params["budget"] = f"0-{int(val)}"

                        logger.info(f"Extracted single budget: {params['budget']}")
                        break

        return params

    def _enhance_room_type_parameters(self, user_input: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强房型相关参数

        Args:
            user_input: 用户输入
            params: LLM提取的参数

        Returns:
            增强后的参数
        """
        # 如果LLM没有提取到房型，使用规则提取
        if not params.get("room_type") or params.get("room_type") == "null":
            # 整租相关关键词
            whole_rent_patterns = [
                r'整租',
                r'全租',
                r'独立公寓',
                r'单间',
                r'单独的房子',
                r'单独租',
                r'独立',
                r'一个人住',
                r'自己住',
                r'独居',
                r'不和别人住',
                r'整套',
                r'整个房子',
                r'独立卫生间',
                r'独立厨房',
                r'独门独户',
                r'一居',
                r'一室',
                r'单间'
            ]

            # 合租相关关键词
            shared_rent_patterns = [
                r'合租',
                r'分租',
                r'合住',
                r'共享公寓',
                r'和别人一起住',
                r'室友',
                r'合伙',
                r'拼房',
                r'拼租',
                r'共享',
                r'合用',
                r'合租房',
                r'床位',
                r'次卧',
                r'主卧',
                r'多居室中的一间',
                r'三居室中的主卧',
                r'两居室中的次卧',
                r'合租的房间',
                r'合租的卧室'
            ]

            # 检查是否包含整租关键词
            for pattern in whole_rent_patterns:
                if re.search(pattern, user_input):
                    # 检查是否同时包含合租关键词，如果有，需要进一步判断
                    has_shared_keywords = any(re.search(shared_pattern, user_input) for shared_pattern in shared_rent_patterns)
                    if not has_shared_keywords:
                        params["room_type"] = "整租"
                        logger.info(f"Enhanced room_type: 整租 (pattern: {pattern})")
                        break

            # 如果没有找到整租关键词，检查是否包含合租关键词
            if not params.get("room_type") or params.get("room_type") == "null":
                for pattern in shared_rent_patterns:
                    if re.search(pattern, user_input):
                        params["room_type"] = "合租"
                        logger.info(f"Enhanced room_type: 合租 (pattern: {pattern})")
                        break

        return params
