import json
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy.orm import Session as SQLAlchemySession
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from app.db.models.session import Session
from app.db.models.message import Message

# 同步操作
def create_session(db: SQLAlchemySession, user_id: Optional[str] = None) -> Session:
    """创建新会话"""
    db_session = Session(user_id=user_id)
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    return db_session

def get_session(db: SQLAlchemySession, session_id: str) -> Optional[Session]:
    """获取会话"""
    return db.query(Session).filter(Session.session_id == session_id).first()

def update_session_params(
    db: SQLAlchemySession, session_id: str, search_parameters: Dict[str, Any]
) -> Optional[Session]:
    """更新会话参数"""
    db_session = get_session(db, session_id)
    if db_session:
        db_session.search_parameters = json.dumps(search_parameters)
        db_session.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(db_session)
    return db_session

def add_message(
    db: SQLAlchemySession,
    session_id: str,
    role: str,
    content: str,
    intent: Optional[str] = None,
    extracted_params: Optional[Dict[str, Any]] = None,
) -> Message:
    """添加消息"""
    db_message = Message(
        session_id=session_id,
        role=role,
        content=content,
        intent=intent,
        extracted_params=json.dumps(extracted_params) if extracted_params else None,
    )
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    return db_message

def get_session_messages(
    db: SQLAlchemySession, session_id: str, limit: int = 50
) -> List[Message]:
    """获取会话消息"""
    return (
        db.query(Message)
        .filter(Message.session_id == session_id)
        .order_by(Message.timestamp.asc())
        .limit(limit)
        .all()
    )

# 异步操作
async def async_create_session(
    db: AsyncSession, user_id: Optional[str] = None
) -> Session:
    """异步创建新会话"""
    db_session = Session(user_id=user_id)
    db.add(db_session)
    await db.commit()
    await db.refresh(db_session)
    return db_session

async def async_get_session(db: AsyncSession, session_id: str) -> Optional[Session]:
    """异步获取会话"""
    result = await db.execute(select(Session).filter(Session.session_id == session_id))
    return result.scalars().first()

async def async_update_session_params(
    db: AsyncSession, session_id: str, search_parameters: Dict[str, Any]
) -> Optional[Session]:
    """异步更新会话参数"""
    db_session = await async_get_session(db, session_id)
    if db_session:
        db_session.search_parameters = json.dumps(search_parameters)
        db_session.updated_at = datetime.utcnow()
        await db.commit()
        await db.refresh(db_session)
    return db_session

async def async_add_message(
    db: AsyncSession,
    session_id: str,
    role: str,
    content: str,
    intent: Optional[str] = None,
    extracted_params: Optional[Dict[str, Any]] = None,
) -> Message:
    """异步添加消息"""
    db_message = Message(
        session_id=session_id,
        role=role,
        content=content,
        intent=intent,
        extracted_params=json.dumps(extracted_params) if extracted_params else None,
    )
    db.add(db_message)
    await db.commit()
    await db.refresh(db_message)
    return db_message

async def async_get_session_messages(
    db: AsyncSession, session_id: str, limit: int = 50
) -> List[Message]:
    """异步获取会话消息"""
    result = await db.execute(
        select(Message)
        .filter(Message.session_id == session_id)
        .order_by(Message.timestamp.asc())
        .limit(limit)
    )
    return result.scalars().all()
