from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from app.core.config import settings

# 创建同步引擎
SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=100,            # 大基础连接池
    max_overflow=200,         # 峰值时最多300个连接
    pool_timeout=5,
    pool_recycle=3600,
    pool_pre_ping=True
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建异步引擎
ASYNC_SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL

if ASYNC_SQLALCHEMY_DATABASE_URL.startswith("sqlite:"):
    ASYNC_SQLALCHEMY_DATABASE_URL = ASYNC_SQLALCHEMY_DATABASE_URL.replace(
        "sqlite:///", "sqlite+aiosqlite:///"
    )
    async_engine = create_async_engine(
        ASYNC_SQLALCHEMY_DATABASE_URL, 
        connect_args={"check_same_thread": False},
        pool_size=10,
        max_overflow=20,
        pool_timeout=30,
        pool_recycle=1800,
        pool_pre_ping=True
    )
elif ASYNC_SQLALCHEMY_DATABASE_URL.startswith("mysql+pymysql:"):
    ASYNC_SQLALCHEMY_DATABASE_URL = ASYNC_SQLALCHEMY_DATABASE_URL.replace(
        "mysql+pymysql:", "mysql+aiomysql:"
    )
    async_engine = create_async_engine(
        ASYNC_SQLALCHEMY_DATABASE_URL,
        pool_size=100,            # 大基础连接池
        max_overflow=200,         # 峰值时最多300个连接
        pool_timeout=5,           # 快速失败，避免排队
        pool_recycle=3600,        # 1小时回收，稳定性优先
        pool_pre_ping=True,       # 自动检测死连接
        pool_reset_on_return='commit',  # 归还时重置
        connect_args={
            "charset": "utf8mb4",
            "connect_timeout": 5,   # 快速连接
        }
    )
else:
    async_engine = create_async_engine(
        ASYNC_SQLALCHEMY_DATABASE_URL,
        pool_size=100,
        max_overflow=200,
        pool_timeout=5,
        pool_recycle=3600,
        pool_pre_ping=True,
        pool_reset_on_return='commit'
    )

import logging

logger = logging.getLogger(__name__)

AsyncSessionLocal = sessionmaker(
    autocommit=False, 
    autoflush=False, 
    bind=async_engine, 
    class_=AsyncSession,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def get_async_db():
    """获取异步数据库会话"""
    session = AsyncSessionLocal()
    try:
        yield session
        await session.commit()
    except Exception:
        await session.rollback()
        raise
    finally:
        # 确保会话正确关闭
        try:
            await session.close()
        except Exception as e:
            logger.error(f"关闭数据库会话时出错: {str(e)}")
            # 不抛出异常，避免掩盖原始错误

async def check_pool_health():
    """检查连接池健康状态"""
    try:
        pool = async_engine.pool
        pool_size = pool.size()
        checked_out = pool.checkedout()
        available = pool_size - checked_out
        
        return {
            "pool_size": pool_size,
            "checked_out": checked_out,
            "available": available,
            "usage_rate": checked_out / pool_size if pool_size > 0 else 0
        }
    except Exception as e:
        logger.error(f"检查连接池状态失败: {str(e)}")
        return {
            "pool_size": 0,
            "checked_out": 0,
            "available": 0,
            "usage_rate": 0
        }
