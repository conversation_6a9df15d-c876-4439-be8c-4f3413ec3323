from typing import List, Optional
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete

from app.db.models.message import Message

class MessageRepository:
    """消息仓库"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create(
        self,
        session_id: str,
        role: str,
        content: str,
        intent: Optional[str] = None,
        extracted_params: Optional[str] = None
    ) -> Message:
        """创建消息"""
        message = Message(
            session_id=session_id,
            role=role,
            content=content,
            intent=intent,
            extracted_params=extracted_params
        )
        self.db.add(message)
        await self.db.commit()
        await self.db.refresh(message)
        return message
    
    async def get_by_id(self, message_id: int) -> Optional[Message]:
        """通过ID获取消息"""
        result = await self.db.execute(
            select(Message).filter(Message.message_id == message_id)
        )
        return result.scalars().first()
    
    async def get_by_session_id(self, session_id: str, limit: int = 50) -> List[Message]:
        """通过会话ID获取消息列表"""
        result = await self.db.execute(
            select(Message)
            .filter(Message.session_id == session_id)
            .order_by(Message.timestamp.asc())
            .limit(limit)
        )
        return result.scalars().all()
    
    async def delete_by_session_id(self, session_id: str) -> int:
        """删除会话的所有消息"""
        result = await self.db.execute(
            delete(Message).where(Message.session_id == session_id)
        )
        await self.db.commit()
        return result.rowcount
