from typing import List, Optional, Tuple
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete, func

from app.db.models.session import Session

class SessionRepository:
    """会话仓库"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create(self, user_id: Optional[str] = None, context: Optional[str] = None) -> Session:
        """创建会话"""
        session = Session(
            user_id=user_id,
            context=context
        )
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        return session

    async def get_by_id(self, session_id: str) -> Optional[Session]:
        """通过ID获取会话"""
        result = await self.db.execute(
            select(Session).filter(Session.session_id == session_id)
        )
        return result.scalars().first()

    async def get_by_user_id(self, user_id: str) -> List[Session]:
        """通过用户ID获取会话列表"""
        result = await self.db.execute(
            select(Session)
            .filter(Session.user_id == user_id)
            .order_by(Session.updated_at.desc())
        )
        return result.scalars().all()

    async def update(self, session_id: str, context: Optional[str] = None) -> Optional[Session]:
        """更新会话"""
        # 准备更新数据
        update_data = {"updated_at": datetime.utcnow()}
        if context is not None:
            update_data["context"] = context

        # 执行更新
        await self.db.execute(
            update(Session)
            .where(Session.session_id == session_id)
            .values(**update_data)
        )
        await self.db.commit()

        # 返回更新后的会话
        return await self.get_by_id(session_id)

    async def delete(self, session_id: str) -> bool:
        """删除会话"""
        result = await self.db.execute(
            delete(Session).where(Session.session_id == session_id)
        )
        await self.db.commit()
        return result.rowcount > 0

    async def get_all(self, skip: int = 0, limit: int = 20, user_id: Optional[str] = None) -> Tuple[List[Session], int]:
        """获取所有会话"""
        # 构建查询
        query = select(Session)
        count_query = select(func.count()).select_from(Session)

        # 如果指定了用户ID，则只返回该用户的会话
        if user_id:
            query = query.where(Session.user_id == user_id)
            count_query = count_query.where(Session.user_id == user_id)

        # 获取总数
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()

        # 获取分页数据
        result = await self.db.execute(
            query
            .order_by(Session.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )

        return result.scalars().all(), total
