import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy import Column, String, DateTime, JSON, Text
from sqlalchemy.orm import relationship

from app.db.base import Base

class Session(Base):
    """会话模型"""

    __tablename__ = "sessions"

    # 主键
    session_id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))

    # 用户标识
    user_id = Column(String(36), index=True, nullable=True)

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 上下文 (JSON格式)
    context = Column(Text, nullable=True)  # 存储为JSON字符串

    # 关系
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Session {self.session_id}>"
