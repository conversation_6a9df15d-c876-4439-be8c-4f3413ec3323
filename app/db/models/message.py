from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship

from app.db.base import Base

class Message(Base):
    """消息模型"""
    
    __tablename__ = "messages"
    
    # 主键
    message_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 外键
    session_id = Column(String(36), ForeignKey("sessions.session_id"), nullable=False)
    
    # 消息内容
    role = Column(String(20), nullable=False)  # 'user' 或 'assistant'
    content = Column(Text, nullable=False)
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 意图和参数
    intent = Column(String(50), nullable=True)
    extracted_params = Column(Text, nullable=True)  # 存储为JSON字符串
    
    # 关系
    session = relationship("Session", back_populates="messages")
    
    def __repr__(self):
        return f"<Message {self.message_id}: {self.role}>"
