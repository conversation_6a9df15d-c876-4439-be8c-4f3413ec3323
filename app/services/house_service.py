"""
房源服务模块

提供房源搜索和格式化功能。
"""

import logging
from typing import Dict, Any, Optional

from app.services.house.searcher import HouseSearcher
from app.services.house.formatter import HouseFormatter

from app.services.session_service import SessionService

logger = logging.getLogger(__name__)

class HouseService:
    """
    房源服务

    负责房源搜索和格式化，是房源相关功能的门面类。
    遵循单一职责原则，将具体实现委托给专门的类。
    """

    def __init__(self, session_service: Optional[SessionService] = None):
        """
        初始化房源服务

        Args:
            session_service: 会话服务
        """

        self.session_service = session_service
        self.house_searcher = HouseSearcher(self.session_service)
        self.house_formatter = HouseFormatter()

    async def search_houses(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        搜索房源

        Args:
            params: 搜索参数

        Returns:
            搜索结果
        """
        try:
            # 使用房源搜索器搜索房源
            result = await self.house_searcher.search_houses(params)
            return result

        except Exception as e:
            logger.error(f"Error searching houses: {str(e)}")
            return {"error": f"Error searching houses: {str(e)}"}



    def format_houses_json(self, search_result: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        格式化房源信息为JSON格式的看房单

        Args:
            search_result: 搜索结果
            context: 上下文信息，包含位置、预算等参数

        Returns:
            格式化后的房源信息，包含标题和数据
        """
        return self.house_formatter.format_houses_json(search_result, context)
