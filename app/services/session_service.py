import json
import logging
from typing import Dict, List, Any, Optional, <PERSON><PERSON>
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.db.repositories.session_repo import SessionRepository
from app.db.repositories.message_repo import MessageRepository
from app.services.context_service import ContextService

logger = logging.getLogger(__name__)

class SessionService:
    """会话管理服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.session_repo = SessionRepository(db)
        self.message_repo = MessageRepository(db)
        self.context_service = ContextService()

    async def create_session(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        创建新会话

        Args:
            user_id: 用户ID（可选）

        Returns:
            会话信息
        """
        # 初始化上下文
        context = self.context_service.initialize_context()

        # 创建会话
        session = await self.session_repo.create(
            user_id=user_id,
            context=json.dumps(context)
        )

        return {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "created_at": session.created_at.isoformat(),
            "context": context
        }

    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话"""
        session = await self.session_repo.get_by_id(session_id)
        if not session:
            return None

        try:
            context = json.loads(session.context) if session.context else {}
        except json.JSONDecodeError:
            context = {}

        return {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "context": context
        }

    async def update_session_context(
        self, session_id: str, updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """更新会话上下文"""
        session = await self.session_repo.get_by_id(session_id)
        if not session:
            return None

        try:
            context = json.loads(session.context) if session.context else {}
        except json.JSONDecodeError:
            context = {}

        # 更新上下文
        updated_context = self.context_service.update_context(context, updates)

        # 记录更新前后的会话状态
        logger.info(f"更新会话上下文: session_id={session_id}")
        logger.info(f"更新前: session_state={context.get('session_state', '未知')}")
        logger.info(f"更新后: session_state={updated_context.get('session_state', '未知')}")
        logger.info(f"更新内容: {updates}")

        # 保存更新
        await self.session_repo.update(
            session_id=session_id,
            context=json.dumps(updated_context)
        )

        return updated_context

    async def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        intent: Optional[str] = None,
        extracted_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """添加消息"""
        message = await self.message_repo.create(
            session_id=session_id,
            role=role,
            content=content,
            intent=intent,
            extracted_params=json.dumps(extracted_params) if extracted_params else None
        )

        return {
            "message_id": message.message_id,
            "session_id": message.session_id,
            "role": message.role,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "intent": message.intent,
            "extracted_params": extracted_params
        }

    async def get_conversation_history(
        self, session_id: str, limit: int = 10
    ) -> List[Dict[str, str]]:
        """获取对话历史"""
        messages = await self.message_repo.get_by_session_id(
            session_id=session_id,
            limit=limit
        )

        history = []
        for msg in messages:
            history.append({
                "role": msg.role,
                "content": msg.content
            })

        return history

    async def merge_extracted_params(
        self, session_id: str, params: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """合并提取的参数到会话上下文"""
        session_data = await self.get_session(session_id)
        if not session_data:
            return None

        # 过滤掉null值并合并到上下文
        filtered_params = {k: v for k, v in params.items() if v != "null" and v != None}
        updated_context = self.context_service.update_context(session_data["context"], filtered_params)

        # 保存更新
        await self.session_repo.update(
            session_id=session_id,
            context=json.dumps(updated_context)
        )

        return updated_context

    async def clear_messages(self, session_id: str) -> bool:
        """清除会话中的所有消息"""
        try:
            # 删除会话中的所有消息
            await self.message_repo.delete_by_session_id(session_id)
            logger.info(f"All messages cleared for session: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error clearing messages for session {session_id}: {str(e)}")
            return False

    async def get_last_search_result(self, session_id: str) -> Dict[str, Any]:
        """获取最后一次搜索结果"""
        try:
            # 获取会话
            session = await self.get_session(session_id)
            if not session:
                logger.error(f"Session not found: {session_id}")
                return {}

            # 从会话上下文中获取搜索结果
            context = session["context"]

            # 构建搜索结果
            result = {}

            # 提取关键参数
            for key in ["location", "budget", "bed_room", "room_type", "area"]:
                if key in context and context[key]:
                    result[key] = context[key]

            # 提取搜索结果
            if "search_summary" in context:
                result["summary"] = context["search_summary"]

            if "rooms_count" in context:
                result["rooms_count"] = context["rooms_count"]

            if "promotion_rooms_count" in context:
                result["promotion_rooms_count"] = context["promotion_rooms_count"]

            # 提取最后一次API响应
            if "last_api_response" in context:
                try:
                    # 解析JSON字符串为对象
                    api_response = json.loads(context["last_api_response"])
                    result["api_response"] = api_response
                except Exception as e:
                    logger.error(f"Error parsing last_api_response: {str(e)}")
                    result["api_response"] = context["last_api_response"]

            # 获取最后一条助手消息
            messages = await self.get_messages(session_id, limit=10)
            assistant_messages = [msg for msg in messages if msg["role"] == "assistant"]

            if assistant_messages:
                result["last_response"] = assistant_messages[-1]["content"]

            return result

        except Exception as e:
            logger.error(f"Error getting last search result for session {session_id}: {str(e)}")
            return {}

    async def get_sessions(self, skip: int = 0, limit: int = 20, user_id: Optional[str] = None) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有会话"""
        try:
            # 获取会话列表
            sessions, total = await self.session_repo.get_all(skip=skip, limit=limit, user_id=user_id)

            # 转换为字典列表
            result = []
            for session in sessions:
                # 解析上下文
                try:
                    context = json.loads(session.context) if session.context else {}
                except json.JSONDecodeError:
                    context = {}

                # 获取最后一条消息
                messages = await self.message_repo.get_by_session_id(
                    session_id=session.session_id,
                    limit=1
                )

                last_message = None
                if messages:
                    last_message = {
                        "role": messages[0].role,
                        "content": messages[0].content,
                        "timestamp": messages[0].timestamp.isoformat()
                    }

                # 构建会话信息
                session_info = {
                    "session_id": session.session_id,
                    "user_id": session.user_id,
                    "created_at": session.created_at.isoformat(),
                    "updated_at": session.updated_at.isoformat(),
                    "last_message": last_message,
                    "context": context
                }

                result.append(session_info)

            return result, total

        except Exception as e:
            logger.error(f"Error getting sessions: {str(e)}")
            return [], 0

    async def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            # 删除会话
            return await self.session_repo.delete(session_id)
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {str(e)}")
            return False

    async def get_messages(self, session_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取会话消息"""
        try:
            # 获取消息
            messages = await self.message_repo.get_by_session_id(
                session_id=session_id,
                limit=limit
            )

            # 转换为字典列表
            result = []
            for msg in messages:
                # 解析提取的参数
                try:
                    extracted_params = json.loads(msg.extracted_params) if msg.extracted_params else None
                except json.JSONDecodeError:
                    extracted_params = None

                # 构建消息信息
                message_info = {
                    "message_id": msg.message_id,
                    "role": msg.role,
                    "content": msg.content,
                    "timestamp": msg.timestamp.isoformat(),
                    "intent": msg.intent,
                    "extracted_params": extracted_params
                }

                result.append(message_info)

            return result

        except Exception as e:
            logger.error(f"Error getting messages for session {session_id}: {str(e)}")
            return []
