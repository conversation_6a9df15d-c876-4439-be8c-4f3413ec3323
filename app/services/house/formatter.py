"""
房源格式化器模块

提供格式化房源信息的功能。
"""

import json
import logging
from typing import Dict, Any, List, Optional

from app.core.config import settings

# 设置日志
logger = logging.getLogger(__name__)

class HouseFormatter:
    """
    房源格式化器

    负责将房源信息格式化为不同的展示格式。
    """

    def _generate_title(self, location: str, budget: str, room_type: str, bed_room: str, prefix: str = "", 
                       resblock_count: int = 0, room_count: int = 0) -> str:
        """
        生成标题

        根据位置、预算、户型等参数生成标题。

        Args:
            location: 位置
            budget: 预算
            room_type: 户型
            bed_room: 居室数量
            prefix: 标题前缀
            resblock_count: 小区数量
            room_count: 房源数量

        Returns:
            生成的标题
        """
        # 构建条件描述
        conditions = []
        
        # 位置信息
        if location and location != "any":
            conditions.append(f"**{location}**")
        
        # 预算信息
        if budget and budget != "any":
            if "," in budget:
                min_price, max_price = budget.split(",")
                if min_price == "0":
                    conditions.append(f"**{max_price}元以内**")
                else:
                    conditions.append(f"**{min_price}-{max_price}元**")
            else:
                conditions.append(f"**{budget}元以内**")
        
        # 房型和居室
        type_info = []
        if room_type and room_type != "any":
            type_info.append(room_type)
        if bed_room and bed_room != "any":
            type_info.append(f"{bed_room}居室")
        
        if type_info:
            # 用顿号连接房型和居室，避免4个星号连在一起
            conditions.append(f"**{'、'.join(type_info)}**")
        
        # 如果没有任何条件，返回默认标题
        if not conditions:
            return "**看房单**" if "看房" in prefix else "**小区列表**"
        
        # 构建完整标题
        condition_text = "、".join(conditions)
        
        # 如果有统计信息，添加统计和推荐语
        if resblock_count > 0 or room_count > 0:
            if resblock_count > 0 and room_count > 0:
                stats = f"共**{resblock_count}个小区{room_count}套房源**符合"
            elif room_count > 0:
                stats = f"共**{room_count}套房源**符合"
            elif resblock_count > 0:
                stats = f"共**{resblock_count}个小区**符合"
            else:
                stats = ""
            
            if stats:
                if resblock_count > 0:
                    return f"{condition_text}。{stats}，小木为您推荐以下**热门小区**："
                else:
                    return f"{condition_text}。{stats}，小木为您推荐以下**房源**："
        
        # 没有统计信息时的简单格式
        return f"{condition_text}"

    def format_houses_json(self, search_result: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        格式化房源信息为JSON格式的看房单

        Args:
            search_result: 搜索结果
            context: 上下文信息，包含位置、预算等参数

        Returns:
            格式化后的房源信息，包含标题和数据
        """
        # 如果DEBUG模式开启，输出原始JSON数据到日志
        if settings.DEBUG:
            try:
                logger.debug("房源搜索API返回数据: %s", json.dumps(search_result, ensure_ascii=False))
            except Exception as e:
                logger.error("JSON序列化失败: %s", str(e))

        if search_result is None:
            return {"title": "看房单", "data": []}

        # 从上下文中提取参数
        context = context or {}
        location = context.get("location", "")
        budget = context.get("budget", "")
        room_type = context.get("room_type", "")
        bed_room = context.get("bed_room", "")

        if "data" in search_result and isinstance(search_result["data"], list):
            # 处理小区数据
            resblocks = search_result.get("data", [])

            # 如果没有小区数据，返回空数组
            if not resblocks:
                title = self._generate_title(location, budget, room_type, bed_room, "看房单：", 
                                           resblock_count=0, room_count=0)
                return {"title": title, "data": []}

            # 计算小区数量和房源数量
            resblock_count = len(resblocks)
            room_count = sum(int(resblock.get('house_count', 0)) for resblock in resblocks)
            
            # 生成看房单标题
            title = self._generate_title(location, budget, room_type, bed_room, "看房单：", 
                                       resblock_count=resblock_count, room_count=room_count)

            # 按价格排序小区
            # sorted_resblocks = sorted(resblocks, key=lambda x: float(x.get('min_price', '0')))

            # 返回JSON格式的小区数据
            return {"title": title, "data": resblocks}

        # 处理房源API的响应格式
        # 检查是否有嵌套的data字段
        if "data" in search_result and isinstance(search_result["data"], dict):
            data = search_result["data"]
            rooms = data.get("rooms", []) or []
            promotion_rooms = data.get("promotionRooms", []) or []
        else:
            # 直接从search_result中获取
            rooms = search_result.get("rooms", []) or []
            promotion_rooms = search_result.get("promotionRooms", []) or []

        # 计算房源数量
        total_rooms = len(rooms) + len(promotion_rooms)

        # 如果没有房源，返回空数组和提示信息
        if not rooms and not promotion_rooms:
            title = self._generate_title(location, budget, room_type, bed_room, "看房单：", 
                                       resblock_count=0, room_count=0)
            return {
                "title": title,
                "data": [],
            }

        # 生成看房单标题（房源模式）
        title = self._generate_title(location, budget, room_type, bed_room, "看房单：", 
                                   resblock_count=0, room_count=total_rooms)

        # 合并推荐房源和普通房源
        all_rooms = []

        # 添加推荐房源
        if promotion_rooms:
            for room in promotion_rooms:
                room["is_promotion"] = True
                all_rooms.append(room)

        # 添加普通房源
        if rooms:
            for room in rooms:
                room["is_promotion"] = False
                all_rooms.append(room)

        # 按价格排序
        sorted_rooms = sorted(all_rooms, key=lambda x: x.get('price', 0))

        # 返回JSON格式的房源数据
        return {"title": title, "data": sorted_rooms}



