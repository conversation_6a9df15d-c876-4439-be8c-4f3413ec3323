"""
房源搜索策略模块

使用策略模式处理不同位置类型的房源搜索逻辑。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class HouseSearchStrategy(ABC):
    """
    房源搜索策略抽象基类
    
    定义了搜索房源的统一接口。
    """
    
    @abstractmethod
    async def search(self, request_params: Dict[str, Any], location_value: str, type_value: Optional[int]) -> Dict[str, Any]:
        """
        执行房源搜索
        
        Args:
            request_params: 请求参数
            location_value: 位置值
            type_value: 租房类型值
            
        Returns:
            搜索结果
        """
        pass


class ResidentialSearchStrategy(HouseSearchStrategy):
    """
    小区和自如寓搜索策略
    
    适用于位置类型为"小区"和"自如寓"的搜索。
    """
    
    def __init__(self, rooms_service):
        """
        初始化策略
        
        Args:
            rooms_service: 房源服务
        """
        self.rooms_service = rooms_service
    
    async def search(self, request_params: Dict[str, Any], location_value: str, type_value: Optional[int]) -> Dict[str, Any]:
        """执行小区/自如寓房源搜索"""
        logger.info(f"使用小区/自如寓搜索策略，位置: {location_value}, 类型: {type_value}")
        return await self.rooms_service.search_rooms(request_params)


class SubwaySearchStrategy(HouseSearchStrategy):
    """
    地铁线搜索策略
    
    适用于位置类型为"地铁线"的搜索。
    """
    
    def __init__(self, subway_house_service):
        """
        初始化策略
        
        Args:
            subway_house_service: 地铁房源服务
        """
        self.subway_house_service = subway_house_service
    
    async def search(self, request_params: Dict[str, Any], location_value: str, type_value: Optional[int]) -> Dict[str, Any]:
        """执行地铁线房源搜索"""
        logger.info(f"使用地铁线搜索策略，位置: {location_value}, 类型: {type_value}")
        return await self.subway_house_service.search_subway_houses(location_value, type_value)


class DistrictSearchStrategy(HouseSearchStrategy):
    """
    行政区搜索策略
    
    适用于位置类型为"行政区"的搜索。
    """
    
    def __init__(self, district_house_service):
        """
        初始化策略
        
        Args:
            district_house_service: 行政区房源服务
        """
        self.district_house_service = district_house_service
    
    async def search(self, request_params: Dict[str, Any], location_value: str, type_value: Optional[int]) -> Dict[str, Any]:
        """执行行政区房源搜索"""
        logger.info(f"使用行政区搜索策略，位置: {location_value}, 类型: {type_value}")
        return await self.district_house_service.search_district_houses(location_value, type_value)


class DefaultSearchStrategy(HouseSearchStrategy):
    """
    默认搜索策略
    
    适用于其他位置类型的搜索，使用小区查询接口。
    """
    
    def __init__(self, resblocks_service):
        """
        初始化策略
        
        Args:
            resblocks_service: 小区服务
        """
        self.resblocks_service = resblocks_service
    
    async def search(self, request_params: Dict[str, Any], location_value: str, type_value: Optional[int]) -> Dict[str, Any]:
        """执行默认房源搜索"""
        logger.info(f"使用默认搜索策略，位置: {location_value}, 类型: {type_value}")
        return await self.resblocks_service.search_resblocks(request_params)


class HouseSearchStrategyFactory:
    """
    房源搜索策略工厂
    
    根据位置类型创建相应的搜索策略。
    """
    
    def __init__(self, rooms_service, subway_house_service, district_house_service, resblocks_service):
        """
        初始化策略工厂
        
        Args:
            rooms_service: 房源服务
            subway_house_service: 地铁房源服务
            district_house_service: 行政区房源服务
            resblocks_service: 小区服务
        """
        self.strategies = {
            # 小区相关
            "小区": ResidentialSearchStrategy(rooms_service),
            "自如寓": ResidentialSearchStrategy(rooms_service),
            
            # 地铁相关
            "地铁线": SubwaySearchStrategy(subway_house_service),

            # 行政区
            "行政区": DistrictSearchStrategy(district_house_service),
            "城区": DistrictSearchStrategy(district_house_service),
            
            # POI类型（地点、公司、学校等）- 使用默认策略
            "地铁": DefaultSearchStrategy(resblocks_service),  # 地铁站也使用地铁线策略
            "地铁站": DefaultSearchStrategy(resblocks_service),
            "地点": DefaultSearchStrategy(resblocks_service),
            "公司": DefaultSearchStrategy(resblocks_service),
            "学校": DefaultSearchStrategy(resblocks_service),
            "医院": DefaultSearchStrategy(resblocks_service),
            "商圈": DefaultSearchStrategy(resblocks_service),
            "写字楼": DefaultSearchStrategy(resblocks_service),
            "办公楼": DefaultSearchStrategy(resblocks_service),
            "购物中心": DefaultSearchStrategy(resblocks_service),
            "交通地点": DefaultSearchStrategy(resblocks_service),
            "景区": DefaultSearchStrategy(resblocks_service),
            "道路": DefaultSearchStrategy(resblocks_service),
            "组织": DefaultSearchStrategy(resblocks_service),
            "科技园区": DefaultSearchStrategy(resblocks_service),
        }
        self.default_strategy = DefaultSearchStrategy(resblocks_service)
    
    def get_strategy(self, location_type: str) -> HouseSearchStrategy:
        """
        根据位置类型获取搜索策略
        
        Args:
            location_type: 位置类型
            
        Returns:
            对应的搜索策略
        """
        # 如果位置类型为空或None，使用默认策略
        if not location_type:
            logger.warning(f"位置类型为空，使用默认策略")
            return self.default_strategy
            
        strategy = self.strategies.get(location_type, self.default_strategy)
        logger.info(f"选择搜索策略: {location_type} -> {strategy.__class__.__name__}")
        return strategy 