"""
房源搜索参数处理器

提供各种搜索参数的处理功能，每个参数类型都有独立的处理方法。
"""

import logging
import re
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class HouseSearchParamsHandler:
    """
    房源搜索参数处理器
    
    负责处理和转换各种搜索参数，将用户输入转换为API可接受的格式。
    """

    def __init__(self):
        """初始化参数处理器"""
        pass

    def process_all_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理所有搜索参数
        
        Args:
            params: 原始参数字典
            
        Returns:
            处理后的API参数字典
        """
        api_params = {}
        
        # 处理位置相关参数
        self._handle_location_params(params, api_params)
        
        # 处理基础房源参数
        self._handle_room_type(params, api_params)
        self._handle_bedroom_count(params, api_params)
        self._handle_checkin_date(params, api_params)
        self._handle_checkin_people_num(params, api_params)
        
        # 处理价格和面积范围
        self._handle_budget(params, api_params)
        self._handle_area(params, api_params)
        
        # 处理房源特征参数
        self._handle_face(params, api_params)
        self._handle_layout(params, api_params)
        self._handle_feature(params, api_params)
        self._handle_version(params, api_params)
        self._handle_feature_house(params, api_params)
        self._handle_roommate(params, api_params)
        self._handle_heating(params, api_params)
        self._handle_tag(params, api_params)
        self._handle_leaseTypeDurationLong(params, api_params)
        self._handle_leaseTypeDurationMonth(params, api_params)
        self._handle_leaseTypeDurationSeason(params, api_params)

        # 处理通勤相关参数
        self._handle_transport(params, api_params)
        self._handle_transport_minutes(params, api_params)
        self._handle_distance(params, api_params)
        
        # 处理系统参数
        self._handle_sort_params(params, api_params)
        self._handle_pagination_params(params, api_params)

        # 处理其他自定义参数: ab test ， app接口版本参数
        # self._handle_testInfo_params(params, api_params)
        # self._handle_appVersion_params(params, api_params)
        
        return api_params

    def _handle_location_params(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理位置相关参数"""
        if "location" in params and params["location"]:
            if "location_type" in params and params["location_type"]:
                location_type = params["location_type"]
                api_params["location_type"] = location_type
                logger.info(f"设置位置类型: {location_type}")

    def _handle_room_type(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理租房类型参数"""
        if "room_type" in params and params["room_type"]:
            room_type = params["room_type"]
            if room_type == "整租":
                api_params["type"] = 2
            elif room_type == "合租":
                api_params["type"] = 1
            else:
                api_params["type"] = 2  # 默认整租
            logger.info(f"设置租房类型: {room_type} -> {api_params['type']}")

    def _handle_bedroom_count(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理卧室数量参数"""
        if "bed_room" in params and params["bed_room"]:
            bed_room = params["bed_room"]
            match = re.search(r'(\d+)', str(bed_room))
            if match:
                bed_room_num = int(match.group(1))
                if 1 <= bed_room_num <= 5:
                    api_params["bedroom"] = str(bed_room_num)
                    logger.info(f"设置卧室数量: {bed_room_num}")
                else:
                    logger.warning(f"卧室数量超出范围: {bed_room_num}, 应在1-5之间")

    def _handle_checkin_date(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理入住时间参数"""
        if "checkin_date" in params and params["checkin_date"]:
            checkin_date = params["checkin_date"]
            date_pattern = r'^\d{4}-\d{2}-\d{2}$'
            if re.match(date_pattern, checkin_date):
                api_params["checkin_date"] = checkin_date
                logger.info(f"设置预计入住时间: {checkin_date}")
            else:
                logger.warning(f"无效的入住时间格式: {checkin_date}, 期望格式: YYYY-MM-DD")

    def _handle_checkin_people_num(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理入住人数参数"""
        if "checkin_people_num" in params and params["checkin_people_num"]:
            people_num = params["checkin_people_num"]
            match = re.search(r'(\d+)', str(people_num))
            if match:
                num = int(match.group(1))
                if 1 <= num <= 10:
                    api_params["checkin_people_num"] = str(num)
                    logger.info(f"设置入住人数: {num}")
                else:
                    logger.warning(f"入住人数超出范围: {num}, 应在1-10之间")

    def _handle_budget(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理预算参数"""
        if "budget" not in params or not params["budget"]:
            return
            
        budget = params["budget"]
        logger.info(f"处理预算参数: {budget}")

        if isinstance(budget, str):
            # 处理使用连字符的格式 (如 "3000-8000")
            if "-" in budget:
                min_price, max_price = budget.split("-")
                try:
                    min_price_int = int(min_price.strip())
                    max_price_int = int(max_price.strip())
                    api_params["price"] = f"{min_price_int},{max_price_int}"
                    logger.info(f"预算范围转换: {budget} -> {api_params['price']}")
                except ValueError as e:
                    logger.error(f"预算范围转换错误: {str(e)}")
            # 处理使用逗号的格式 (如 "3000,8000")
            elif "," in budget:
                min_price, max_price = budget.split(",")
                try:
                    min_price_int = int(min_price.strip())
                    max_price_int = int(max_price.strip())
                    api_params["price"] = f"{min_price_int},{max_price_int}"
                    logger.info(f"使用预算范围: {api_params['price']}")
                except ValueError as e:
                    logger.error(f"预算范围处理错误: {str(e)}")
            else:
                # 尝试提取数字，设置合理范围
                match = re.search(r'(\d+)', budget)
                if match:
                    price = int(match.group(1))
                    price_range = self._get_price_range(price)
                    api_params["price"] = price_range
                    logger.info(f"单一预算值转换: {price} -> {price_range}")
                else:
                    logger.warning(f"无法从预算中提取价格: {budget}")
        else:
            logger.warning(f"预算不是字符串类型: {budget}")
            try:
                budget_str = str(budget)
                match = re.search(r'(\d+)', budget_str)
                if match:
                    price = int(match.group(1))
                    api_params["price"] = f"0,{price}"
                    logger.info(f"非字符串预算转换: {api_params['price']}")
            except Exception as e:
                logger.error(f"非字符串预算处理错误: {str(e)}")

    def _get_price_range(self, price: int) -> str:
        """根据价格获取合理的价格范围"""
        if price <= 1000:
            return "0,1000"
        elif price <= 2000:
            return "1000,2000"
        elif price <= 3000:
            return "2000,3000"
        elif price <= 5000:
            return "3000,5000"
        elif price <= 8000:
            return "5000,8000"
        else:
            return "8000,20000"

    def _handle_area(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理面积参数"""
        if "area" not in params or not params["area"]:
            return
            
        area = params["area"]
        logger.info(f"处理面积参数: {area}")

        if isinstance(area, str):
            # 处理使用连字符的格式 (如 "60-80")
            if "-" in area:
                min_area, max_area = area.split("-")
                try:
                    min_area_int = int(min_area.strip())
                    max_area_int = int(max_area.strip())
                    api_params["area"] = f"{min_area_int},{max_area_int}"
                    logger.info(f"面积范围转换: {area} -> {api_params['area']}")
                except ValueError as e:
                    logger.error(f"面积范围转换错误: {str(e)}")
            # 处理使用逗号的格式 (如 "60,80")
            elif "," in area:
                min_area, max_area = area.split(",")
                try:
                    min_area_int = int(min_area.strip())
                    max_area_int = int(max_area.strip())
                    api_params["area"] = f"{min_area_int},{max_area_int}"
                    logger.info(f"使用面积范围: {api_params['area']}")
                except ValueError as e:
                    logger.error(f"面积范围处理错误: {str(e)}")
            else:
                # 尝试提取数字，设置合理范围
                match = re.search(r'(\d+)', area)
                if match:
                    area_value = int(match.group(1))
                    area_range = self._get_area_range(area_value)
                    api_params["area"] = area_range
                    logger.info(f"单一面积值转换: {area_value} -> {area_range}")
                else:
                    logger.warning(f"无法从面积中提取数值: {area}")
        else:
            logger.warning(f"面积不是字符串类型: {area}")
            try:
                area_str = str(area)
                match = re.search(r'(\d+)', area_str)
                if match:
                    area_value = int(match.group(1))
                    api_params["area"] = f"0,{area_value}"
                    logger.info(f"非字符串面积转换: {api_params['area']}")
            except Exception as e:
                logger.error(f"非字符串面积处理错误: {str(e)}")

    def _get_area_range(self, area_value: int) -> str:
        """根据面积获取合理的面积范围"""
        if area_value <= 30:
            return "0,30"
        elif area_value <= 50:
            return "30,50"
        elif area_value <= 80:
            return "50,80"
        elif area_value <= 100:
            return "80,100"
        elif area_value <= 150:
            return "100,150"
        else:
            return "150,500"

    def _handle_face(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理朝向参数 - 东：1, 南：2, 西：3, 北：4"""
        if "face" in params and params["face"]:
            api_params["face"] = params["face"]
            logger.info(f"设置朝向参数: {params['face']}")

    def _handle_layout(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理特色户型参数 - 独立卫生间：1, 独立阳台：2"""
        if "layout" in params and params["layout"]:
            api_params["layout"] = params["layout"]
            logger.info(f"设置特色户型参数: {params['layout']}")

    def _handle_feature(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理房源特色参数 - 高层视野：71, 带起居室：61, 全屋智能：63, 南北通透：72, loft：44"""
        if "feature" in params and params["feature"]:
            api_params["feature"] = params["feature"]
            logger.info(f"设置房源特色参数: {params['feature']}")

    def _handle_version(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理产品风格参数 - 友家7.0：7, 友家6.0：6, 心舍3.0：34, 心舍2.0：21, 心舍1.0：17"""
        if "version" in params and params["version"]:
            api_params["version"] = params["version"]
            logger.info(f"设置产品风格参数: {params['version']}")

    def _handle_feature_house(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理小区特色参数 - 新小区：8"""
        if "feature_house" in params and params["feature_house"]:
            api_params["feature_house"] = params["feature_house"]
            logger.info(f"设置小区特色参数: {params['feature_house']}")

    def _handle_roommate(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理室友性别参数 - 全女：1, 全男：2"""
        if "roommate" in params and params["roommate"]:
            api_params["roommate"] = params["roommate"]
            logger.info(f"设置室友性别参数: {params['roommate']}")

    def _handle_heating(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理供暖形式参数 - 中央供暖：1, 独立供暖：2, 集体供暖：3"""
        if "heating" in params and params["heating"]:
            api_params["heating"] = params["heating"]
            logger.info(f"设置供暖形式参数: {params['heating']}")

    def _handle_leaseTypeDurationLong(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """长租1年：62  长租2年：63  长租3年：64"""
        if "leasetypeduration_long" in params and params["leasetypeduration_long"]:
            api_params["leasetypeduration_long"] = params["leasetypeduration_long"]
            logger.info(f"设置长租参数: {params['leasetypeduration_long']}")

    def _handle_leaseTypeDurationMonth(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """月租 1个月：67  2个月：68  3个月：69"""
        if "leasetypeduration_month" in params and params["leasetypeduration_month"]:
            api_params["leasetypeduration_month"] = params["leasetypeduration_month"]
            logger.info(f"设置月租参数: {params['leasetypeduration_month']}")

    def _handle_leaseTypeDurationSeason(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """季租 4个月： 70  5个月：71  6个月：72"""
        if "leasetypeduration_season" in params and params["leasetypeduration_season"]:
            api_params["leasetypeduration_season"] = params["leasetypeduration_season"]
            logger.info(f"设置季租租参数: {params['leasetypeduration_season']}")

    def _handle_tag(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理房源状态参数 - 可预定：9, 有电梯：13"""
        if "tag" in params and params["tag"]:
            api_params["tag"] = params["tag"]
            logger.info(f"设置房源状态参数: {params['tag']}")

    def _handle_transport(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理通勤方式参数 - walk: 步行, ride: 骑行, drive: 驾车, transit: 公交"""
        if "transport" in params and params["transport"]:
            transport = params["transport"]
            # 支持的通勤方式：walk(步行), ride(骑行), drive(驾车), transit(公交)
            valid_transports = ["walk", "ride", "drive", "transit"]
            
            if transport in valid_transports:
                api_params["transport"] = transport
                logger.info(f"设置通勤方式参数: {transport}")
            else:
                logger.warning(f"无效的通勤方式: {transport}, 支持的方式: {valid_transports}")

    def _handle_transport_minutes(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理通勤时间参数"""
        if "transport_minutes" in params and params["transport_minutes"]:
            transport_minutes = params["transport_minutes"]
            try:
                minutes = int(transport_minutes)
                api_params["minute"] = minutes
                logger.info(f"设置通勤时间参数: {transport_minutes} 分钟")
            except ValueError:
                logger.warning(f"无效的通勤时间格式: {transport_minutes}")

    def _handle_distance(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理距离参数"""
        if "distance" in params and params["distance"]:
            distance = params["distance"]
            try:
                distance_km = float(distance)
                api_params["distance"] = distance_km
                logger.info(f"设置距离参数: {distance_km} 公里")
            except ValueError:
                logger.warning(f"无效的距离格式: {distance}")

    def _handle_testInfo_params(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """ab实验参数"""
        if "testInfo" in params and params["test_info"]:
            api_params["testInfo"] = params["test_info"]
            logger.info(f"设置ab实验参数: {params['test_info']}")

        logger.info(f"设置ab实验参数: {api_params['testInfo']} {api_params['testInfo']}")

    def _handle_appVersion_params(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """app接口版本参数"""
        if "appVersion" in params and params["app_version"]:
            api_params["appVersion"] = params["app_version"]
            logger.info(f"设置app接口版本参数: {params['app_version']}")

        logger.info(f"设置app接口版本参数: {api_params['appVersion']} {api_params['appVersion']}")

    def _handle_sort_params(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理排序参数"""
        # 处理排序字段
        if "sort" in params:
            api_params["sort"] = params["sort"]
        else:
            api_params["sort"] = "price"  # 默认按价格排序

        # 处理排序顺序
        if "order" in params:
            api_params["order"] = params["order"]
        else:
            api_params["order"] = "asc"  # 默认升序

        logger.info(f"设置排序参数: {api_params['sort']} {api_params['order']}")

    def _handle_pagination_params(self, params: Dict[str, Any], api_params: Dict[str, Any]) -> None:
        """处理分页参数"""
        # 处理当前页码
        if "current_page" in params and params["current_page"]:
            try:
                current_page = int(params["current_page"])
                api_params["page"] = current_page
                logger.info(f"使用指定页码: {current_page}")
                logger.info(f"分页参数详情: params={params}, current_page={current_page}, api_params={api_params}")
            except (ValueError, TypeError):
                api_params["page"] = 1
                logger.warning(f"无效的页码值: {params['current_page']}, 使用默认页码 1")
        elif "pagination" in params and params["pagination"] == "true":
            # 如果有分页标志但没有current_page，默认为第2页
            api_params["page"] = 2
            logger.info("检测到分页标志，使用页码 2")
        elif "page" in params:
            api_params["page"] = params["page"]
        else:
            api_params["page"] = 1  # 默认第一页

        # 再次检查分页参数，确保它被正确设置
        logger.info(f"最终分页参数: page={api_params.get('page', 1)}, params={params}")

        # 固定每页显示3条结果，以便支持"再来点"功能
        api_params["page_size"] = 3  # 固定每页3条