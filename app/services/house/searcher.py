"""
房源搜索器模块

提供搜索房源的功能。
"""

import logging
from typing import Dict, Any, Optional


from app.core.config import settings
from app.services.session_service import SessionService
from app.services.bff.location_service import LocationService
from app.services.bff.subway_service import SubwayHouseService
from app.services.bff.district_service import DistrictHouseService
from app.services.bff.resblocks_service import ResblocksService
from app.services.bff.rooms_service import RoomsService
from app.services.house.handle_params import HouseSearchParamsHandler
from app.services.house.search_strategies import HouseSearchStrategyFactory
from app.sse.constants import MappingRegistry

logger = logging.getLogger(__name__)

class HouseSearcher:
    """
    房源搜索器

    负责搜索房源，处理不同位置类型的搜索参数。
    """

    def __init__(self, session_service: Optional[SessionService] = None):
        """
        初始化房源搜索器

        Args:
            session_service: 会话服务
        """
        self.session_service = session_service

        # 参数处理器
        self.params_handler = HouseSearchParamsHandler()

        # 查询位置相关service
        self.location_service = LocationService()
        
        # 初始化各种服务
        self.resblocks_service = ResblocksService()
        self.rooms_service = RoomsService()
        self.subway_house_service = SubwayHouseService()
        self.district_house_service = DistrictHouseService()
        
        # 初始化搜索策略工厂
        self.strategy_factory = HouseSearchStrategyFactory(
            rooms_service=self.rooms_service,
            subway_house_service=self.subway_house_service,
            district_house_service=self.district_house_service,
            resblocks_service=self.resblocks_service
        )

    async def search_houses(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        搜索房源

        Args:
            params: 搜索参数

        Returns:
            搜索结果
        """
        try:
            # 准备搜索参数
            api_params = await self._prepare_search_params(params)
            
            # 获取位置信息
            location_value, sug_type = await self._get_location_value(params["location"])
            
            # 获取位置类型（中文）
            location_type_chinese = api_params.get("location_type", "")
            
            # 转换位置类型为英文（用于后端API）
            location_type_english = self._convert_location_type_to_english(location_type_chinese)
            
            # 获取租房类型
            type_value = api_params.get("type")
            
            # 构建基础请求参数（使用英文位置类型）
            request_params = self._build_request_params(api_params, location_value, sug_type, location_type_english)
            
            # 使用策略模式执行搜索（使用中文位置类型进行策略选择）
            search_strategy = self.strategy_factory.get_strategy(location_type_chinese)
            return await search_strategy.search(request_params, location_value, type_value)

        except Exception as e:
            logger.exception(f"房源搜索失败: {str(e)}")
            return {"error": f"房源搜索失败: {str(e)}"}

    async def _prepare_search_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备搜索参数

        Args:
            params: 原始参数

        Returns:
            API参数
        """
        # 使用参数处理器处理所有参数
        api_params = self.params_handler.process_all_params(params)
        
        return api_params

    def _convert_location_type_to_english(self, location_type_chinese: str) -> str:
        """
        将中文位置类型转换为英文（用于后端API）
        
        Args:
            location_type_chinese: 中文位置类型
            
        Returns:
            英文位置类型
        """
        if not location_type_chinese:
            logger.warning("位置类型为空，返回空字符串")
            return ""
            
        # 使用映射注册中心进行转换
        english_type = MappingRegistry.get_parent_type_english(location_type_chinese)
        logger.info(f"位置类型转换: {location_type_chinese} -> {english_type}")
        return english_type

    def _build_request_params(self, api_params: Dict[str, Any], location_value: str, sug_type: int, location_type: str) -> Dict[str, Any]:
        """
        构建请求参数
        
        Args:
            api_params: API参数
            location_value: 位置值
            sug_type: 建议类型
            location_type: 位置类型（英文）
            
        Returns:
            构建好的请求参数
        """
        request_params = {
            # 北京市代码
            "city_code": settings.DEFAULT_CITY_CODE,
            "location_value": location_value,
            "sug_type": sug_type,
            "location_type": location_type,
        }
        
        # 将api_params中的所有参数添加到request_params中
        request_params.update(api_params)
        
        logger.info(f"构建请求参数完成: 位置类型={location_type}, 位置值={location_value}")
        return request_params

    async def _get_location_value(self, location: str) -> tuple[str, int]:
        """
        获取位置对应的value值和sug_type

        Args:
            location: 位置名称

        Returns:
            tuple: (位置对应的value值, sug_type)，如果获取失败则返回(原始位置, 0)
        """
        try:
            items = await self.location_service.get_location_suggestions(location)
            if items and len(items) > 0:
                # 使用第一个建议项的value作为location_value
                location_value = items[0].get("value", location)
                sug_type = items[0].get("type", 0)
                logger.info(f"获取 location value: {location_value}, sug_type: {sug_type}")
                return location_value, sug_type
            else:
                logger.error(f"没查询到位置对应value信息: {location}, using original location")
                return location, 0

        except Exception as e:
            logger.error(f"处理建议API响应时出错: {str(e)}")
            return location, 0
