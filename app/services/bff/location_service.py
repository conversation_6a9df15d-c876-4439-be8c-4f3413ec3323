"""
位置服务模块

提供位置相关的功能，包括位置建议等。
"""
import asyncio
import logging
from typing import Dict, Any, List, Tuple, Union

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

class LocationService:
    """
    位置服务

    负责处理位置相关的功能。
    """

    def __init__(self):
        """
        初始化位置服务
        """
        # 查询位置列表信息
        self.api_sug_url = f"{settings.HOUSE_API_BASE_URL}{settings.HOUSE_LOCATION_SUG_API}"


    async def get_location_suggestions(self, keyword: str) -> List[Dict[str, Any]]:
        """
        获取位置建议列表

        Args:
            keyword: 搜索关键词

        Returns:
            位置建议列表，每个建议项包含详细信息
        """
        try:
            suggestion_params = {
                "keyword": keyword,
                "city_code": settings.DEFAULT_CITY_CODE,
            }

            logger.info(f"Calling Location Suggestion API with request params: {suggestion_params}")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.api_sug_url,
                    params=suggestion_params,
                    timeout=5.0
                )

                if response.status_code != 200:
                    logger.error(f"Location Suggestion API error: {response.status_code} - {response.text}")
                    return []

                data = response.json()
                items = data.get("data", {}).get("items", [])

                logger.info(f"Location Suggestion API response: {items}")

                if not items:
                    logger.warning(f"No location suggestions found for keyword: {keyword}")
                    return []

                logger.info(f"Found {len(items)} location suggestions for keyword: {keyword}")
                return items

        except Exception as e:
            logger.exception(f"Error getting location suggestions: {str(e)}")
            return []

    def process_location(self, keyword: str, suggestions: List[Dict[str, Any]]) -> Union[Tuple[str, str], List[Dict[str, Any]]]:
        """
        处理位置输入，根据用户输入和建议列表返回合适的位置信息

        如果用户输入与任何一个建议的title完全匹配，则直接返回该建议的type_name和name
        如果没有完全匹配但有建议列表，则返回第一个建议的位置信息
        如果没有建议，则返回空列表

        Args:
            keyword: 用户输入的关键词
            suggestions: 位置建议列表

        Returns:
            如果完全匹配任何一个建议或使用默认第一个建议，返回(location_type, location)元组
            如果需要用户选择，返回原始建议列表
            如果没有建议，返回空列表
        """
        # 如果keyword为null或空，直接返回空列表
        if not keyword or keyword == "null":
            logger.warning("Keyword is null or empty, returning empty list")
            return []
            
        if not suggestions:
            logger.warning("No suggestions provided for processing")
            return []

        # 按顺序循环检查用户输入是否与任何一个建议的title完全匹配
        # 确保返回第一个匹配的结果
        for i, suggestion in enumerate(suggestions):
            if keyword == suggestion['title']:
                location_type = suggestion['type_name']
                location = suggestion['name']
                logger.info(f"Exact match found for '{keyword}' at index {i}: {location_type} - {location}")
                return location, location_type

        # 如果没有完全匹配，返回建议列表供用户选择
        logger.warning(f"No exact match found for '{keyword}', returning suggestions list")
        return suggestions

if __name__ == '__main__':
    async def main():
        location_service = LocationService()

        # 测试1：获取位置建议
        keyword = "酒仙桥"
        suggestions = await location_service.get_location_suggestions(keyword)
        print(f"获取到的建议列表: {len(suggestions)} 条")

        # 测试2：完全匹配第一个建议的情况
        result1 = location_service.process_location("酒仙桥", suggestions)
        print(f"\n测试完全匹配第一个建议 - 输入: '酒仙桥'")
        print(f"结果类型: {type(result1)}")
        print(f"结果内容: {result1}")

        # 测试3：完全匹配非第一个建议的情况
        if len(suggestions) > 3:
            match_title = suggestions[3]['title']
            result2 = location_service.process_location(match_title, suggestions)
            print(f"\n测试完全匹配非第一个建议 - 输入: '{match_title}'")
            print(f"结果类型: {type(result2)}")
            print(f"结果内容: {result2}")

        # 测试4：不完全匹配的情况
        result3 = location_service.process_location("酒仙", suggestions)
        print(f"\n测试不完全匹配 - 输入: '酒仙'")
        print(f"结果类型: {type(result3)}")
        print(f"结果条数: {len(result3)}")
        print(f"第一条结果: {result3[0]['title']} - {result3[0]['type_name']}")
    asyncio.run(main())