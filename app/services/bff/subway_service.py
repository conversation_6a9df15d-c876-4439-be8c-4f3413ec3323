"""
地铁房源服务模块

提供地铁房源相关的功能，包括地铁线房源搜索等。
"""

import logging
from typing import Dict, Any

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

class SubwayHouseService:
    """
    地铁房源服务

    负责处理地铁房源相关的功能。
    """

    def __init__(self):
        """
        初始化地铁房源服务
        """
        # 查询地铁站接口
        self.api_subway_url = f"{settings.HOUSE_API_BASE_URL}{settings.HOUSE_SUBWAY_API}"

    async def search_subway_houses(self, subway_line: str, type_value: Any) -> Dict[str, Any]:
        """
        查询地铁线下的站点信息

        Args:
            subway_line: 地铁线名称
            type_value: 租住类型

        Returns:
            站点信息列表
        """
        try:
            async with httpx.AsyncClient() as client:
                params = {
                    "city_code": settings.DEFAULT_CITY_CODE,
                    "subway_line": subway_line,
                    "product_category": int(type_value) if isinstance(type_value, (str, int)) else 1
                }

                logger.info(f"Calling subway API with params: {params}")

                response = await client.get(
                    self.api_subway_url,
                    params=params,
                    timeout=10.0
                )

                if response.status_code != 200:
                    logger.error(f"Subway API error: {response.status_code} - {response.text}")
                    return {"error": f"Subway API error: {response.status_code}"}

                data = response.json()
                logger.debug(f"Subway API response: {data}")

                return data

        except Exception as e:
            logger.exception(f"Error searching subway houses: {str(e)}")
            return {"error": f"Error searching subway houses: {str(e)}"} 