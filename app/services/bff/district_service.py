"""
行政区房源服务模块

提供行政区房源相关的功能，包括行政区房源搜索等。
"""

import logging
from typing import Dict, Any

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

class DistrictHouseService:
    """
    行政区房源服务

    负责处理行政区房源相关的功能。
    """

    def __init__(self):
        """
        初始化行政区房源服务
        """
        # 查询商圈接口
        self.api_biz_url = f"{settings.HOUSE_API_BASE_URL}{settings.HOUSE_BIZCIRCLES_API}"

    async def search_district_houses(self, district_code: str, type_value: Any) -> Dict[str, Any]:
        """
        查询行政区下的商圈信息

        Args:
            district_code: 行政区代码
            type_value: 租住类型

        Returns:
            商圈信息列表
        """
        try:
            async with httpx.AsyncClient() as client:
                params = {
                    "city_code": settings.DEFAULT_CITY_CODE,
                    "district_code": district_code,
                    "type": int(type_value) if isinstance(type_value, (str, int)) else None
                }

                logger.info(f"Calling district API with params: {params}")

                response = await client.get(
                    self.api_biz_url,
                    params=params,
                    timeout=10.0
                )

                if response.status_code != 200:
                    logger.error(f"District API error: {response.status_code} - {response.text}")
                    return {"error": f"District API error: {response.status_code}"}

                data = response.json()
                logger.debug(f"District API response: {data}")

                return data

        except Exception as e:
            logger.exception(f"Error searching district houses: {str(e)}")
            return {"error": f"Error searching district houses: {str(e)}"} 
