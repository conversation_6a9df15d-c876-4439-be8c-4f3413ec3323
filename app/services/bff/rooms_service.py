"""
房源服务模块

提供房源相关的功能，包括房源搜索等。
"""

import logging
from typing import Dict, Any

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

class RoomsService:
    """
    房源服务

    负责处理房源相关的功能。
    """

    def __init__(self):
        """
        初始化房源服务
        """
        # 查询房源接口
        self.api_url = f"{settings.HOUSE_API_BASE_URL}{settings.HOUSE_SEARCH_API}"

    async def search_rooms(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        搜索房源

        Args:
            params: 搜索参数

        Returns:
            房源信息列表
        """
        try:
            async with httpx.AsyncClient() as client:
                logger.info(f"Calling rooms API with params: {params}")

                response = await client.post(
                    self.api_url,
                    json=params,
                    timeout=10.0
                )

                if response.status_code != 200:
                    logger.error(f"Rooms API error: {response.status_code} - {response.text}")
                    return {"error": f"Rooms API error: {response.status_code}"}

                data = response.json()
                logger.debug(f"Rooms API response: {data}")

                return data

        except Exception as e:
            logger.exception(f"Error searching rooms: {str(e)}")
            return {"error": f"Error searching rooms: {str(e)}"} 