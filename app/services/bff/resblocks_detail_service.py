"""
小区详情服务模块

提供小区详情相关的功能，包括获取小区详细信息等。
"""

import logging
from typing import Dict, Any, Optional

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

class ResblocksDetailService:
    """
    小区详情服务

    负责处理小区详情相关的功能。
    """

    def __init__(self):
        """
        初始化小区详情服务
        """
        # 查询小区详情接口
        self.api_detail_url = f"{settings.HOUSE_MARS_API_BASE_URL}{settings.HOUSE_RESBLOCKS_DETAIL_API}"

    async def get_resblock_detail(self, resblock_id: str) -> Dict[str, Any]:
        """
        获取小区详情

        Args:
            resblock_id: 小区ID

        """
        try:
            async with httpx.AsyncClient() as client:
                params = {
                    "resblockId": resblock_id
                }

                logger.info(f"Calling resblock detail API with params: {params}")

                response = await client.get(
                    self.api_detail_url,
                    params=params,
                    timeout=10.0
                )

                if response.status_code != 200:
                    logger.error(f"Resblock detail API error: {response.status_code} - {response.text}")
                    return {"error": f"Resblock detail API error: {response.status_code}"}

                data = response.json()
                logger.debug(f"Resblock detail API response: {data}")

                if not data.get("success"):
                    logger.error(f"Resblock detail API returned error: {data.get('message')}")
                    return {"error": data.get("message", "Unknown error")}

                return data.get("data", {})

        except Exception as e:
            logger.exception(f"Error getting resblock detail: {str(e)}")
            return {"error": f"Error getting resblock detail: {str(e)}"} 