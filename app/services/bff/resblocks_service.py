"""
小区房源服务模块

提供小区房源相关的功能，包括小区房源搜索等。
"""

import logging
from typing import Dict, Any

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

class ResblocksService:
    """
    小区房源服务

    负责处理小区房源相关的功能。
    """

    def __init__(self):
        """
        初始化小区房源服务
        """
        # 查询附近小区api
        self.api_resblocks_url = f"{settings.HOUSE_API_BASE_URL}{settings.HOUSE_RESBLOCKS_API}"

    async def search_resblocks(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        搜索小区房源

        Args:
            params: 搜索参数

        Returns:
            小区房源信息列表
        """
        try:
            async with httpx.AsyncClient() as client:
                logger.info(f"Calling resblocks API with params: {params}")

                response = await client.get(
                    self.api_resblocks_url,
                    params=params,
                    timeout=10.0
                )

                if response.status_code != 200:
                    logger.error(f"Resblocks API error: {response.status_code} - {response.text}")
                    return {"error": f"Resblocks API error: {response.status_code}"}

                data = response.json()
                logger.debug(f"Resblocks API response: {data}")

                return data

        except Exception as e:
            logger.exception(f"Error searching resblocks: {str(e)}")
            return {"error": f"Error searching resblocks: {str(e)}"} 