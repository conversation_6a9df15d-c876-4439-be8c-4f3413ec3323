"""
房源预约服务模块

提供创建看房预约单的功能。
"""

import logging
import httpx
from typing import Dict, Any, List

from app.core.config import settings

logger = logging.getLogger(__name__)

class ReservationService:
    """
    房源预约服务

    负责处理房源预约相关的功能。
    """

    def __init__(self):
        """
        初始化房源预约服务
        """
        # 创建预约单接口
        self.api_reserve_url = f"{settings.HOUSE_API_BASE_URL}{settings.HOUSE_RESERVE_API}"

    async def create_reservation(
        self,
        token: str,
        expect_time: str,
        house_list: List[Dict[str, int]]
    ) -> Dict[str, Any]:
        """
        创建看房预约单

        Args:
            token: 用户token
            city_code: 城市代码 110000
            expect_time: 期望看房时间 2025-05-29 12:00:00
            house_list: 房源列表，每个房源包含resblockId和houseId {"resblockId":1111027381821,"houseId":774702416}

        Returns:
            预约结果，包含管家信息
        """
        try:
            async with httpx.AsyncClient() as client:
                params = {
                    "token": token,
                    "cityCode": "110000",
                    "expectTime": expect_time,
                    # "houseList": house_list,
                    "houseList": [{"resblockId":1111027381821,"houseId":774702416}]
                }

                logger.info(f"Calling reservation API with params: {params}")

                response = await client.post(
                    self.api_reserve_url,
                    json=params,
                    timeout=10.0
                )

                if response.status_code != 200:
                    logger.error(f"Reservation API error: {response.status_code} - {response.text}")
                    return {"error": f"Reservation API error: {response.status_code}"}

                data = response.json()
                logger.debug(f"Reservation API response: {data}")

                # 判断API调用是否成功，成功则返回完整数据
                if data.get("code") == 200:
                    return data
                else:
                    return {
                        "code": data.get("code", 500),
                        "message": data.get("message", "预约失败"),
                        "error": "预约API调用失败"
                    }

        except Exception as e:
            logger.exception(f"Error creating reservation: {str(e)}")
            return {"error": f"Error creating reservation: {str(e)}"} 