import json
import logging
from typing import Dict, Any, List, Optional, Set
from enum import Enum

logger = logging.getLogger(__name__)

class SessionState(str, Enum):
    """会话状态枚举"""
    IDLE = "idle"                   # 空闲状态
    PROCESSING = "processing"       # 处理中
    ASKING_LOCATION = "asking_location"  # 询问位置
    ASKING_BUDGET = "asking_budget"     # 询问预算
    ASKING_ROOM_TYPE = "asking_room_type"  # 询问租房类型
    BROWSING = "browsing"           # 浏览结果中

class ContextService:
    """上下文管理服务"""


    # 关键参数及其询问标记
    KEY_PARAMS = {
        "location": "location_asked",  # 位置
        "budget": "budget_asked",      # 预算
        "room_type": "room_type_asked"  # 租房类型（整租/合租）
    }

    def initialize_context(self) -> Dict[str, Any]:
        """初始化上下文"""
        context = {
            # 会话状态
            "session_state": SessionState.IDLE,

            # 追问标记
            "location_asked": 0,
            "budget_asked": 0,
            "room_type_asked": 0,

            # 分页
            "current_page": 1,

            # 默认值
            "room_or_resblock": "room",
           
        }
        return context


    @staticmethod
    def update_context(context: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新上下文"""
        # 记录更新内容
        logger.info(f"更新上下文: 原始上下文={context}, 更新内容={updates}")

        # 处理枚举值
        for key, value in updates.items():
            if isinstance(value, Enum):
                updates[key] = value.value
                logger.info(f"将枚举值转换为字符串: {key}={value} -> {value.value}")

        context.update(updates)
        logger.info(f"更新后的上下文: {context}")
        return context



