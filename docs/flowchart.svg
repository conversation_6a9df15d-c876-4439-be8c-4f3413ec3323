<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .api-box {
        fill: #d1e7dd;
        stroke: #0d6832;
      }
      .service-box {
        fill: #cfe2ff;
        stroke: #084298;
      }
      .workflow-box {
        fill: #fff3cd;
        stroke: #664d03;
      }
      .llm-box {
        fill: #f8d7da;
        stroke: #842029;
      }
      .external-box {
        fill: #e2e3e5;
        stroke: #41464b;
      }
      .arrow {
        stroke: #333;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .text {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
        dominant-baseline: middle;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 24px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 16px;
        font-style: italic;
        text-anchor: middle;
      }
      .small-text {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="450" y="30" class="title">AI House Search System Architecture</text>
  <text x="450" y="55" class="subtitle">Workflow and Component Interaction</text>

  <!-- User Interface -->
  <rect x="50" y="100" width="120" height="60" class="box" />
  <text x="110" y="130" class="text">User Interface</text>
  
  <!-- API Layer -->
  <rect x="250" y="100" width="120" height="60" class="box api-box" />
  <text x="310" y="130" class="text">API Layer</text>
  <text x="310" y="150" class="small-text">/api/chat</text>
  
  <!-- Chat Service -->
  <rect x="450" y="100" width="120" height="60" class="box service-box" />
  <text x="510" y="130" class="text">Chat Service</text>
  
  <!-- Workflow Factory -->
  <rect x="650" y="100" width="120" height="60" class="box workflow-box" />
  <text x="710" y="130" class="text">Workflow Factory</text>

  <!-- Session Service -->
  <rect x="250" y="220" width="120" height="60" class="box service-box" />
  <text x="310" y="250" class="text">Session Service</text>
  
  <!-- Intent Recognition -->
  <rect x="450" y="220" width="120" height="60" class="box llm-box" />
  <text x="510" y="250" class="text">Intent Recognition</text>
  
  <!-- Parameter Extraction -->
  <rect x="650" y="220" width="120" height="60" class="box llm-box" />
  <text x="710" y="250" class="text">Parameter Extraction</text>

  <!-- Workflow Router -->
  <rect x="450" y="340" width="120" height="60" class="box workflow-box" />
  <text x="510" y="370" class="text">Workflow Router</text>

  <!-- Workflows -->
  <rect x="250" y="460" width="120" height="60" class="box workflow-box" />
  <text x="310" y="490" class="text">Search Workflow</text>
  
  <rect x="450" y="460" width="120" height="60" class="box workflow-box" />
  <text x="510" y="490" class="text">Ask Parameters</text>
  
  <rect x="650" y="460" width="120" height="60" class="box workflow-box" />
  <text x="710" y="490" class="text">General Workflow</text>

  <!-- House Service -->
  <rect x="250" y="580" width="120" height="60" class="box service-box" />
  <text x="310" y="610" class="text">House Service</text>
  
  <!-- Response Generator -->
  <rect x="450" y="580" width="120" height="60" class="box llm-box" />
  <text x="510" y="610" class="text">Response Generator</text>
  
  <!-- External API -->
  <rect x="650" y="580" width="120" height="60" class="box external-box" />
  <text x="710" y="610" class="text">BFF API Layer</text>

  <!-- Arrows -->
  <!-- User to API -->
  <path d="M 170 130 L 250 130" class="arrow" />
  
  <!-- API to Chat Service -->
  <path d="M 370 130 L 450 130" class="arrow" />
  
  <!-- Chat Service to Workflow Factory -->
  <path d="M 570 130 L 650 130" class="arrow" />
  
  <!-- Workflow Factory to Session Service -->
  <path d="M 710 160 L 710 190 L 310 190 L 310 220" class="arrow" />
  
  <!-- Workflow Factory to Intent Recognition -->
  <path d="M 710 160 L 710 190 L 510 190 L 510 220" class="arrow" />
  
  <!-- Workflow Factory to Parameter Extraction -->
  <path d="M 710 160 L 710 220" class="arrow" />
  
  <!-- Session Service to Workflow Router -->
  <path d="M 310 280 L 310 370 L 450 370" class="arrow" />
  
  <!-- Intent Recognition to Workflow Router -->
  <path d="M 510 280 L 510 340" class="arrow" />
  
  <!-- Parameter Extraction to Workflow Router -->
  <path d="M 710 280 L 710 370 L 570 370" class="arrow" />
  
  <!-- Workflow Router to Search Workflow -->
  <path d="M 510 400 L 510 430 L 310 430 L 310 460" class="arrow" />
  
  <!-- Workflow Router to Ask Parameters -->
  <path d="M 510 400 L 510 460" class="arrow" />
  
  <!-- Workflow Router to General Workflow -->
  <path d="M 510 400 L 510 430 L 710 430 L 710 460" class="arrow" />
  
  <!-- Search Workflow to House Service -->
  <path d="M 310 520 L 310 580" class="arrow" />
  
  <!-- Search Workflow to Response Generator -->
  <path d="M 310 520 L 310 550 L 510 550 L 510 580" class="arrow" />
  
  <!-- Ask Parameters to Response Generator -->
  <path d="M 510 520 L 510 580" class="arrow" />
  
  <!-- General Workflow to Response Generator -->
  <path d="M 710 520 L 710 550 L 510 550 L 510 580" class="arrow" />
  
  <!-- House Service to External API -->
  <path d="M 370 610 L 450 610" class="arrow" />
  
  <!-- Response Generator to External API -->
  <path d="M 570 610 L 650 610" class="arrow" />
</svg>
