<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .step-box {
        fill: #fff3cd;
        stroke: #664d03;
      }
      .decision-box {
        fill: #cfe2ff;
        stroke: #084298;
      }
      .action-box {
        fill: #d1e7dd;
        stroke: #0d6832;
      }
      .llm-box {
        fill: #f8d7da;
        stroke: #842029;
      }
      .api-box {
        fill: #e2e3e5;
        stroke: #41464b;
      }
      .arrow {
        stroke: #333;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .dashed-arrow {
        stroke: #333;
        stroke-width: 2;
        stroke-dasharray: 5,5;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .text {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
        dominant-baseline: middle;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 24px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 16px;
        font-style: italic;
        text-anchor: middle;
      }
      .small-text {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
      .label {
        font-family: Arial, sans-serif;
        font-size: 12px;
        fill: #555;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="450" y="30" class="title">AI House Search Workflow</text>
  <text x="450" y="55" class="subtitle">Step-by-Step Process Flow</text>

  <!-- Start -->
  <rect x="400" y="80" width="100" height="40" rx="20" ry="20" fill="#4CAF50" stroke="#333" stroke-width="2" />
  <text x="450" y="100" class="text" fill="white">Start</text>

  <!-- Get Session -->
  <rect x="380" y="150" width="140" height="50" class="box step-box" />
  <text x="450" y="175" class="text">Get Session</text>

  <!-- Intent Recognition -->
  <rect x="380" y="230" width="140" height="50" class="box llm-box" />
  <text x="450" y="255" class="text">Intent Recognition</text>

  <!-- Parameter Extraction -->
  <rect x="380" y="310" width="140" height="50" class="box llm-box" />
  <text x="450" y="335" class="text">Parameter Extraction</text>

  <!-- Save User Message -->
  <rect x="380" y="390" width="140" height="50" class="box action-box" />
  <text x="450" y="415" class="text">Save User Message</text>

  <!-- Check Missing Params -->
  <rect x="380" y="470" width="140" height="50" class="box decision-box" />
  <text x="450" y="495" class="text">Check Missing Params</text>

  <!-- Decision Diamond - Location -->
  <polygon points="450,550 500,590 450,630 400,590" class="box decision-box" />
  <text x="450" y="590" class="text">Has Location?</text>

  <!-- Decision Diamond - Budget -->
  <polygon points="650,550 700,590 650,630 600,590" class="box decision-box" />
  <text x="650" y="590" class="text">Has Budget?</text>

  <!-- Decision Diamond - Room Type -->
  <polygon points="850,550 900,590 850,630 800,590" class="box decision-box" />
  <text x="850" y="590" class="text">Has Room Type?</text>

  <!-- Ask Location -->
  <rect x="380" y="660" width="140" height="50" class="box action-box" />
  <text x="450" y="685" class="text">Ask Location</text>

  <!-- Ask Budget -->
  <rect x="580" y="660" width="140" height="50" class="box action-box" />
  <text x="650" y="685" class="text">Ask Budget</text>

  <!-- Ask Room Type -->
  <rect x="780" y="660" width="140" height="50" class="box action-box" />
  <text x="850" y="685" class="text">Ask Room Type</text>

  <!-- Search Houses -->
  <rect x="180" y="660" width="140" height="50" class="box api-box" />
  <text x="250" y="685" class="text">Search Houses</text>

  <!-- Generate Response -->
  <rect x="180" y="740" width="140" height="50" class="box llm-box" />
  <text x="250" y="765" class="text">Generate Response</text>

  <!-- End -->
  <rect x="400" y="740" width="100" height="40" rx="20" ry="20" fill="#F44336" stroke="#333" stroke-width="2" />
  <text x="450" y="760" class="text" fill="white">End</text>

  <!-- Arrows -->
  <!-- Start to Get Session -->
  <path d="M 450 120 L 450 150" class="arrow" />
  
  <!-- Get Session to Intent Recognition -->
  <path d="M 450 200 L 450 230" class="arrow" />
  
  <!-- Intent Recognition to Parameter Extraction -->
  <path d="M 450 280 L 450 310" class="arrow" />
  
  <!-- Parameter Extraction to Save User Message -->
  <path d="M 450 360 L 450 390" class="arrow" />
  
  <!-- Save User Message to Check Missing Params -->
  <path d="M 450 440 L 450 470" class="arrow" />
  
  <!-- Check Missing Params to Decision Diamond - Location -->
  <path d="M 450 520 L 450 550" class="arrow" />
  
  <!-- Decision Diamond - Location to Decision Diamond - Budget (Yes) -->
  <path d="M 500 590 L 600 590" class="arrow" />
  <text x="550" y="580" class="label">Yes</text>
  
  <!-- Decision Diamond - Budget to Decision Diamond - Room Type (Yes) -->
  <path d="M 700 590 L 800 590" class="arrow" />
  <text x="750" y="580" class="label">Yes</text>
  
  <!-- Decision Diamond - Location to Ask Location (No) -->
  <path d="M 450 630 L 450 660" class="arrow" />
  <text x="460" y="645" class="label">No</text>
  
  <!-- Decision Diamond - Budget to Ask Budget (No) -->
  <path d="M 650 630 L 650 660" class="arrow" />
  <text x="660" y="645" class="label">No</text>
  
  <!-- Decision Diamond - Room Type to Ask Room Type (No) -->
  <path d="M 850 630 L 850 660" class="arrow" />
  <text x="860" y="645" class="label">No</text>
  
  <!-- Decision Diamond - Room Type to Search Houses (Yes) -->
  <path d="M 850 590 L 850 540 L 250 540 L 250 660" class="arrow" />
  <text x="860" y="560" class="label">Yes</text>
  
  <!-- Search Houses to Generate Response -->
  <path d="M 250 710 L 250 740" class="arrow" />
  
  <!-- Ask Location to End -->
  <path d="M 450 710 L 450 740" class="arrow" />
  
  <!-- Ask Budget to End -->
  <path d="M 650 710 L 650 730 L 500 730 L 500 760" class="dashed-arrow" />
  
  <!-- Ask Room Type to End -->
  <path d="M 850 710 L 850 730 L 500 730 L 500 760" class="dashed-arrow" />
  
  <!-- Generate Response to End -->
  <path d="M 320 765 L 400 760" class="arrow" />
</svg>
