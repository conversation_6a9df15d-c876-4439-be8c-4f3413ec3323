# AI找房系统代码梳理

## 1. 系统架构

AI找房系统采用了分层架构设计，各层次职责清晰，代码结构模块化。整体架构如下：

```
┌─────────────┐
│    API层    │ 处理HTTP请求和响应
├─────────────┤
│   服务层    │ 实现业务逻辑
├─────────────┤
│  LLM集成层  │ 与大型语言模型交互
├─────────────┤
│   数据层    │ 数据存储和访问
└─────────────┘
```

## 2. 目录结构

主要目录结构及其功能：

```
ai_house_search/
├── app/                      # 主应用目录
│   ├── api/                  # API层 - 处理HTTP请求
│   ├── services/             # 服务层 - 业务逻辑
│   │   ├── house/            # 房源服务相关实现
│   │   │   ├── searcher.py   # 房源搜索器
│   │   │   └── formatter.py  # 房源格式化器
│   ├── llm/                  # LLM集成 - AI模型交互
│   │   ├── intent/           # 意图识别
│   │   ├── extraction/       # 参数提取
│   │   └── prompts/          # 提示模板
│   ├── db/                   # 数据层 - 数据存储与访问
│   ├── core/                 # 核心配置
│   └── utils/                # 工具函数
├── static/                   # 静态资源
└── templates/                # 前端模板
```

## 3. 核心模块详解

### 3.1 API层 (app/api/)

API层负责处理HTTP请求，主要包含：

- **chat.py**: 聊天相关API接口
  - `/api/chat/message` - 发送消息
  - `/api/chat/stream` - 流式响应
  
- **session.py**: 会话管理API接口
  - 创建、更新、获取会话
  - 会话上下文管理

### 3.2 服务层 (app/services/)

服务层实现核心业务逻辑：

- **chat_service.py**: 聊天服务
  - 消息处理
  - 调用意图识别
  - 调用参数提取
  - 生成响应

- **session_service.py**: 会话服务
  - 会话管理
  - 上下文追踪
  - 会话状态控制

- **house_service.py**: 房源服务（门面模式）
  - 封装房源搜索和格式化功能
  - 提供统一接口
  - 委托具体实现给专门的类

- **house/searcher.py**: 房源搜索器
  - 处理不同位置类型的搜索参数
  - 与BFF层API交互
  - 支持小区、地铁站、地铁线、行政区、商圈等多种位置类型
  - 处理价格、户型、面积等筛选条件
  - 分页搜索结果处理

- **house/formatter.py**: 房源格式化器
  - 将API返回数据格式化为用户友好的文本
  - 生成JSON格式的看房单
  - 处理不同格式的展示需求

- **context_service.py**: 上下文服务
  - 对话上下文管理
  - 会话状态追踪

### 3.3 LLM集成层 (app/llm/)

LLM集成层负责与AI模型交互：

- **client.py**: OpenAI客户端封装
  - 封装对OpenAI API的调用
  - 处理API请求和响应
  - 错误处理和重试机制

- **intent/recognizer.py**: 意图识别器
  - 使用LLM识别用户意图（找房、询问、比较等）
  - 基于特定提示模板进行意图分类
  - 支持多种意图类型的识别

- **extraction/**: 参数提取模块
  - **extractor.py**: 参数提取器
    - 从用户输入中提取关键找房参数
    - 支持提取位置、预算、户型等多种参数
    - 基于LLM的结构化信息提取

  - **location_type_recognizer.py**: 位置类型识别器
    - 识别用户提及的位置类型
    - 支持地铁站、地铁线、行政区、商圈、小区等多种位置类型
    - 使用规则和模式匹配进行识别
    - 处理各种模糊表达（如"附近"、"周边"等）
    - 支持通勤场景的位置识别

- **response.py**: 响应生成
  - 基于用户意图和上下文生成自然语言响应
  - 处理不同类型的回复（找房结果、引导提问等）
  - 支持流式响应生成

- **prompts/**: 提示模板（支持版本控制）
  - 意图识别提示模板
  - 参数提取提示模板
  - 响应生成提示模板

### 3.4 数据层 (app/db/)

数据层负责数据存储和访问：

- **models/**: 数据模型定义
  - 会话模型
  - 消息模型
  
- **repositories/**: 数据访问对象
  - 会话数据仓库
  - 消息数据仓库

## 4. 核心流程

### 4.1 对话流程

1. 用户发送消息到 `/api/chat/message`
2. `ChatService` 处理消息
3. 调用 `IntentRecognizer` 识别用户意图
4. 调用 `ParameterExtractor` 提取关键参数
5. 更新会话上下文（`SessionService`）
6. 根据意图执行相应操作（如搜索房源）
7. 生成响应并返回给用户

### 4.2 找房流程

1. 识别用户的找房意图
2. 提取找房参数（位置、预算、户型等）
3. 若参数不足，引导用户提供
4. 调用 `HouseService` 搜索房源
   - `HouseService` 委托 `HouseSearcher` 执行搜索
   - `HouseSearcher` 根据位置类型选择合适的API
   - `HouseSearcher` 构建查询参数并调用BFF层API
   - `HouseSearcher` 处理API响应
5. 格式化房源信息
   - `HouseService` 委托 `HouseFormatter` 格式化结果
   - 生成结构化的房源信息
6. 返回找房结果给用户

### 4.3 位置处理流程

1. 用户提供位置信息（如"望京附近"、"14号线"等）
2. `LocationTypeRecognizer` 识别位置类型
   - 匹配地铁站、地铁线、行政区、商圈、小区等位置类型
   - 处理"附近"、"周边"等模糊表达
   - 处理通勤需求（如"到百度公司20分钟内"）
3. 根据位置类型执行不同的搜索策略
   - 小区：直接搜索该小区的房源
   - 地铁线：搜索沿线站点的房源
   - 商圈：搜索商圈内的房源
   - 通勤：计算通勤距离，搜索满足通勤需求的房源

## 5. 技术特点

### 5.1 提示模板版本控制

系统支持不同版本的提示模板，便于迭代优化：

- 意图识别提示模板
- 参数提取提示模板
- 响应生成提示模板

每个模板类型支持多个版本，通过配置选择使用的版本：

```python
# app/core/config.py
INTENT_PROMPT_VERSION: str = "v1"
EXTRACTION_PROMPT_VERSION: str = "v1"
RESPONSE_PROMPT_VERSION: str = "v1"
```

### 5.2 流式响应

支持类似OpenAI的流式SSE响应，提升用户体验：

```python
# app/api/chat.py
@router.post("/stream", response_model=None)
async def stream_chat(request: Request, message: ChatMessage):
    # 流式处理逻辑
    # ...
    return StreamingResponse(stream_generator(), media_type="text/event-stream")
```

### 5.3 上下文管理

灵活的上下文管理机制，支持：

- 追踪对话状态
- 记住用户偏好
- 适时清除过期上下文

```python
# app/services/context_service.py
CLEAR_ON_NEW_DIALOG = {
    "location_asked", "budget_asked", "room_type_asked",
    "company_asked", "region_asked", "current_page"
}
```

### 5.4 错误处理

全局异常处理机制，确保系统稳定运行：

```python
# app/main.py
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )
```

## 6. 代码设计原则

### 6.1 单一职责原则

每个模块专注于单一功能，如：
- `HouseService` 专注于房源搜索
- `SessionService` 专注于会话管理
- `LocationTypeRecognizer` 专注于位置类型识别

### 6.2 依赖注入

通过构造函数注入依赖，提高代码可测试性：

```python
def __init__(self, location_recognizer: Optional[LocationTypeRecognizer] = None, 
             session_service: Optional[SessionService] = None):
    self.location_recognizer = location_recognizer or LocationTypeRecognizer()
    self.session_service = session_service
```

### 6.3 接口分离

不同层次间通过明确的接口通信，降低耦合度。例如：
- API层通过服务层提供的方法调用业务逻辑
- 服务层通过LLM集成层提供的抽象接口调用AI能力

### 6.4 异常处理

各层统一的异常处理机制，提高系统稳定性：
- API层全局异常处理
- 服务层方法内的try-except捕获
- 日志记录机制

### 6.5 门面模式

使用门面模式简化复杂子系统的访问：
- `HouseService` 作为门面，封装了 `HouseSearcher` 和 `HouseFormatter` 的细节
- 客户端只需与门面交互，无需了解内部实现

## 7. 扩展与优化方向

### 7.1 支持更多位置类型

当前系统已支持多种位置类型，可进一步扩展：
- 支持更多POI类型
- 增强通勤场景识别
- 支持多位置组合查询

### 7.2 增强对话理解能力

- 提升意图识别准确率
- 增强对模糊表达的理解
- 支持多轮对话中的指代消解

### 7.3 性能优化

- 缓存常用查询结果
- 优化API调用频率
- 提高流式响应效率 