# 数据库连接池问题故障排除指南

## 问题描述
在生产环境中出现 `SQL connection timeout` 和连接池耗尽的问题。

## 根本原因分析

### 1. 连接池配置不当
- **原始问题**: 连接池大小设置过大（40个连接），导致数据库服务器压力过大
- **解决方案**: 优化连接池配置，减少基础连接数，增加连接回收频率

### 2. 连接生命周期管理问题
- **原始问题**: 异步数据库会话在异常情况下可能没有正确关闭
- **解决方案**: 重构 `get_async_db()` 函数，添加更严格的异常处理和连接清理

### 3. 依赖注入设计缺陷
- **原始问题**: 每次请求都创建新的服务实例，但连接管理不当
- **解决方案**: 优化依赖注入，确保服务实例的正确生命周期管理

## 解决方案实施

### 1. 连接池配置优化

```python
# 优化后的配置
async_engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,           # 减少基础连接池大小
    max_overflow=30,        # 适中的溢出连接数
    pool_timeout=60,        # 快速失败，避免长时间等待
    pool_recycle=3600,      # 1小时回收连接
    pool_pre_ping=True,     # 使用前测试连接有效性
    connect_args={
        "connect_timeout": 10,  # 连接超时
        "read_timeout": 30,     # 读取超时
        "write_timeout": 30,    # 写入超时
    }
)
```

### 2. 连接监控和日志

添加了全面的连接池监控：
- 连接创建/销毁监控
- 连接使用率监控
- 连接池状态健康检查

### 3. 中间件保护

实施了数据库连接监控中间件：
- 请求级别的连接池状态检查
- 异常情况下的连接清理
- 详细的请求处理日志

## 监控和维护

### 1. 健康检查端点

访问 `/health/db` 查看连接池状态：

```json
{
    "status": "healthy",
    "pool_status": {
        "pool_size": 20,
        "checked_out": 5,
        "overflow": 0,
        "checked_in": 15
    },
    "usage_rate": "25.00%",
    "message": "连接池使用率: 25.00%"
}
```

### 2. 实时监控脚本

使用监控脚本实时查看连接池状态：

```bash
python scripts/monitor_db_pool.py 5
```

### 3. 日志监控

关键日志指标：
- `连接池使用率过高`: 使用率超过80%时的警告
- `连接创建/归还`: 连接生命周期跟踪
- `连接失效`: 连接异常情况

## 部署最佳实践

### 1. 环境配置

使用 `deployment/production.env` 配置文件：
- 合理的连接池参数
- 适当的超时设置
- 监控和日志配置

### 2. 进程管理

推荐的 Uvicorn 启动参数：
```bash
uvicorn app.main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker
```

### 3. 负载均衡配置

配置负载均衡器健康检查：
- 主健康检查: `/health`
- 数据库健康检查: `/health/db`
- 检查间隔: 30秒
- 失败阈值: 3次

## 故障排除步骤

### 1. 立即检查

1. 访问 `/health/db` 查看连接池状态
2. 检查应用日志中的连接池警告
3. 运行监控脚本查看实时状态

### 2. 临时缓解

如果连接池耗尽：
1. 重启应用服务
2. 检查数据库服务器状态
3. 临时减少并发请求

### 3. 长期解决

1. 分析连接使用模式
2. 调整连接池参数
3. 优化数据库查询
4. 考虑连接池分片

## 预防措施

### 1. 监控告警

设置以下告警：
- 连接池使用率 > 80%
- 连接获取超时
- 数据库连接异常

### 2. 定期维护

- 每周检查连接池使用统计
- 每月分析数据库性能
- 季度评估连接池配置

### 3. 压力测试

定期进行压力测试：
- 模拟高并发场景
- 测试连接池极限
- 验证故障恢复能力

## 常见问题 FAQ

### Q: 为什么减少连接池大小反而能解决问题？
A: 过大的连接池会给数据库服务器造成压力，导致连接质量下降。适中的连接池配合快速回收更加稳定。

### Q: 如何确定合适的连接池大小？
A: 一般公式：`pool_size = 并发用户数 / 平均请求处理时间 * 数据库操作比例`

### Q: 什么时候需要增加连接池大小？
A: 当连接池使用率持续超过80%，且响应时间正常时，可以考虑适当增加。

### Q: 如何处理连接泄漏？
A: 通过监控日志找到未正确关闭的连接，检查相关代码的异常处理逻辑。

## 联系支持

如果问题持续存在，请提供以下信息：
1. `/health/db` 端点的输出
2. 应用日志（特别是连接池相关日志）
3. 数据库服务器状态
4. 并发用户数和请求模式 