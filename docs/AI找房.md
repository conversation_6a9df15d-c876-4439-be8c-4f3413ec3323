## AI 找房技术文档

### 1. 引言

本项目旨在设计一个基于人工智能的对话系统，帮助用户通过自然语言交互来寻找合适的房源。系统能够理解用户的意图（例如找房、询问特定信息），提取关键参数（如地段、预算、户型等），并在多轮对话中保持上下文连贯性。为了实现这一目标，我们将利用 OpenAI 的大型语言模型 (LLM) 进行自然语言理解和生成，并使用 SQLite 数据库来存储会话信息。

本文档将详细阐述系统的各个技术环节，包括意图识别、参数提取、会话管理、数据存储以及与 LLM 的集成方式。

### 2. 核心流程概述

AI 找房系统的核心工作流程可以概括为以下几个步骤：

1.  **接收用户输入**: 系统接收用户通过文本发送的找房请求或问题。
2.  **意图识别**: 利用 LLM 分析用户输入，判断用户的主要意图（是想找房？还是询问某个细节？）。
3.  **参数提取**: 如果用户意图是找房，利用 LLM 从用户输入中提取关键信息，如期望的地段、价格范围、房间数量等。
4.  **会话上下文更新**: 将识别出的意图和提取出的参数更新到当前用户的会话上下文中。
5.  **会话存储**: 将最新的用户输入、AI 回复、意图和上下文参数存储到 SQLite 数据库中，以便后续追踪和恢复对话。
6.  **(可选) 业务逻辑处理**: 根据提取的参数，可能需要查询房源数据库或执行其他业务操作。
7.  **生成回复**: 结合当前会话上下文和业务逻辑处理结果（如果有），利用 LLM 生成自然、相关的回复给用户。
8.  **返回回复**: 将生成的回复展示给用户。

以下是该流程的可视化表示：

<svg width="600" height="550" xmlns="http://www.w3.org/2000/svg" font-family="sans-serif" font-size="14">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- Nodes -->
  <rect id="start" x="250" y="20" width="100" height="40" rx="20" ry="20" fill="#f0f0f0" stroke="#333" stroke-width="1"/>
  <text x="300" y="45" text-anchor="middle">开始</text>

  <rect id="user_input" x="225" y="90" width="150" height="40" fill="#e0f7fa" stroke="#333" stroke-width="1"/>
  <text x="300" y="115" text-anchor="middle">用户输入</text>

  <rect id="intent_rec" x="200" y="160" width="200" height="40" fill="#fff9c4" stroke="#333" stroke-width="1"/>
  <text x="300" y="185" text-anchor="middle">意图识别 (LLM)</text>

  <rect id="param_ext" x="200" y="230" width="200" height="40" fill="#fff9c4" stroke="#333" stroke-width="1"/>
  <text x="300" y="255" text-anchor="middle">参数提取 (LLM)</text>

  <rect id="update_ctx" x="175" y="300" width="250" height="40" fill="#c8e6c9" stroke="#333" stroke-width="1"/>
  <text x="300" y="325" text-anchor="middle">更新/管理会话上下文</text>

  <rect id="store_session" x="175" y="370" width="250" height="40" fill="#d1c4e9" stroke="#333" stroke-width="1"/>
  <text x="300" y="395" text-anchor="middle">存储会话 (SQLite)</text>

  <rect id="gen_resp" x="200" y="440" width="200" height="40" fill="#fff9c4" stroke="#333" stroke-width="1"/>
  <text x="300" y="465" text-anchor="middle">生成回复 (LLM)</text>

  <rect id="ai_output" x="225" y="510" width="150" height="40" fill="#e0f7fa" stroke="#333" stroke-width="1"/>
  <text x="300" y="535" text-anchor="middle">AI 输出</text>

  <!-- Edges -->
  <line x1="300" y1="60" x2="300" y2="90" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="130" x2="300" y2="160" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="200" x2="300" y2="230" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="270" x2="300" y2="300" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="340" x2="300" y2="370" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="410" x2="300" y2="440" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <line x1="300" y1="480" x2="300" y2="510" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>

</svg>

### 3. 意图识别 (Intent Recognition)

#### 目标

准确判断用户每次输入的真实意图。例如，用户是想开始一次新的找房搜索，还是想在当前搜索基础上增加条件，或者是询问某个房源的细节，甚至是闲聊。

#### 实现方式

主要利用 OpenAI 的 LLM 能力。有两种常见方法：

1.  **Prompt Engineering (提示工程)**: 设计精心构造的提示 (Prompt)，将用户输入和预定义的意图类别一起发送给 LLM，让 LLM 判断属于哪个类别。

    - **优点**: 相对简单，不需要复杂的 API 调用结构。
    - **缺点**: 输出格式可能不够稳定，需要对 LLM 的回复进行后处理。
    - **示例 Prompt**:

      ```
      请判断以下用户输入的意图，从以下类别中选择一个：[开始找房,  уточнить条件, 查询细节, 闲聊]

      用户输入: "我想在朝阳区找个两居室"
      意图:
      ```

      预期 LLM 回复 `开始找房`。

2.  **Function Calling (函数调用)**: 利用 OpenAI API 的函数调用功能。预先定义好不同的意图对应的“函数”及其描述，LLM 会根据用户输入判断应该“调用”哪个函数，从而识别意图。
    - **优点**: 输出格式是结构化的 JSON，方便程序处理，更稳定可靠。
    - **缺点**: 需要更复杂的 API 请求结构。
    - **示例 (伪代码)**:
      定义函数 `start_search(location, type, ...)`、`refine_search(criteria)`、`ask_details(property_id)` 等。
      当用户输入 "我想在朝阳区找个两居室" 时，LLM 可能返回需要调用 `start_search` 函数，并附带提取出的参数（我们将在下一节详述）。

#### 建议

对于需要精确控制流程的场景，推荐使用 **Function Calling**，因为它提供了更可靠的结构化输出。

### 4. 参数提取 (Parameter Extraction)

#### 目标

从用户的自然语言描述中，提取出结构化的找房条件，例如：

- **地段 (Location)**: 如 "朝阳区", "五道口附近"
- **价格范围 (Price Range)**: 如 "月租 5000 到 8000", "总价 300 万以下"
- **户型 (House Type)**: 如 "两居室", "一室一厅", "别墅"
- **面积 (Area)**: 如 "大概 100 平米"
- **其他要求 (Other Requirements)**: 如 "需要有电梯", "南北通透", "靠近地铁站"

#### 实现方式

同样主要依赖 OpenAI LLM 的能力，特别是 **Function Calling** 功能。

1.  **Function Calling**: 这是最适合参数提取的方式。定义一个或多个“函数”，函数的参数就对应我们想要提取的找房条件。

    - **优点**: 直接输出结构化的 JSON，包含提取到的参数名和值，处理非常方便。
    - **缺点**: 需要仔细设计函数签名（参数名、类型、描述）。
    - **示例**:
      定义函数 `find_house(location: string, price_min: number, price_max: number, bedrooms: number, keywords: list[string])`。
      用户输入: "帮我找一个海淀区的房子，预算大概每月 6000 元，要三居室，最好是近地铁。"
      LLM 通过 Function Calling 返回:
      ```json
      {
        "function_name": "find_house",
        "arguments": {
          "location": "海淀区",
          "price_max": 6000,
          "bedrooms": 3,
          "keywords": ["近地铁"]
        }
      }
      ```
      注意：LLM 可能不会提取所有参数，比如 `price_min` 在这个例子中就没有明确给出。我们的程序需要处理这种情况。

2.  **Prompt Engineering + Structured Output**: 也可以通过提示工程，要求 LLM 按照指定的 JSON 格式输出提取到的参数。
    - **优点**: 相对 Function Calling 可能更灵活一些。
    - **缺点**: 输出格式的稳定性可能稍差于 Function Calling，仍需后处理和校验。

#### 建议

强烈推荐使用 **Function Calling** 来实现参数提取，以获得稳定、结构化的数据。

### 5. 会话上下文管理 (Session Context Management)

#### 目标

让 AI 能够“记住”之前的对话内容和用户已经确认的找房条件，使得对话能够连贯进行。例如，用户先说了地段，下一句再说预算，AI 应该能结合这两个信息。

#### 实现方式

通过维护一组 **会话变量 (Session Variables)** 来实现。这些变量存储了当前对话状态下的关键信息。

1.  **定义会话变量**: 根据业务需求，定义需要跟踪的变量。例如：

    - `session_id`: 唯一标识当前会话。
    - `user_id`: 标识用户（如果需要支持多用户）。
    - `current_intent`: 当前识别的用户意图。
    - `search_parameters`: 一个字典或对象，存储已提取并确认的找房参数，如：
      - `location`
      - `price_min`
      - `price_max`
      - `bedrooms`
      - `keywords`
      - ...
    - `conversation_history`: 存储最近几轮的对话记录（用户输入和 AI 回复），用于提供给 LLM 作为上下文。

2.  **更新逻辑**:
    - 每次用户输入后，进行意图识别和参数提取。
    - 将提取到的新参数更新（或覆盖）到 `search_parameters` 中。例如，用户先说 "找朝阳的房子"，`search_parameters.location` 更新为 "朝阳区"；用户再说 "预算 8000"，则 `search_parameters.price_max` 更新为 8000。
    - 将最新的用户输入和 AI 回复追加到 `conversation_history` 中（可能需要限制历史记录的长度，避免超出 LLM 的 token 限制）。

#### 示例

假设用户的对话过程：

1.  用户: "我想找个望京附近的房子"
    - 意图: `开始找房`
    - 参数: `location = "望京"`
    - 会话变量更新: `search_parameters = {"location": "望京"}`, `conversation_history` 追加记录。
2.  用户: "要两居室，月租 9000 以内"
    - 意图: ` уточнить条件`
    - 参数: `bedrooms = 2`, `price_max = 9000`
    - 会话变量更新: `search_parameters = {"location": "望京", "bedrooms": 2, "price_max": 9000}`, `conversation_history` 追加记录。

在生成下一次回复时，将 `conversation_history` 和 `search_parameters` 提供给 LLM，让它了解当前的完整情况。

### 6. 会话存储 (Session Storage)

#### 目标

将用户的会话状态（包括对话历史、上下文变量等）持久化存储，以便：

- 用户下次回来时可以继续之前的对话。
- 系统重启后能恢复状态。
- 进行数据分析和模型优化。

#### 技术选型

**SQLite**: 一个轻量级的、基于文件的关系型数据库。

- **优点**:
  - 配置简单，不需要单独的数据库服务器。
  - 单个文件存储，易于备份和迁移。
  - 对于中小型应用或原型开发足够使用。
- **缺点**:
  - 并发写入性能有限（对于高并发场景可能不是最佳选择）。
  - 功能相比大型数据库（如 PostgreSQL, MySQL）较少。

对于 AI 找房这种场景，如果并发量不是特别巨大，SQLite 是一个非常合适的选择。

#### 数据库表结构设计 (Schema)

我们可以设计以下几张表：

1.  **`sessions` 表**: 存储每个会话的基本信息和当前状态。

    ```sql
    CREATE TABLE IF NOT EXISTS sessions (
        session_id TEXT PRIMARY KEY,  -- 会话唯一 ID (例如 UUID)
        user_id TEXT,               -- 用户 ID (可选)
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 最后更新时间
        -- 存储 JSON 格式的搜索参数
        search_parameters TEXT       -- e.g., '{"location": "望京", "price_max": 9000}'
    );
    ```

2.  **`messages` 表**: 存储每一轮的对话消息。
    ```sql
    CREATE TABLE IF NOT EXISTS messages (
        message_id INTEGER PRIMARY KEY AUTOINCREMENT, -- 消息唯一 ID
        session_id TEXT NOT NULL,                  -- 外键关联 sessions 表
        role TEXT NOT NULL,                         -- 角色 ('user' 或 'assistant')
        content TEXT NOT NULL,                      -- 消息内容
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 消息时间戳
        intent TEXT,                                -- 该消息识别出的意图 (可选, 主要用于 user 消息)
        extracted_params TEXT,                      -- 该消息提取出的参数 (可选, JSON 格式)
        FOREIGN KEY (session_id) REFERENCES sessions (session_id)
    );
    ```

#### 交互逻辑

1.  **新会话开始**:
    - 生成一个新的 `session_id`。
    - 在 `sessions` 表中插入一条新记录。
2.  **处理用户输入**:
    - 根据 `session_id` 从 `sessions` 表加载 `search_parameters`。
    - 从 `messages` 表加载最近的 `conversation_history`。
    - 进行意图识别和参数提取。
    - 将用户输入作为一条新消息插入 `messages` 表（包含识别的意图和提取的参数）。
    - 更新 `search_parameters` 并更新 `sessions` 表中对应记录。
3.  **生成 AI 回复**:
    - 生成回复后，将 AI 回复作为一条新消息插入 `messages` 表。
    - 更新 `sessions` 表的 `updated_at` 时间戳。

### 7. LLM 集成 (LLM Integration)

#### 如何与 OpenAI API 交互

整个系统的核心是与 OpenAI 的 API (例如 GPT-4, GPT-3.5-turbo) 进行交互。主要交互点包括：

1.  **意图识别**: 将用户输入和可能的意图（或 Function Calling 定义）发送给 API。
2.  **参数提取**: 将用户输入和参数提取的 Function Calling 定义发送给 API。
3.  **生成回复**: 将 `conversation_history`、`search_parameters`（可能格式化成自然语言或作为背景信息）以及相应的指令 (prompt) 发送给 API，让其生成回复。

需要使用 OpenAI 提供的 SDK (例如 Python 的 `openai` 库) 来调用 API。

#### Prompt 设计要点

设计有效的 Prompt 是提升 LLM 效果的关键。

1.  **明确角色**: 告诉 LLM 它扮演的角色，例如 "你是一个专业的房产中介 AI 助手"。
2.  **清晰指令**: 明确告知 LLM 需要完成的任务，例如 "请根据以下对话历史和用户当前的找房条件，生成一个友好且相关的回复"。
3.  **提供上下文**: 将 `conversation_history` 和 `search_parameters` 作为上下文信息提供给 LLM。对于 `search_parameters`，可以考虑将其格式化为易于理解的文本，例如 "用户当前正在寻找位于[地点]、预算在[最低价]到[最高价]之间、需要[卧室数]个卧室的房子，并关注[关键词]这些特点。"
4.  **约束输出格式 (可选)**: 如果需要特定的回复风格或格式，可以在 Prompt 中提出要求。
5.  **利用 Function Calling**: 在进行意图识别和参数提取时，优先使用 Function Calling 来获取结构化数据。

### 8. 总结

本技术文档勾勒了一个 AI 找房系统的基本设计思路。通过结合 OpenAI LLM 的自然语言处理能力和 SQLite 的轻量级数据存储，我们可以构建一个能够理解用户需求、维护对话上下文并提供找房建议的智能助手。核心环节包括意图识别、参数提取、会话管理和数据持久化。
