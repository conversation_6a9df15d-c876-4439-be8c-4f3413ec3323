# SSE 协议返回格式说明

## 1. SSE 协议简介

SSE（Server-Sent Events，服务器推送事件）是一种服务端主动向客户端推送数据的协议，常用于实时数据流场景。
服务端以 `text/event-stream` 格式持续输出数据，客户端（如浏览器或前端应用）可以逐行接收和处理。

每一行数据为一个 JSON 对象，便于前端解析和处理。

---

## 2. 返回数据格式

SSE 协议中，每一行数据以 `data:` 开头，后面跟着一个 JSON 字符串，格式如下：

```
data: {"id": "c49651a4-acb5-4c83-943b-6b77da4538c4", "parent_type": "community", "sub_type": "text", "content": "房"}
```

当流式响应结束时，会发送一个特殊的结束标记：

```
data: [DONE]
```

> 注意：浏览器的 EventSource API 会自动去除 `data:` 前缀，在 `event.data` 中只包含后面的内容。

### 字段说明

- `id`：字符串，唯一标识本条消息（通常为 UUID）。
- `parent_type`：字符串，消息的父类型，表示主要的业务场景。所有父 type 均以英文表示。
- `sub_type`：字符串，消息的子类型，表示 `content` 字段的数据格式。
- `content`：字符串，消息内容。根据 `sub_type` 不同，内容格式不同。

---

## 3. 类型字段说明

### `parent_type`（父类型 - 业务场景）

- `community`：小区相关信息
- `listing`：房源相关信息
- `viewing_list`：看房单相关信息
- `requirement_communication`：需求沟通相关，例如澄清用户意图、询问偏好等
- `subway_line`：地铁线路相关信息
- `commute`：通勤规划相关信息

### `sub_type`（子类型 - 数据格式）

- `text`：表示 `content` 是单个字符，每个字符作为一条独立消息的 `content`。前端需要将这些字符累积起来，最终形成完整的文本（通常是 Markdown 格式）。
- `json`：表示 `content` 是一个完整的 JSON 对象，通常用于列表、详情等复杂数据。

## 4. 业务场景举例

以下是一些示例，展示不同 `parent_type` 和 `sub_type` 的组合。请注意，当 `sub_type` 为 `json` 时，`content` 字段中的 JSON 字符串需要进行转义。对于 `sub_type` 为 `json` 的情况，每行返回一个完整的 JSON 对象，而不是像示例中那样包含在转义的字符串中。

### 4.1 `parent_type: community` (小区信息)

#### `sub_type: text` - 例如，返回小区名称的部分字符

```
data: {"id": "uuid-1", "parent_type": "community", "sub_type": "text", "content": "阳"}
data: {"id": "uuid-2", "parent_type": "community", "sub_type": "text", "content": "光"}
```

#### `sub_type: json` - 例如，返回小区列表

```
data: {"id": "uuid-3", "parent_type": "community", "sub_type": "json", "content": {"communities": [{"id": 201, "name": "阳光小区", "average_price": 5000}, {"id": 202, "name": "幸福家园", "average_price": 5500}]}}
data: [DONE]
```

> 注意：上面的示例中，`content` 字段直接包含了一个 JSON 对象，而不是字符串形式。

### 4.2 `parent_type: listing` (房源信息)

#### `sub_type: json` - 例如，返回房源列表

```
data: {"id": "uuid-4", "parent_type": "listing", "sub_type": "json", "content": {"listings": [{"id": 101, "title": "近地铁精装一居", "price": 3000}, {"id": 102, "title": "南北通透两居室", "price": 4500}]}}
data: [DONE]
```

> 注意：上面的示例中，`content` 字段直接包含了一个 JSON 对象，而不是字符串形式。

### 4.3 `parent_type: viewing_list` (看房单)

#### `sub_type: json`

```
data: {"id": "uuid-5", "parent_type": "viewing_list", "sub_type": "json", "content": {"items": [{"listing_id": 101, "status": "scheduled", "time": "2024-07-15T10:00:00Z"}, {"listing_id": 102, "status": "pending", "time": null}]}}
data: [DONE]
```

> 注意：上面的示例中，`content` 字段直接包含了一个 JSON 对象，而不是字符串形式。

### 4.4 `parent_type: requirement_communication` (需求沟通)

#### `sub_type: text` - 例如，询问用户预算范围

```
data: {"id": "uuid-9", "parent_type": "requirement_communication", "sub_type": "text", "content": "您"}
data: {"id": "uuid-10", "parent_type": "requirement_communication", "sub_type": "text", "content": "的"}
data: {"id": "uuid-11", "parent_type": "requirement_communication", "sub_type": "text", "content": "预"}
data: {"id": "uuid-12", "parent_type": "requirement_communication", "sub_type": "text", "content": "算"}
data: {"id": "uuid-13", "parent_type": "requirement_communication", "sub_type": "text", "content": "范"}
data: {"id": "uuid-14", "parent_type": "requirement_communication", "sub_type": "text", "content": "围"}
data: {"id": "uuid-15", "parent_type": "requirement_communication", "sub_type": "text", "content": "是"}
data: {"id": "uuid-16", "parent_type": "requirement_communication", "sub_type": "text", "content": "？"}
data: [DONE]
```

> 注意：上面的示例展示了 `text` 类型的 `content` 是单个字符，前端需要将这些字符累积起来形成完整文本。

### 4.5 `parent_type: subway_line` (地铁线路信息)

#### `sub_type: json`

```
data: {"id": "uuid-10", "parent_type": "subway_line", "sub_type": "json", "content": {"line_name": "10号线", "stations": ["苏州街", "海淀黄庄", "知春里"]}}
data: [DONE]
```

> 注意：上面的示例中，`content` 字段直接包含了一个 JSON 对象，而不是字符串形式。

### 4.6 `parent_type: commute` (通勤信息)

#### `sub_type: json`

```
data: {"id": "uuid-11", "parent_type": "commute", "sub_type": "json", "content": {"origin": "龙泽苑", "destination": "中关村软件园", "duration_minutes": 45, "modes": ["地铁", "公交"]}}
data: [DONE]
```

> 注意：上面的示例中，`content` 字段直接包含了一个 JSON 对象，而不是字符串形式。

---

## 5. 前端解析说明

- 客户端接收到 SSE 事件时，每一行（`event.data`）都是一个 JSON 字符串。
- **第一步**：使用 JSON 解析将其转换为对象。这个对象会包含 `id`, `parent_type`, `sub_type`, 和 `content`。
- **第二步**：根据 `sub_type` 来处理 `content`：
  - 如果 `sub_type` 是 `text`，那么 `content` 是单个字符，需要将这些字符累积起来，最终形成完整的文本。
  - 如果 `sub_type` 是 `json`，那么 `content` 本身就是一个完整的 JSON 对象，可以直接使用。
- **第三步**：根据 `parent_type` 和解析后的业务数据执行相应的逻辑。

**注意事项**：

- 浏览器的 EventSource API 会自动去除 `data:` 前缀，在 `event.data` 中只包含后面的内容。
- 当收到 `data: [DONE]` 时，表示流式响应结束，此时应该完成当前会话的处理。
