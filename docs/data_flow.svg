<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .user-box {
        fill: #e9ecef;
        stroke: #495057;
      }
      .ui-box {
        fill: #d1e7dd;
        stroke: #0d6832;
      }
      .api-box {
        fill: #cfe2ff;
        stroke: #084298;
      }
      .service-box {
        fill: #fff3cd;
        stroke: #664d03;
      }
      .llm-box {
        fill: #f8d7da;
        stroke: #842029;
      }
      .db-box {
        fill: #e2e3e5;
        stroke: #41464b;
      }
      .data-flow {
        stroke: #0d6efd;
        stroke-width: 2;
        fill: none;
        marker-end: url(#dataflow);
      }
      .response-flow {
        stroke: #198754;
        stroke-width: 2;
        fill: none;
        marker-end: url(#responseflow);
      }
      .text {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
        dominant-baseline: middle;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 24px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 16px;
        font-style: italic;
        text-anchor: middle;
      }
      .small-text {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
      .data-label {
        font-family: Arial, sans-serif;
        font-size: 12px;
        fill: #0d6efd;
      }
      .response-label {
        font-family: Arial, sans-serif;
        font-size: 12px;
        fill: #198754;
      }
    </style>
    <marker id="dataflow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0d6efd" />
    </marker>
    <marker id="responseflow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#198754" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="450" y="30" class="title">AI House Search Data Flow</text>
  <text x="450" y="55" class="subtitle">Data and Response Pathways</text>

  <!-- User -->
  <rect x="50" y="100" width="100" height="50" class="box user-box" />
  <text x="100" y="125" class="text">User</text>

  <!-- UI -->
  <rect x="250" y="100" width="100" height="50" class="box ui-box" />
  <text x="300" y="125" class="text">UI</text>

  <!-- API Layer -->
  <rect x="450" y="100" width="100" height="50" class="box api-box" />
  <text x="500" y="125" class="text">API Layer</text>

  <!-- Chat Service -->
  <rect x="650" y="100" width="100" height="50" class="box service-box" />
  <text x="700" y="125" class="text">Chat Service</text>

  <!-- Session Service -->
  <rect x="250" y="250" width="100" height="50" class="box service-box" />
  <text x="300" y="275" class="text">Session Service</text>

  <!-- Intent Recognition -->
  <rect x="450" y="250" width="100" height="50" class="box llm-box" />
  <text x="500" y="275" class="text">Intent Recognition</text>

  <!-- Parameter Extraction -->
  <rect x="650" y="250" width="100" height="50" class="box llm-box" />
  <text x="700" y="275" class="text">Parameter Extraction</text>

  <!-- Database -->
  <rect x="50" y="400" width="100" height="50" class="box db-box" />
  <text x="100" y="425" class="text">Database</text>

  <!-- House Service -->
  <rect x="250" y="400" width="100" height="50" class="box service-box" />
  <text x="300" y="425" class="text">House Service</text>

  <!-- Response Generator -->
  <rect x="450" y="400" width="100" height="50" class="box llm-box" />
  <text x="500" y="425" class="text">Response Generator</text>

  <!-- External API -->
  <rect x="650" y="400" width="100" height="50" class="box api-box" />
  <text x="700" y="425" class="text">BFF API</text>

  <!-- OpenAI API -->
  <rect x="450" y="550" width="100" height="50" class="box api-box" />
  <text x="500" y="575" class="text">OpenAI API</text>

  <!-- Data Flow Arrows -->
  <!-- User to UI -->
  <path d="M 150 125 L 250 125" class="data-flow" />
  <text x="200" y="115" class="data-label">Query</text>
  
  <!-- UI to API -->
  <path d="M 350 125 L 450 125" class="data-flow" />
  <text x="400" y="115" class="data-label">Request</text>
  
  <!-- API to Chat Service -->
  <path d="M 550 125 L 650 125" class="data-flow" />
  <text x="600" y="115" class="data-label">Message</text>
  
  <!-- Chat Service to Session Service -->
  <path d="M 700 150 L 700 175 L 300 175 L 300 250" class="data-flow" />
  <text x="500" y="165" class="data-label">Session ID</text>
  
  <!-- Chat Service to Intent Recognition -->
  <path d="M 700 150 L 700 175 L 500 175 L 500 250" class="data-flow" />
  <text x="600" y="195" class="data-label">User Message</text>
  
  <!-- Chat Service to Parameter Extraction -->
  <path d="M 700 150 L 700 250" class="data-flow" />
  <text x="710" y="200" class="data-label">Intent + Message</text>
  
  <!-- Session Service to Database -->
  <path d="M 250 275 L 100 275 L 100 400" class="data-flow" />
  <text x="175" y="265" class="data-label">Store Session</text>
  
  <!-- Intent Recognition to OpenAI API -->
  <path d="M 500 300 L 500 550" class="data-flow" />
  <text x="510" y="425" class="data-label">Prompt</text>
  
  <!-- Parameter Extraction to OpenAI API -->
  <path d="M 700 300 L 700 500 L 550 500 L 550 550" class="data-flow" />
  <text x="625" y="490" class="data-label">Extraction Prompt</text>
  
  <!-- Parameter Extraction to House Service -->
  <path d="M 650 275 L 300 275 L 300 400" class="data-flow" />
  <text x="475" y="265" class="data-label">Extracted Parameters</text>
  
  <!-- House Service to External API -->
  <path d="M 350 425 L 650 425" class="data-flow" />
  <text x="500" y="415" class="data-label">Search Request</text>
  
  <!-- Response Generator to OpenAI API -->
  <path d="M 450 425 L 450 550" class="data-flow" />
  <text x="440" y="490" class="data-label">Response Prompt</text>

  <!-- Response Flow Arrows -->
  <!-- OpenAI API to Response Generator -->
  <path d="M 500 550 L 500 450" class="response-flow" />
  <text x="520" y="500" class="response-label">Generated Text</text>
  
  <!-- External API to House Service -->
  <path d="M 650 400 L 350 400" class="response-flow" />
  <text x="500" y="390" class="response-label">House Data</text>
  
  <!-- House Service to Response Generator -->
  <path d="M 350 425 L 450 425" class="response-flow" />
  <text x="400" y="445" class="response-label">House Results</text>
  
  <!-- Response Generator to Chat Service -->
  <path d="M 500 400 L 500 350 L 700 350 L 700 150" class="response-flow" />
  <text x="600" y="350" class="response-label">Generated Response</text>
  
  <!-- Chat Service to API -->
  <path d="M 650 100 L 550 100" class="response-flow" />
  <text x="600" y="90" class="response-label">Response</text>
  
  <!-- API to UI -->
  <path d="M 450 100 L 350 100" class="response-flow" />
  <text x="400" y="90" class="response-label">Response</text>
  
  <!-- UI to User -->
  <path d="M 250 100 L 150 100" class="response-flow" />
  <text x="200" y="90" class="response-label">Results</text>
  
  <!-- Session Service to Database -->
  <path d="M 250 300 L 100 300 L 100 400" class="response-flow" />
  <text x="175" y="320" class="response-label">Save Conversation</text>
</svg>
