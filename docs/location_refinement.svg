<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .step-box {
        fill: #fff3cd;
        stroke: #664d03;
      }
      .decision-box {
        fill: #cfe2ff;
        stroke: #084298;
      }
      .action-box {
        fill: #d1e7dd;
        stroke: #0d6832;
      }
      .llm-box {
        fill: #f8d7da;
        stroke: #842029;
      }
      .api-box {
        fill: #e2e3e5;
        stroke: #41464b;
      }
      .arrow {
        stroke: #333;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .dashed-arrow {
        stroke: #333;
        stroke-width: 2;
        stroke-dasharray: 5,5;
        fill: none;
        marker-end: url(#arrowhead);
      }
      .text {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
        dominant-baseline: middle;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 24px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 16px;
        font-style: italic;
        text-anchor: middle;
      }
      .small-text {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
      .label {
        font-family: Arial, sans-serif;
        font-size: 12px;
        fill: #555;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="450" y="30" class="title">位置精确化流程</text>
  <text x="450" y="55" class="subtitle">Location Refinement Process Flow</text>

  <!-- Start -->
  <rect x="400" y="80" width="100" height="40" rx="20" ry="20" fill="#4CAF50" stroke="#333" stroke-width="2" />
  <text x="450" y="100" class="text" fill="white">开始</text>

  <!-- Extract Location -->
  <rect x="380" y="150" width="140" height="50" class="box llm-box" />
  <text x="450" y="175" class="text">提取位置参数</text>

  <!-- Call Search Suggestion API -->
  <rect x="380" y="230" width="140" height="50" class="box api-box" />
  <text x="450" y="255" class="text">调用搜索建议API</text>

  <!-- Decision: Multiple Location Types? -->
  <polygon points="450,310 500,350 450,390 400,350" class="box decision-box" />
  <text x="450" y="350" class="text">多种位置类型?</text>

  <!-- Decision: Region Asked? -->
  <polygon points="650,310 700,350 650,390 600,350" class="box decision-box" />
  <text x="650" y="350" class="text">已询问区域?</text>

  <!-- Ask Region -->
  <rect x="580" y="420" width="140" height="50" class="box action-box" />
  <text x="650" y="445" class="text">询问具体区域</text>

  <!-- Set Region Asked Flag -->
  <rect x="580" y="500" width="140" height="50" class="box action-box" />
  <text x="650" y="525" class="text">设置区域询问标志</text>

  <!-- Recognize Location Type -->
  <rect x="380" y="420" width="140" height="50" class="box step-box" />
  <text x="450" y="445" class="text">识别位置类型</text>

  <!-- Decision: Is Subway/District/Bizcircle? -->
  <polygon points="450,500 500,540 450,580 400,540" class="box decision-box" />
  <text x="450" y="540" class="text">是地铁/行政区/商圈?</text>

  <!-- Decision: Company Asked? -->
  <polygon points="250,500 300,540 250,580 200,540" class="box decision-box" />
  <text x="250" y="540" class="text">已询问通勤?</text>

  <!-- Ask Commuting -->
  <rect x="180" y="610" width="140" height="50" class="box action-box" />
  <text x="250" y="635" class="text">询问通勤信息</text>

  <!-- Set Company Asked Flag -->
  <rect x="180" y="690" width="140" height="50" class="box action-box" />
  <text x="250" y="715" class="text">设置通勤询问标志</text>

  <!-- Search Houses -->
  <rect x="380" y="610" width="140" height="50" class="box api-box" />
  <text x="450" y="635" class="text">搜索房源</text>

  <!-- Generate Response -->
  <rect x="380" y="690" width="140" height="50" class="box llm-box" />
  <text x="450" y="715" class="text">生成回复</text>

  <!-- End -->
  <rect x="400" y="770" width="100" height="40" rx="20" ry="20" fill="#F44336" stroke="#333" stroke-width="2" />
  <text x="450" y="790" class="text" fill="white">结束</text>

  <!-- Arrows -->
  <!-- Start to Extract Location -->
  <path d="M 450 120 L 450 150" class="arrow" />
  
  <!-- Extract Location to Call Search Suggestion API -->
  <path d="M 450 200 L 450 230" class="arrow" />
  
  <!-- Call Search Suggestion API to Decision: Multiple Location Types? -->
  <path d="M 450 280 L 450 310" class="arrow" />
  
  <!-- Decision: Multiple Location Types? to Decision: Region Asked? (Yes) -->
  <path d="M 500 350 L 600 350" class="arrow" />
  <text x="550" y="340" class="label">是</text>
  
  <!-- Decision: Region Asked? to Ask Region (No) -->
  <path d="M 650 390 L 650 420" class="arrow" />
  <text x="660" y="405" class="label">否</text>
  
  <!-- Ask Region to Set Region Asked Flag -->
  <path d="M 650 470 L 650 500" class="arrow" />
  
  <!-- Set Region Asked Flag to Extract Location (loop back) -->
  <path d="M 650 550 L 650 580 L 750 580 L 750 175 L 520 175" class="dashed-arrow" />
  <text x="700" y="570" class="label">重新提取位置</text>
  
  <!-- Decision: Multiple Location Types? to Recognize Location Type (No) -->
  <path d="M 450 390 L 450 420" class="arrow" />
  <text x="460" y="405" class="label">否</text>
  
  <!-- Recognize Location Type to Decision: Is Subway/District/Bizcircle? -->
  <path d="M 450 470 L 450 500" class="arrow" />
  
  <!-- Decision: Is Subway/District/Bizcircle? to Decision: Company Asked? (No) -->
  <path d="M 400 540 L 300 540" class="arrow" />
  <text x="350" y="530" class="label">否</text>
  
  <!-- Decision: Company Asked? to Ask Commuting (No) -->
  <path d="M 250 580 L 250 610" class="arrow" />
  <text x="260" y="595" class="label">否</text>
  
  <!-- Ask Commuting to Set Company Asked Flag -->
  <path d="M 250 660 L 250 690" class="arrow" />
  
  <!-- Set Company Asked Flag to Extract Location (loop back) -->
  <path d="M 250 740 L 250 760 L 150 760 L 150 175 L 380 175" class="dashed-arrow" />
  <text x="200" y="750" class="label">重新提取位置</text>
  
  <!-- Decision: Is Subway/District/Bizcircle? to Search Houses (Yes) -->
  <path d="M 450 580 L 450 610" class="arrow" />
  <text x="460" y="595" class="label">是</text>
  
  <!-- Decision: Company Asked? to Search Houses (Yes) -->
  <path d="M 300 540 L 350 540 L 350 635 L 380 635" class="arrow" />
  <text x="320" y="530" class="label">是</text>
  
  <!-- Decision: Region Asked? to Recognize Location Type (Yes) -->
  <path d="M 600 350 L 550 350 L 550 445 L 520 445" class="arrow" />
  <text x="580" y="340" class="label">是</text>
  
  <!-- Search Houses to Generate Response -->
  <path d="M 450 660 L 450 690" class="arrow" />
  
  <!-- Generate Response to End -->
  <path d="M 450 740 L 450 770" class="arrow" />
</svg>
