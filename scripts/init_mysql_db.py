#!/usr/bin/env python
"""
MySQL数据库初始化脚本

用于创建MySQL数据库表结构。
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.base import Base, engine
from app.core.config import settings

async def init_db():
    """初始化数据库"""
    print(f"正在连接到数据库: {settings.DATABASE_URL}")
    
    try:
        # 创建所有表
        async with engine.begin() as conn:
            # 删除所有表（如果存在）
            # 警告：这将删除所有数据！仅在开发环境使用
            await conn.run_sync(Base.metadata.drop_all)
            
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
        
        print("数据库表创建成功！")
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(init_db())
