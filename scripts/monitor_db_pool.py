#!/usr/bin/env python
"""
数据库连接池监控脚本

用于实时监控数据库连接池的状态，帮助诊断连接池耗尽问题。
"""

import asyncio
import sys
import os
import time
import signal
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.base import check_pool_health, async_engine
from app.core.config import settings

class PoolMonitor:
    """连接池监控器"""
    
    def __init__(self, interval: int = 5):
        """
        初始化监控器
        
        Args:
            interval: 监控间隔（秒）
        """
        self.interval = interval
        self.running = True
        self.start_time = time.time()
        
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止监控...")
        self.running = False
        
    async def monitor(self):
        """开始监控"""
        print(f"开始监控数据库连接池...")
        print(f"数据库URL: {settings.DATABASE_URL}")
        print(f"监控间隔: {self.interval}秒")
        print("-" * 80)
        
        # 打印表头
        print(f"{'时间':<20} {'池大小':<8} {'已使用':<8} {'可用':<8} {'使用率':<10} {'状态':<10}")
        print("-" * 80)
        
        while self.running:
            try:
                # 获取连接池状态
                pool_status = await check_pool_health()
                
                # 获取连接池状态
                pool_size = pool_status["pool_size"]
                checked_out = pool_status["checked_out"]
                available = pool_status["available"]
                usage_rate = pool_status["usage_rate"]
                
                # 确定状态
                if usage_rate < 0.5:
                    status = "正常"
                elif usage_rate < 0.8:
                    status = "注意"
                elif usage_rate < 0.95:
                    status = "警告"
                else:
                    status = "危险"
                
                # 打印状态
                current_time = datetime.now().strftime("%H:%M:%S")
                print(f"{current_time:<20} {pool_size:<8} {checked_out:<8} {available:<8} {usage_rate:.1%:<10} {status:<10}")
                
                # 如果使用率过高，发出警告
                if usage_rate > 0.8:
                    print(f"⚠️  警告: 连接池使用率过高 ({usage_rate:.1%})")
                
                # 等待下一次检查
                await asyncio.sleep(self.interval)
                
            except KeyboardInterrupt:
                print("\n用户中断，正在停止监控...")
                break
            except Exception as e:
                print(f"监控过程中出错: {str(e)}")
                await asyncio.sleep(self.interval)
        
        print("\n监控已停止")
        
        # 打印总结
        total_time = time.time() - self.start_time
        print(f"总监控时间: {total_time:.1f}秒")

async def main():
    """主函数"""
    # 解析命令行参数
    interval = 5
    if len(sys.argv) > 1:
        try:
            interval = int(sys.argv[1])
        except ValueError:
            print("错误: 监控间隔必须是整数")
            sys.exit(1)
    
    # 创建监控器
    monitor = PoolMonitor(interval)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, monitor.signal_handler)
    signal.signal(signal.SIGTERM, monitor.signal_handler)
    
    try:
        # 开始监控
        await monitor.monitor()
    except Exception as e:
        print(f"监控失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    print("数据库连接池监控器")
    print("使用方法: python monitor_db_pool.py [监控间隔秒数]")
    print("按 Ctrl+C 停止监控")
    print()
    
    asyncio.run(main()) 