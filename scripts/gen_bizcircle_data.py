#!/usr/bin/env python

"""
离线生成商圈描述文字。
使用具备搜索的能力的 deepseek 模型, 根据商圈名称，生成商圈的描述文字。
"""

import os
import httpx
import json
import csv
OPENAI_API_KEY = "bce-v3/ALTAK-9N2iPeFuwTZOywLQvR0Kv/64817fad03d05944e43f7c645b99152edf79dab8" #os.getenv("OPENAI_API_KEY")

PROMPT = """
你是一个置业顾问，需要从居住者视角对北京商圈: {bizcircle_name} 进行分析, 结合搜索结果，需要分析总结出两个字段:
1. 10字以内的商圈特点描述
2. 商圈的综合介绍, 100字以内，使用纯文本，不要输出markdown格式

需要聚焦检索以下与租房息息相关的信息:
1. 大型互联网公司、政企单位
2. 交通: 地铁、公交线路
3. 商业配套: 商场、超市、影院、餐饮等
4. 教育资源: 学校、培训机构等
5. 医疗资源: 医院、诊所等
6. 公园、绿地等休闲设施


举例:
<example>
    <input>望京</input>
    <search_result>
       #在示例中忽略#
    </search_result>
    <output>
        {{
            "title": "互联网新贵聚集地",
            "description": "北京国际化的科技商务区，聚集众多跨国企业（如阿里、美团）与韩国社群，商业繁华（望京
SOHO、合生麒麟社），艺术氛围浓厚，交通便利（13/15号线），兼具现代感与多元文化，适合高净值人群与科技从业者"
        }}
    </output>
</example>

<example>
    <input>国贸CBD</input>
    <search_result>
       #在示例中忽略#
    </search_result>
    <output>
        {{
            "title": "金融精英主场",
            "description": "首都核心金融贸易区，汇聚全球500强总部、顶级写字楼（中国尊、国贸三期）与高端商业（SKP），1/10号线交汇，精英云集，是商务与奢华的象征。"
        }}
    </output>
</example>

"""

def ask_llm(prompt: str) -> str:
    """
    向 deepseek 模型提问，并返回回答。
    """
    url = "https://qianfan.baidubce.com/v2/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + OPENAI_API_KEY
    }
        
    data = {
        "messages": [
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "stream": False,
        "model": "deepseek-v3",
        "web_search": {
            "enable_status": False,
            "enable": True,
            "enable_trace": True
        }
    }

    response = httpx.post(
        url,
        headers=headers,
        json=data,
        timeout=120
    )
    
    # print(response.text)
    
    try:
        response_json = response.json()
        if "choices" in response_json:
            # 提取第一个选择的内容
            first_choice = response_json["choices"][0]
            if "message" in first_choice:
                content = first_choice["message"]["content"]
                content = content.strip().replace("```json", "").replace("```", "")
                try:
                    return json.loads(content)
                except Exception as e:
                    print(f"Error parsing response: {e}: {content}")
                    return ""
        return ""
    except Exception as e:
        print(f"Error parsing response: {e}")
        return ""

def gen_bizcircle_data(bizcircle_name: str) -> str:
    """
    根据商圈名称，生成商圈的描述文字。
    """
    prompt = PROMPT.format(bizcircle_name=bizcircle_name)
    return ask_llm(prompt)


if __name__ == "__main__":
    data_file = os.path.join(os.path.dirname(__file__), "bizcircles.txt")
    output_file = os.path.join(os.path.dirname(__file__), "bizcircle_data.csv")
    
    with open(data_file, "r", encoding="utf-8") as f:
        bizcircles = [line.strip() for line in f if line.strip()]

    with open(output_file, "w", encoding="utf-8", newline="") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=["bizcircle", "title", "description"])
        writer.writeheader()
        for name in bizcircles:
            print(f"Processing: {name}")
            result = gen_bizcircle_data(name)
            if isinstance(result, dict) and "title" in result and "description" in result:
                writer.writerow({
                    "bizcircle": name,
                    "title": result["title"],
                    "description": result["description"]
                })
                print(f"Processed: {name}: {result['title']} - {result['description']}")
            else:
                writer.writerow({
                    "bizcircle": name,
                    "title": "",
                    "description": ""
                })
