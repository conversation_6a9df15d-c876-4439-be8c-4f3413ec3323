#!/usr/bin/env python
"""
数据库迁移脚本

用于运行Alembic数据库迁移。
"""

import os
import sys
import subprocess

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.config import settings

def run_migrations():
    """运行数据库迁移"""
    print(f"正在连接到数据库: {settings.DATABASE_URL}")
    
    try:
        # 运行Alembic迁移
        subprocess.run(["alembic", "upgrade", "head"], check=True)
        print("数据库迁移成功！")
    except subprocess.CalledProcessError as e:
        print(f"数据库迁移失败: {str(e)}")
        raise

if __name__ == "__main__":
    run_migrations()
