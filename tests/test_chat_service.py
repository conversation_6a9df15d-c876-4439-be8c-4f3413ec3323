"""
聊天服务测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch, PropertyMock
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.llm.extraction.location_type_recognizer import LocationTypeRecognizer

from app.services.chat_service import ChatService


class TestChatService:
    """聊天服务测试"""

    def setup_method(self):
        """测试前准备"""
        # 创建模拟的数据库会话
        self.mock_db = AsyncMock()

        # 创建模拟的会话服务
        self.mock_session_service = AsyncMock()
        self.mock_session_service.get_session.return_value = {"context": {}}
        self.mock_session_service.get_conversation_history.return_value = []
        self.mock_session_service.merge_extracted_params.return_value = {}
        self.mock_session_service.update_session_context.return_value = {}

        # 创建模拟的意图识别器
        self.mock_intent_recognizer = AsyncMock()
        self.mock_intent_recognizer.recognize_intent.return_value = ("finding_house", 0.9)

        # 创建模拟的参数提取器
        self.mock_parameter_extractor = AsyncMock()

        # 创建模拟的响应生成器
        self.mock_response_generator = AsyncMock()
        self.mock_response_generator.generate_response.return_value = "这是一个测试回复"

        # 创建模拟的房源服务
        self.mock_house_service = AsyncMock()
        self.mock_house_service.search_houses.return_value = {
            "rooms": [
                {"id": "1", "title": "测试房源1", "price": 3000, "area": 20, "location": "望京", "roomType": "1室0厅"}
            ],
            "promotionRooms": [],
            "summary": "找到1套房源"
        }

        # 创建真实的位置类型识别器
        self.location_recognizer = LocationTypeRecognizer()

        # 创建聊天服务实例
        self.chat_service = ChatService(self.mock_db)

        # 替换模拟对象
        self.chat_service.session_service = self.mock_session_service
        self.chat_service.intent_recognizer = self.mock_intent_recognizer
        self.chat_service.parameter_extractor = self.mock_parameter_extractor
        self.chat_service.response_generator = self.mock_response_generator
        self.chat_service.house_service = self.mock_house_service
        self.chat_service.location_recognizer = self.location_recognizer

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_subway_station(self):
        """测试增强地铁站位置参数"""
        # 模拟用户消息
        user_message = "我想在将台站附近找房子"

        # 模拟提取的参数
        params = {
            "location": "将台站",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "将台站"
        assert enhanced_params["location_type"] == "地铁站"
        assert enhanced_params["distance"] == "3"  # 地铁站默认3公里范围
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_subway_line(self):
        """测试增强地铁线位置参数"""
        # 模拟用户消息
        user_message = "我想在14号线附近找房子"

        # 模拟提取的参数
        params = {
            "location": "14号线",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "14号线"
        assert enhanced_params["location_type"] == "地铁线"
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_district(self):
        """测试增强行政区位置参数"""
        # 模拟用户消息
        user_message = "我想在朝阳区找房子"

        # 模拟提取的参数
        params = {
            "location": "朝阳区",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "朝阳区"
        assert enhanced_params["location_type"] == "行政区"
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_bizcircle(self):
        """测试增强商圈位置参数"""
        # 模拟用户消息
        user_message = "我想在望京找房子"

        # 模拟提取的参数
        params = {
            "location": "望京",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "望京"
        assert enhanced_params["location_type"] == "商圈"
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_community(self):
        """测试增强小区位置参数"""
        # 模拟用户消息
        user_message = "我想在将府家园找房子"

        # 模拟提取的参数
        params = {
            "location": "将府家园",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "将府家园"
        assert enhanced_params["location_type"] == "小区"
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_poi(self):
        """测试增强POI位置参数"""
        # 模拟用户消息
        user_message = "我想在颐堤港附近找房子"

        # 模拟提取的参数
        params = {
            "location": "颐堤港",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "颐堤港"
        assert enhanced_params["location_type"] == "poi"
        assert enhanced_params["distance"] == "3"  # POI默认3公里范围
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_enhance_location_parameters_commuting(self):
        """测试增强通勤位置参数"""
        # 模拟用户消息
        user_message = "我想找一个到百度公司步行10分钟的房子"

        # 模拟提取的参数
        params = {
            "location": "百度公司",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        # 调用方法
        enhanced_params = self.chat_service._enhance_location_parameters(user_message, params)

        # 验证结果
        assert enhanced_params["location"] == "百度公司"
        assert enhanced_params["location_type"] == "通勤"
        assert enhanced_params["transport"] == "walk"
        assert enhanced_params["transport_minutes"] == "10"
        assert "distance" in enhanced_params
        assert enhanced_params["room_type"] == "整租"
        assert enhanced_params["budget"] == "3000,5000"

    @pytest.mark.asyncio
    async def test_has_enough_search_params(self):
        """测试是否有足够的搜索参数"""
        # 测试有足够参数的情况
        context_with_all_params = {
            "location": "将台站",
            "budget": "3000,5000",
            "room_type": "整租"
        }
        assert self.chat_service._has_enough_search_params(context_with_all_params) == True

        # 测试只有位置的情况
        context_with_location_only = {
            "location": "将台站"
        }
        assert self.chat_service._has_enough_search_params(context_with_location_only) == True

        # 测试没有位置的情况
        context_without_location = {
            "budget": "3000,5000",
            "room_type": "整租"
        }
        assert self.chat_service._has_enough_search_params(context_without_location) == False

        # 测试空上下文的情况
        empty_context = {}
        assert self.chat_service._has_enough_search_params(empty_context) == False
