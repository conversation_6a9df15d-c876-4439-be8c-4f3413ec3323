import asyncio
import json
import sys
import os
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.base import Base
from app.services.session_service import SessionService
from app.services.chat_service import ChatService
from app.services.house_service import HouseService

# 创建内存数据库
DATABASE_URL = "sqlite+aiosqlite:///:memory:"
engine = create_async_engine(DATABASE_URL)
async_session = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)

async def setup_db():
    """设置数据库"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

async def test_chat_flow():
    """测试聊天流程"""
    # 设置数据库
    await setup_db()

    # 创建会话
    async with async_session() as session:
        # 创建会话服务
        session_service = SessionService(session)

        # 创建会话
        session_data = await session_service.create_session(user_id="test_user")
        session_id = session_data["session_id"]

        print(f"创建会话: {session_id}")
        print(f"初始上下文: {session_data['context']}")
        print()

        # 创建聊天服务
        chat_service = ChatService(session)

        # 测试消息
        test_messages = [
            "我想在朝阳区找一个两居室的房子",
            "预算在8000左右",
            "要有电梯，最好是南向的",
            "离地铁近一点的"
        ]

        # 处理消息
        for i, message in enumerate(test_messages):
            print(f"用户消息 {i+1}: {message}")

            # 处理消息
            result = await chat_service.process_message(session_id, message)

            # 打印结果
            print(f"意图: {result['intent']}")
            print(f"提取参数: {result['extracted_params']}")
            print(f"更新上下文: {result['context']}")

            # 如果有搜索结果，打印房源信息
            if "search_result" in result and result["search_result"]:
                print(f"搜索结果:")
                print(f"  房源数量: {len(result['search_result'].get('rooms', []))}")
                print(f"  推荐房源数量: {len(result['search_result'].get('promotionRooms', []))}")
                print(f"  摘要: {result['search_result'].get('summary', '')}")

            print(f"AI回复: {result['response']}")
            print()

        # 获取会话
        session_data = await session_service.get_session(session_id)
        print(f"最终上下文: {session_data['context']}")

        # 获取对话历史
        history = await session_service.get_conversation_history(session_id)
        print(f"对话历史:")
        for i, msg in enumerate(history):
            print(f"{i+1}. {msg['role']}: {msg['content'][:50]}...")

if __name__ == "__main__":
    asyncio.run(test_chat_flow())
