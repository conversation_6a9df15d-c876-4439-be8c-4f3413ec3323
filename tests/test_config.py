import os
import sys
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.config import settings

async def test_config():
    """测试配置加载"""
    print("测试配置加载...")

    # 打印OpenAI配置
    print(f"OpenAI API Key: {'*' * 5}{settings.OPENAI_API_KEY[-4:] if settings.OPENAI_API_KEY else 'Not Set'}")
    print(f"OpenAI Model: {settings.OPENAI_MODEL}")
    print(f"OpenAI API Base: {settings.OPENAI_API_BASE}")
    print(f"OpenAI API Version: {settings.OPENAI_API_VERSION}")

    # 打印房源API配置
    print(f"House API Base URL: {settings.HOUSE_API_BASE_URL}")
    print(f"House Search API: {settings.HOUSE_SEARCH_API}")
    print(f"House Location Search API: {settings.HOUSE_LOCATION_SEARCH_API}")
    print(f"House Bizcircles API: {settings.HOUSE_BIZCIRCLES_API}")
    print(f"House Subway API: {settings.HOUSE_SUBWAY_API}")
    print(f"House Resblocks API: {settings.HOUSE_RESBLOCKS_API}")

    # 打印默认城市编码
    print(f"Default City Code: {settings.DEFAULT_CITY_CODE}")

    # 打印提示模板版本
    print(f"Intent Prompt Version: {settings.INTENT_PROMPT_VERSION}")
    print(f"Extraction Prompt Version: {settings.EXTRACTION_PROMPT_VERSION}")
    print(f"Response Prompt Version: {settings.RESPONSE_PROMPT_VERSION}")

    # 检查配置是否正确
    assert settings.OPENAI_MODEL, "OpenAI Model is not set"
    assert settings.OPENAI_API_BASE, "OpenAI API Base is not set"
    assert settings.HOUSE_API_BASE_URL, "House API Base URL is not set"
    assert settings.HOUSE_SEARCH_API, "House Search API is not set"
    assert settings.HOUSE_LOCATION_SEARCH_API, "House Location Search API is not set"
    assert settings.DEFAULT_CITY_CODE, "Default City Code is not set"

    print("配置加载测试通过!")

if __name__ == "__main__":
    asyncio.run(test_config())
