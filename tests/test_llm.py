import asyncio
import json
import sys
import os
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.llm.intent import IntentRecognizer
from app.llm.extraction import ParameterExtractor

async def test_intent_recognition():
    """测试意图识别"""
    print("测试意图识别...")

    # 创建意图识别器
    intent_recognizer = IntentRecognizer()

    # 测试用例
    test_cases = [
        {
            "message": "我想找一个两居室的房子",
            "expected_intent": "finding_house"
        },
        {
            "message": "这个房子有电梯吗？",
            "expected_intent": "asking_details"
        },
        {
            "message": "我的预算是8000元",
            "expected_intent": "providing_info"
        },
        {
            "message": "谢谢你的帮助",
            "expected_intent": "greeting"
        }
    ]

    # 运行测试
    for i, case in enumerate(test_cases):
        message = case["message"]
        expected_intent = case["expected_intent"]

        # 识别意图
        intent, confidence = await intent_recognizer.recognize_intent(message, [])

        # 打印结果
        print(f"测试 {i+1}: {message}")
        print(f"  预期意图: {expected_intent}")
        print(f"  实际意图: {intent}")
        print(f"  置信度: {confidence}")
        print(f"  结果: {'通过' if intent == expected_intent else '失败'}")
        print()

async def test_parameter_extraction():
    """测试参数提取"""
    print("测试参数提取...")

    # 创建参数提取器
    parameter_extractor = ParameterExtractor()

    # 测试用例
    test_cases = [
        {
            "message": "我想在朝阳区找一个两居室的房子",
            "expected_params": {
                "location": "朝阳区",
                "bed_room": "2"
            }
        },
        {
            "message": "预算在8000左右",
            "expected_params": {
                "budget": "8000"
            }
        },
        {
            "message": "要有电梯，最好是南向的",
            "expected_params": {
                "face": "南"
            }
        },
        {
            "message": "离地铁近一点的",
            "expected_params": {
                "distance": "近"
            }
        }
    ]

    # 运行测试
    for i, case in enumerate(test_cases):
        message = case["message"]
        expected_params = case["expected_params"]

        # 提取参数
        extracted_params = await parameter_extractor.extract_parameters(message, [], {})

        # 打印结果
        print(f"测试 {i+1}: {message}")
        print(f"  预期参数: {expected_params}")
        print(f"  实际参数: {extracted_params}")

        # 检查结果
        passed = True
        for key, value in expected_params.items():
            if key not in extracted_params or extracted_params[key] != value:
                passed = False
                break

        print(f"  结果: {'通过' if passed else '失败'}")
        print()

if __name__ == "__main__":
    asyncio.run(test_intent_recognition())
    asyncio.run(test_parameter_extraction())
