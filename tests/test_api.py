import asyncio
import json
import sys
import os
import httpx

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

async def test_api():
    """测试API"""
    base_url = "http://localhost:8000"

    async with httpx.AsyncClient() as client:
        # 1. 测试健康检查
        print("测试健康检查...")
        response = await client.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()

        # 2. 测试版本信息
        print("测试版本信息...")
        response = await client.get(f"{base_url}/version")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()

        # 3. 创建会话
        print("创建会话...")
        response = await client.post(
            f"{base_url}/api/session/create",
            json={"user_id": "test_user"}
        )
        print(f"状态码: {response.status_code}")
        session_data = response.json()
        print(f"会话ID: {session_data['session_id']}")
        print(f"上下文: {session_data['context']}")
        print()

        session_id = session_data["session_id"]

        # 4. 发送消息
        print("发送消息...")
        response = await client.post(
            f"{base_url}/api/chat/send",
            json={
                "session_id": session_id,
                "message": "我想在朝阳区找一个两居室的房子，预算8000左右"
            }
        )
        print(f"状态码: {response.status_code}")
        chat_data = response.json()
        print(f"意图: {chat_data['intent']}")
        print(f"上下文: {chat_data['context']}")
        print(f"回复: {chat_data['response']}")
        print()

        # 5. 获取会话
        print("获取会话...")
        response = await client.get(f"{base_url}/api/session/{session_id}")
        print(f"状态码: {response.status_code}")
        session_data = response.json()
        print(f"会话ID: {session_data['session_id']}")
        print(f"上下文: {session_data['context']}")
        print()

        # 6. 获取对话
        print("获取对话...")
        response = await client.get(f"{base_url}/api/session/{session_id}/conversation")
        print(f"状态码: {response.status_code}")
        conversation_data = response.json()
        print(f"会话ID: {conversation_data['session_id']}")
        print(f"消息数: {len(conversation_data['messages'])}")
        for i, msg in enumerate(conversation_data['messages']):
            print(f"消息 {i+1}: {msg['role']} - {msg['content'][:50]}...")
        print()

        # 7. 测试流式响应
        print("测试流式响应...")
        async with client.stream(
            "POST",
            f"{base_url}/api/chat/stream",
            json={
                "session_id": session_id,
                "message": "我想要南向的，有电梯的"
            },
            timeout=30.0
        ) as response:
            print(f"状态码: {response.status_code}")
            async for chunk in response.aiter_text():
                if chunk.startswith("data: "):
                    data = chunk[6:].strip()
                    if data == "[DONE]":
                        print("\n[完成]")
                    else:
                        print(data, end="", flush=True)

if __name__ == "__main__":
    asyncio.run(test_api())
