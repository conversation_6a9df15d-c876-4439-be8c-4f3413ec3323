"""
房源服务测试
"""

import pytest

from app.services.house_service import HouseService
from app.services.house.searcher import HouseSearcher


class TestHouseService:
    """房源服务测试"""

    def setup_method(self):
        """测试前准备"""
        self.house_service = HouseService()
        self.house_searcher = HouseSearcher()

    @pytest.mark.asyncio
    async def test_prepare_search_params_subway_station(self):
        """测试地铁站位置类型的搜索参数准备"""
        params = {
            "location": "将台站",
            "location_type": "地铁站",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "将台站"
        assert api_params["location_type"] == "subway_station"
        assert api_params["type"] == 2  # 整租

    @pytest.mark.asyncio
    async def test_prepare_search_params_subway_line(self):
        """测试地铁线位置类型的搜索参数准备"""
        params = {
            "location": "14号线",
            "location_type": "地铁线",
            "room_type": "合租",
            "budget": "2000,3000"
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "14号线"
        assert api_params["location_type"] == "subway_line"
        assert api_params["type"] == 1  # 合租

    @pytest.mark.asyncio
    async def test_prepare_search_params_district(self):
        """测试行政区位置类型的搜索参数准备"""
        params = {
            "location": "朝阳区",
            "location_type": "行政区",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "朝阳区"
        assert api_params["location_type"] == "district"
        assert api_params["type"] == 2  # 整租

    @pytest.mark.asyncio
    async def test_prepare_search_params_bizcircle(self):
        """测试商圈位置类型的搜索参数准备"""
        params = {
            "location": "望京",
            "location_type": "商圈",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "望京"
        assert api_params["location_type"] == "bizcircle"
        assert api_params["type"] == 2  # 整租

    @pytest.mark.asyncio
    async def test_prepare_search_params_community(self):
        """测试小区位置类型的搜索参数准备"""
        params = {
            "location": "将府家园",
            "location_type": "小区",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "将府家园"
        assert api_params["location_type"] == "community"
        assert api_params["type"] == 2  # 整租

    @pytest.mark.asyncio
    async def test_prepare_search_params_poi(self):
        """测试POI位置类型的搜索参数准备"""
        params = {
            "location": "颐堤港",
            "location_type": "poi",
            "room_type": "整租",
            "budget": "3000,5000",
            "distance": 5
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "颐堤港"
        assert api_params["location_type"] == "poi"
        assert api_params["distance"] == 5
        assert api_params["type"] == 2  # 整租

    @pytest.mark.asyncio
    async def test_prepare_search_params_commuting(self):
        """测试通勤位置类型的搜索参数准备"""
        params = {
            "location": "百度公司",
            "location_type": "通勤",
            "transport": "walk",
            "transport_minutes": "10",
            "room_type": "整租",
            "budget": "3000,5000"
        }

        api_params = self.house_searcher._prepare_search_params(params)

        assert api_params["location_value"] == "百度公司"
        assert api_params["location_type"] == "poi"
        assert api_params["transport"] == "walk"
        assert api_params["minute"] == "10"
        # 检查距离是否设置了值
        assert api_params["distance"] > 0
        assert api_params["type"] == 2  # 整租

    # 暂时跳过这个测试，因为需要更复杂的模拟
    @pytest.mark.skip(reason="需要更复杂的模拟")
    @pytest.mark.asyncio
    async def test_search_houses_subway_line(self):
        """测试地铁线位置类型的房源搜索"""
        # 这个测试需要更复杂的模拟，暂时跳过
        pass

    # 暂时跳过这个测试，因为需要更复杂的模拟
    @pytest.mark.skip(reason="需要更复杂的模拟")
    @pytest.mark.asyncio
    async def test_search_houses_district(self):
        """测试行政区位置类型的房源搜索"""
        # 这个测试需要更复杂的模拟，暂时跳过
        pass
