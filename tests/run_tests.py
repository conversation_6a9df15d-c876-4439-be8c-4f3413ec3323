#!/usr/bin/env python
"""
测试运行脚本
用法：python -m tests.run_tests [test_name]
例如：python -m tests.run_tests test_llm
"""

import asyncio
import os
import sys
import importlib
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

async def run_test(module_name):
    """运行指定的测试"""
    try:
        print(f"\n{'='*50}")
        print(f"运行测试: {module_name}")
        print(f"{'='*50}\n")

        # 导入模块
        module = importlib.import_module(f"tests.{module_name}")

        # 查找测试函数
        test_functions = [
            name for name in dir(module)
            if name.startswith("test_") and callable(getattr(module, name))
        ]

        if not test_functions:
            print(f"未找到测试函数，将尝试运行模块的主函数")
            if hasattr(module, "__main__") and callable(getattr(module, "__main__")):
                getattr(module, "__main__")()
            else:
                print(f"模块 {module_name} 没有主函数，请确保它有 if __name__ == '__main__': 代码块")
            return

        # 运行测试函数
        for func_name in test_functions:
            print(f"\n{'-'*30}")
            print(f"运行测试函数: {func_name}")
            print(f"{'-'*30}\n")

            func = getattr(module, func_name)
            if asyncio.iscoroutinefunction(func):
                await func()
            else:
                func()

        print(f"\n测试 {module_name} 完成")

    except ImportError as e:
        print(f"导入测试模块 {module_name} 失败: {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"运行测试 {module_name} 时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

async def run_all_tests():
    """运行所有测试"""
    # 获取测试文件
    test_files = [f for f in Path(__file__).parent.glob("test_*.py") if f.is_file()]

    # 排除当前文件
    test_files = [f for f in test_files if f.name != "run_tests.py"]

    # 按名称排序
    test_files.sort()

    # 运行测试
    for test_file in test_files:
        module_name = test_file.stem
        await run_test(module_name)

    print(f"\n{'='*50}")
    print(f"所有测试完成")
    print(f"{'='*50}\n")

async def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行指定的测试
        test_name = sys.argv[1]
        if test_name.endswith(".py"):
            test_name = test_name[:-3]

        await run_test(test_name)
    else:
        # 运行所有测试
        await run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
