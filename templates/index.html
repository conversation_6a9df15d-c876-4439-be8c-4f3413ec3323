<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- 添加 highlight.js 用于代码高亮 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
    <!-- 添加 DOMPurify 用于安全渲染 HTML -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>
    <!-- 添加 Favicon -->
    <link rel="icon" href="/static/img/favicon.svg" type="image/svg+xml">
    <meta name="theme-color" content="#3B82F6">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧面板 -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <div class="brand">
                        <img src="/static/img/logo.svg" alt="Logo" class="logo">
                        <h3>AI找房助手</h3>
                    </div>
                    <div class="sidebar-actions">
                        <button id="new-chat-btn" class="btn">
                            <i class="bi bi-plus-lg"></i> 新对话
                        </button>
                        <button id="load-sessions-btn" class="btn" title="加载所有历史会话">
                            <i class="bi bi-clock-history"></i>
                        </button>
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="user-name">访客</div>
                    <button id="switch-user-btn" class="btn btn-sm" title="切换用户">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                </div>
                <div class="session-list" id="session-list">
                    <!-- 会话列表将通过JavaScript动态生成 -->
                </div>
                <div class="sidebar-footer">
                    <div class="user-info">
                        <img src="/static/img/avatar.svg" alt="User" class="user-avatar">
                        <span class="user-name">访客用户</span>
                    </div>
                    <div class="app-info">
                        <p>版本: 1.0.0</p>
                        <p><a href="/admin" class="text-muted"><i class="bi bi-gear"></i> 会话管理</a></p>
                    </div>
                </div>
            </div>

            <!-- 右侧聊天区域 -->
            <div class="col-md-9 chat-area">
                <div class="chat-header">
                    <div class="d-flex align-items-center">
                        <h4 id="current-session-title">新对话</h4>
                        <span class="badge bg-success ms-2" id="session-state-badge">空闲</span>
                    </div>
                    <div class="chat-actions">
                        <button id="show-params-btn" class="btn btn-outline-primary">
                            <i class="bi bi-list-check"></i> 查看参数
                        </button>
                        <button id="clear-chat-btn" class="btn btn-outline-secondary">
                            <i class="bi bi-trash"></i> 清空对话
                        </button>
                    </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <!-- 欢迎消息 -->
                    <div class="message system-message">
                        <div class="message-content">
                            <h3>👋 欢迎使用AI找房助手</h3>
                            <p>我是您的智能找房顾问，可以根据您的需求快速匹配合适的房源。请告诉我您的找房需求，例如：</p>
                            <ul>
                                <li>🏙️ <strong>位置需求</strong>：我想在朝阳区找房、离地铁5号线近一点、望京SOHO附近</li>
                                <li>💰 <strong>预算范围</strong>：月租5000-8000元、预算7000左右</li>
                                <li>🏠 <strong>房型要求</strong>：两居室、一室一厅、主卧带独卫</li>
                                <li>🌟 <strong>特殊需求</strong>：要有电梯、南北通透、精装修、可以养宠物</li>
                                <li>🚇 <strong>通勤要求</strong>：到国贸上班通勤30分钟内、骑车到中关村不超过20分钟</li>
                            </ul>
                            <p>您可以直接输入您的需求，我会为您推荐最合适的房源！</p>
                        </div>
                    </div>
                </div>

                <div class="chat-input-area">
                    <div class="input-group">
                        <textarea id="message-input" class="form-control" placeholder="输入您的找房需求..." rows="2"></textarea>
                        <button id="send-btn" class="btn btn-primary">
                            <i class="bi bi-send"></i> 发送
                        </button>
                    </div>
                    <div class="input-hints">
                        <span>快捷提问：<small class="text-muted">(点击即发送)</small></span>
                        <button class="hint-btn" title="点击发送此问题">我想在望京找房</button>
                        <button class="hint-btn" title="点击发送此问题">预算5000-8000</button>
                        <button class="hint-btn" title="点击发送此问题">整租两居室</button>
                        <button class="hint-btn" title="点击发送此问题">离地铁近一点</button>
                        <button class="hint-btn" title="点击发送此问题">到国贸通勤30分钟内</button>
                        <button class="hint-btn" title="点击发送此问题">精装修南向</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 参数面板 -->
    <div class="parameters-panel" id="parameters-panel">
        <div class="parameters-header">
            <h5><i class="bi bi-list-check"></i> 当前提取参数</h5>
            <button id="close-parameters-btn" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>
        <div class="parameters-content" id="parameters-content">
            <!-- 参数内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debug-panel">
        <div class="debug-header">
            <h5><i class="bi bi-code-slash"></i> 调试信息</h5>
            <div>
                <button id="toggle-debug-btn" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-code-slash"></i>
                </button>
                <button id="close-debug-btn" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
        </div>
        <div class="debug-content" id="debug-content">
            <!-- 调试内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 用户名输入模态框 -->
    <div class="modal fade" id="userNameModal" tabindex="-1" aria-labelledby="userNameModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userNameModalLabel">欢迎使用AI找房助手</h5>
                </div>
                <div class="modal-body">
                    <p>请输入您的用户名，以便我们为您提供个性化的服务。</p>
                    <div class="mb-3">
                        <label for="userName" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="userName" placeholder="请输入用户名">
                        <div class="form-text text-muted">用户名将用于区分不同用户的会话。</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="saveUserNameBtn">开始使用</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 房源详情模态框 -->
    <div class="modal fade" id="houseDetailModal" tabindex="-1" aria-labelledby="houseDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="houseDetailModalLabel">房源详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="houseDetailContent">
                    <!-- 房源详情内容将通过JavaScript动态生成 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="contactLandlordBtn">联系房东</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/localforage@1.10.0/dist/localforage.min.js"></script>

    <!-- 添加调试脚本 -->
    <script>
        // 检查脚本加载情况
        console.log('页面初始化');
        window.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
        });

        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('全局错误:', message, 'at', source, lineno, colno, error);
            return false;
        };
    </script>

    <script src="/static/js/house-renderer.js"></script>
    <script src="/static/js/app.js"></script>
    <script src="/static/js/image-loader.js"></script>

    <!-- 检查脚本加载 -->
    <script>
        // 检查房源渲染器是否正确加载
        setTimeout(function() {
            console.log('检查房源渲染器:', window.HouseRenderer ? '已加载' : '未加载');
            if (window.HouseRenderer) {
                console.log('房源渲染器方法:', Object.keys(window.HouseRenderer));
            }

            // 测试渲染一个示例房源
            if (window.HouseRenderer) {
                try {
                    const testHouse = {
                        resblock_id: "test123",
                        resblock_name: "测试小区",
                        min_price: "5000.00",
                        max_price: "8000.00",
                        house_count: "3",
                        pic_info: "https://example.com/test.jpg"
                    };

                    console.log('测试渲染房源:', testHouse);
                    const markdown = window.HouseRenderer.renderHouseCard(testHouse);
                    console.log('生成的Markdown:', markdown);

                    // 测试转换为HTML
                    if (window.marked && window.DOMPurify) {
                        const html = DOMPurify.sanitize(marked.parse(markdown));
                        console.log('生成的HTML:', html);
                    } else {
                        console.error('marked或DOMPurify未加载');
                    }
                } catch (error) {
                    console.error('测试渲染房源失败:', error);
                }
            }
        }, 1000);
    </script>
</body>
</html>
