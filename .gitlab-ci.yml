stages:
  - build


job_build:
  stage: build
  script:
    - set -eo pipefail



    - export APP_NAME=$(echo ${TAG} | cut -d'/' -f2)
    - export DOCKER_IMAGE=harbor.ziroom.com/$TAG:$CI_COMMIT_REF_NAME-$CI_PIPELINE_ID
    - docker build -f Dockerfile --no-cache --build-arg RUN_ENV=$ENV -t $DOCKER_IMAGE .
    - docker push $DOCKER_IMAGE
    - echo "build & push image $DOCKER_IMAGE successfully"
  only:
    - api
    - triggers
  except:
    variables:
      - $TRIGGER_DEPLOY == "true"
