# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_VERSION=2023-05-15

# 数据库配置
DATABASE_URL=sqlite:///./ai_house_search.db

# 应用配置
APP_NAME=AI House Search
DEBUG=True

# 自如找房API配置
HOUSE_API_BASE_URL=http://ai-finder-bff.uat.ziroom.com
HOUSE_API_KEY=

# 房源搜索API
HOUSE_SEARCH_API=/rooms/
HOUSE_LOCATION_SEARCH_API=/searchSug
HOUSE_BIZCIRCLES_API=/bizcircles/
HOUSE_SUBWAY_API=/subway/subwayStations
HOUSE_RESBLOCKS_API=/resblocks

# 默认城市编码
DEFAULT_CITY_CODE=110000

# 提示模板版本
INTENT_PROMPT_VERSION=v1
EXTRACTION_PROMPT_VERSION=v1
RESPONSE_PROMPT_VERSION=v1
