# 添加urllib3版本限制解决Docker兼容性问题
urllib3<2.0.0

aiosqlite==0.21.0
pymysql==1.1.0
aiomysql==0.2.0
alembic==1.15.2
annotated-types==0.7.0
anyio==4.9.0
certifi==2025.4.26
click==8.1.8
distro==1.9.0
fastapi==0.115.12
greenlet==3.2.1
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
iniconfig==2.1.0
jinja2==3.1.6
jiter==0.9.0
mako==1.3.10
markupsafe==3.0.2
openai==1.76.0
packaging==25.0
pluggy==1.5.0
pydantic==2.11.3
pydantic-core==2.33.1
pydantic-settings==2.9.1
pytest==8.3.5
pytest-asyncio==0.26.0
python-dotenv==1.1.0
python-multipart==0.0.20
sniffio==1.3.1
sqlalchemy==2.0.40
starlette==0.46.2
tqdm==4.67.1
typing-extensions==4.13.2
typing-inspection==0.4.0
uvicorn==0.34.2
