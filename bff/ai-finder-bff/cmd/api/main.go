package main

import (
	"ai-finder-bff/internal/delivery/http/handler"
	"ai-finder-bff/internal/infrastructure/external"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-finder-bff/internal/application/service"
	repository "ai-finder-bff/internal/infrastructure/repository/localfile"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"go.uber.org/zap"
)

func main() {
	// 初始化日志
	logConfig := logger.Config{
		Level:      "debug",
		OutputPath: "stdout",
		Format:     "console",
	}
	if err := logger.Initialize(logConfig); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	log := logger.GetLogger()

	r := chi.NewRouter()

	// 中间件
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(middleware.Timeout(60 * time.Second))

	// 初始化外部服务
	ymerClient := external.NewYmerHttpClient(external.HTTPClientConfig{
		BaseURL: "http://ymer.kp.ziroom.com",
	})

	personaClient := external.NewPersonaHttpClient(external.HTTPClientConfig{
		BaseURL: "http://zoro.kp.ziroom.com",
	})

	erebusClient := external.NewErebusHttpClient(external.HTTPClientConfig{
		BaseURL: "http://erebus.kp.ziroom.com",
	})

	marsClient := external.NewMarsHttpClient(external.HTTPClientConfig{
		BaseURL: "http://mars.kp.ziroom.com",
	})

	rentOrderClient := external.NewRentOrderHttpClient(external.HTTPClientConfig{
		BaseURL: "http://rentorder.kq.ziroom.com",
	})

	rentOqClient := external.NewRentOqHttpClient(external.HTTPClientConfig{
		BaseURL: "http://rentoq.q.ziroom.com",
	})

	darkportalClient := external.NewDarkPortalHttpClient(external.HTTPClientConfig{
		BaseURL: "http://darkportal-api.kq.ziroom.com",
	})

	// 创建repo实现
	bizcircleExtendRepo := repository.NewBizcircleExtendRepository()
	subwayStationExtendRepo := repository.NewSubwayStationExtendRepository()

	// 创建服务实现
	bizcircleExtendService := service.NewBizcircleService(bizcircleExtendRepo)
	subwayStationExtendService := service.NewSubwayStationExtendService(subwayStationExtendRepo)

	// 初始化 usecase
	roomRetrieveUseCase := usecase.NewRoomRetrieveUseCase(ymerClient, erebusClient)
	userInfoUseCase := usecase.NewUserInfoUseCase(personaClient)
	searchSugUseCase := usecase.NewSearchSugUseCase(ymerClient)
	bizcircleUseCase := usecase.NewBizcircleUseCase(
		erebusClient,
		marsClient,
		bizcircleExtendService,
	)
	resblockUseCase := usecase.NewResblockUseCase(erebusClient, marsClient, roomRetrieveUseCase, ymerClient)
	subwayStationUseCase := usecase.NewSubwayStationUseCase(erebusClient, subwayStationExtendService)
	reserveUseCase := usecase.NewReserveUseCase(rentOrderClient, darkportalClient, rentOqClient)

	// 初始化 handlers
	roomRetrieveHandler := handler.NewRoomRetrieveHandler(roomRetrieveUseCase)
	roomRetrieveHandler.RegisterRoutes(r)

	userInfoHandler := handler.NewUserInfoHandler(userInfoUseCase)
	userInfoHandler.RegisterRoutes(r)

	searchSugHandler := handler.NewSearchSugHandler(searchSugUseCase)
	searchSugHandler.RegisterRoutes(r)

	// 商圈
	bizcircleHandler := handler.NewBizcircleHandler(bizcircleUseCase)
	bizcircleHandler.RegisterRoutes(r)

	// 楼盘
	resblockHandler := handler.NewResblockHandler(resblockUseCase)
	resblockHandler.RegisterRoutes(r)

	// 地铁
	subwayHandler := handler.NewSubwayHandler(subwayStationUseCase)
	subwayHandler.RegisterRoutes(r)

	// 预约看房单
	reserveHandler := handler.NewReserveHandler(reserveUseCase)
	reserveHandler.RegisterRoutes(r)

	srv := &http.Server{
		Addr:    ":8080",
		Handler: r,
	}

	// 优雅关闭
	go func() {
		log.Info("Starting server", zap.String("addr", srv.Addr))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Server failed to start", zap.Error(err))
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown", zap.Error(err))
	}

	log.Info("Server exited gracefully")
}
