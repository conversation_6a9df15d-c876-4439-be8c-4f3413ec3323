package service

type RentOqService interface {
	// WatchingDtlListKeeper 查询行程管家
	WatchingDtlListKeeper(req RentOqReq) (*WatchingDtlListKeeperResp, error)
	// WatchingDtlListHouse 查询行程房源
	WatchingDtlListHouse(req RentOqReq) (*WatchingDtlListHouseResp, error)
	// WatchingDtlListBase 查询行程基础信息
	WatchingDtlListBase(req RentOqReq) (*WatchingDtlListBaseResp, error)
}

type RentOqReq struct {
	Token        string `json:"token"`
	MainOrderNum string `json:"mainOrderNum"` //看房单
	AppVersion   string `json:"appVersion"`
}

type RentOqResp[T any] struct {
	Code         int    `json:"code"`
	Status       string `json:"status"`
	Message      string `json:"message"`
	Data         T      `json:"data"`
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
}

type WatchingDtlListBaseResp struct {
	ErrorMessage string      `json:"errorMessage"`
	Header       interface{} `json:"header"`
	MainOrderNum interface{} `json:"mainOrderNum"`
	UserId       interface{} `json:"userId"`
	AppointType  interface{} `json:"appointType"`
	TourType     interface{} `json:"tourType"`
	BtnObjs      interface{} `json:"btnObjs"`
}

type WatchingDtlListKeeperResp struct {
	KeeperId         string      `json:"keeperId"`
	KeeperName       string      `json:"keeperName"`
	KeeperPhoto      string      `json:"keeperPhoto"`
	KeeperPhone      string      `json:"keeperPhone"`
	KeeperTitle      string      `json:"keeperTitle"`
	ImBtn            bool        `json:"imBtn"`
	PhoneBtn         bool        `json:"phoneBtn"`
	AppointType      string      `json:"appointType"`
	TourType         string      `json:"tourType"`
	KeeperType       string      `json:"keeperType"`
	BottomBanner     interface{} `json:"bottomBanner"`
	BottomBannerInfo interface{} `json:"bottomBannerInfo"`
	Longitude        interface{} `json:"longitude"`
	Latitude         interface{} `json:"latitude"`
	AddressName      interface{} `json:"addressName"`
	VideoCommand     interface{} `json:"videoCommand"`
}

type WatchingDtlListHouseResp struct {
	TotalHouseNumStr string    `json:"totalHouseNumStr"`
	VillageList      []Village `json:"villageList"`
	TourType         string    `json:"tourType"`
}

type Village struct {
	VillageId   int64  `json:"villageId"`
	BlockId     string `json:"blockId"`
	VillageName string `json:"villageName"`
	RouterModel struct {
		Target    string `json:"target"`
		Parameter struct {
			CityCode   string `json:"city_code"`
			ResblockId string `json:"resblockId"`
		} `json:"parameter"`
	} `json:"routerModel"`
	BuildingsList []struct {
		BuildingName interface{} `json:"buildingName"`
		RouterModel  interface{} `json:"routerModel"`
		HouseList    []struct {
			InvNo       int    `json:"invNo"`
			DtlOrderNum string `json:"dtlOrderNum"`
			HouseTitle  string `json:"houseTitle"`
			HouseInfo   struct {
				Area          string `json:"area"`
				PhysicalInvNo string `json:"physical_inv_no"`
				Photo         string `json:"photo"`
				RoomIntro     string `json:"room_intro"`
				Description   string `json:"description"`
				Raw           struct {
					Atmosphere struct {
						BgColor      string `json:"bg_color"`
						Color        string `json:"color"`
						DescColor    string `json:"desc_color"`
						Icon         string `json:"icon"`
						Title        string `json:"title"`
						Type         int    `json:"type"`
						TitleBgColor string `json:"title_bg_color"`
						Desc         string `json:"desc"`
					} `json:"atmosphere"`
					CanSignDateText   string `json:"can_sign_date_text"`
					SubwayStationIcon string `json:"subway_station_icon"`
					CanSignTime       int    `json:"can_sign_time"`
					CanReserveTime    int    `json:"can_reserve_time"`
					RoomCode          string `json:"room_code"`
					Type              int    `json:"type"`
					Bizcircle         string `json:"bizcircle"`
					Router            struct {
						Parameter struct {
							InvNo string `json:"invNo"`
						} `json:"parameter"`
						Target string `json:"target"`
					} `json:"router"`
					ClientSaleStatusText string `json:"client_sale_status_text"`
					Price                int    `json:"price"`
					LayoutPic            string `json:"layout_pic"`
					HousePvUv            struct {
						UvDepa3D  int    `json:"uvDepa3d"`
						PvAppo3D  int    `json:"pvAppo3d"`
						UvDepa24H int    `json:"uvDepa24h"`
						InvNo     string `json:"inv_no"`
						PvAppo24H int    `json:"pvAppo24h"`
						PvFavo3D  int    `json:"pvFavo3d"`
					} `json:"house_pv_uv"`
					RoomNo          string        `json:"room_no"`
					CanSignDate     int           `json:"can_sign_date"`
					Lat             float64       `json:"lat"`
					HxPhoto         string        `json:"hx_photo"`
					AntiTheftPrice  []interface{} `json:"anti_theft_price"`
					StockStatus     string        `json:"stock_status"`
					Lng             float64       `json:"lng"`
					PhotoHd         string        `json:"photo_hd"`
					Parlor          int           `json:"parlor"`
					HasImmersivePic int           `json:"has_immersive_pic"`
					BizcircleStat   struct {
						Total         int    `json:"total"`
						ShareMinPrice int    `json:"share_min_price"`
						WholeTotal    int    `json:"whole_total"`
						WholeMinPrice int    `json:"whole_min_price"`
						ShareTotal    int    `json:"share_total"`
						Key           string `json:"key"`
					} `json:"bizcircle_stat"`
					ShareRentType   int `json:"share_rent_type"`
					BizcircleRouter struct {
						Parameter struct {
							SugType  string `json:"sug_type"`
							SugName  string `json:"sug_name"`
							Children struct {
							} `json:"children"`
							SugValue   string `json:"sug_value"`
							PageSource string `json:"page_source"`
							SugId      string `json:"sug_id"`
						} `json:"parameter"`
						Target string `json:"target"`
					} `json:"bizcircle_router"`
					SignDurationText string `json:"sign_duration_text"`
					Tags             struct {
						LowStock struct {
							Color           string `json:"color"`
							BackgroundColor string `json:"background_color"`
							Title           string `json:"title"`
						} `json:"low_stock"`
						ContentTeamTitle []struct {
							Color           string `json:"color"`
							BackgroundColor string `json:"background_color"`
							Title           string `json:"title"`
						} `json:"content_team_title"`
					} `json:"tags"`
					Size            int    `json:"size"`
					ZiroomVersionId int    `json:"ziroom_version_id"`
					DistrictCode    string `json:"district_code"`
					SortTagsV2      []struct {
						Color           string `json:"color"`
						BackgroundColor string `json:"background_color"`
						Title           string `json:"title"`
					} `json:"sort_tags_v2"`
					HouseCode    string `json:"house_code"`
					Name         string `json:"name"`
					ResblockStat struct {
						Total         int    `json:"total"`
						ShareMinPrice int    `json:"share_min_price"`
						WholeTotal    int    `json:"whole_total"`
						WholeMinPrice int    `json:"whole_min_price"`
						ShareTotal    int    `json:"share_total"`
						Key           string `json:"key"`
					} `json:"resblock_stat"`
					InvNo         int    `json:"inv_no"`
					RoomId        int    `json:"room_id"`
					BizcircleCode string `json:"bizcircle_code"`
					CityCode      string `json:"city_code"`
					PhotoWebp     string `json:"photo_webp"`
					ResblockName  string `json:"resblock_name"`
					UnitType      int    `json:"unit_type"`
					ToiletCount   int    `json:"toilet_count"`
					PhotoWebpHd   string `json:"photo_webp_hd"`
					DistrictName  string `json:"district_name"`
					CityName      string `json:"city_name"`
					SortTags      []struct {
						Color           string `json:"color"`
						BackgroundColor string `json:"background_color"`
						Title           string `json:"title"`
					} `json:"sort_tags"`
					ZiroomVersionName string `json:"ziroom_version_name"`
					Floor             string `json:"floor"`
					HasImmersiveVideo int    `json:"has_immersive_video"`
					PriceStyle        struct {
						Color string `json:"color"`
					} `json:"price_style"`
					SubwayStationInfo string `json:"subway_station_info"`
					PaladinPrice      int    `json:"paladin_price"`
					ResblockId        string `json:"resblock_id"`
					News              struct {
						BgColor      string `json:"bg_color"`
						Color        string `json:"color"`
						DescColor    string `json:"desc_color"`
						Icon         string `json:"icon"`
						Title        string `json:"title"`
						Type         int    `json:"type"`
						TitleBgColor string `json:"title_bg_color"`
						Desc         string `json:"desc"`
					} `json:"news"`
					HouseId          int     `json:"house_id"`
					ClientSaleStatus int     `json:"client_sale_status"`
					BuildingLat      float64 `json:"building_lat"`
					TypeText         string  `json:"type_text"`
					SaleStatus       int     `json:"sale_status"`
					Photo            string  `json:"photo"`
					BuildingLng      float64 `json:"building_lng"`
					PaladinPriceUnit string  `json:"paladin_price_unit"`
					Bedroom          int     `json:"bedroom"`
					PriceUnit        string  `json:"price_unit"`
					Layout           string  `json:"layout"`
					HasLift          int     `json:"has_lift"`
					Face             string  `json:"face"`
					FloorTotal       string  `json:"floor_total"`
				} `json:"raw"`
				ResblockName string `json:"resblock_name"`
				Tags         []struct {
					Title string `json:"title"`
				} `json:"tags"`
				PriceUnit   string `json:"price_unit"`
				Face        string `json:"face"`
				FloorTotal  string `json:"floor_total"`
				ContentType int    `json:"content_type"`
				Price       int    `json:"price"`
				Name        string `json:"name"`
				Floor       string `json:"floor"`
				InvNo       string `json:"inv_no"`
				PriceStyle  struct {
					Color string `json:"color"`
				} `json:"price_style"`
				ResblockId string `json:"resblock_id"`
			} `json:"houseInfo"`
			TipIcon     interface{} `json:"tipIcon"`
			Tip         interface{} `json:"tip"`
			RouterModel struct {
				Target    string `json:"target"`
				Parameter struct {
					InvNo string `json:"invNo"`
				} `json:"parameter"`
			} `json:"routerModel"`
			HouseType     int         `json:"houseType"`
			Bedroom       int         `json:"bedroom"`
			AppointStatus interface{} `json:"appointStatus"`
			HouseTips     interface{} `json:"houseTips"`
			GrayFlagBool  bool        `json:"grayFlagBool"`
			HouseStatus   interface{} `json:"houseStatus"`
			Button        interface{} `json:"button"`
			IsRecommend   bool        `json:"isRecommend"`
			News          interface{} `json:"news"`
		} `json:"houseList"`
	} `json:"buildingsList"`
}
