package service

// Erebus 服务接口
type ErebusService interface {
	GetBaseData(request BaseDataRequest) (*BaseDataResponse, error)                      // 基础数据， 包括地铁线，商圈等
	Aggregate(request AggregateRequest) (*AggregateResponse, error)                      // 聚合接口
	GetCommonCondition(request CommonConditionRequest) (*CommonConditionResponse, error) // 新增
}

// BaseDataRequest 过滤器请求
type BaseDataRequest struct {
	CityCode string `url:"city"`
}

// BaseDataResponse 过滤器响应
type BaseDataResponse struct {
	Status    string   `json:"status"`
	ErrorCode int      `json:"errorCode"`
	Message   string   `json:"message"`
	TraceId   string   `json:"traceId"`
	Data      BaseData `json:"data"`
}

type BaseData struct {
	Subway []SubwayLine `json:"subway"`
}

type SubwayLine struct {
	Code     int       `json:"code"`
	Name     string    `json:"name"`
	Stations []Station `json:"stations"`
}

type Station struct {
	Code int     `json:"code"`
	Name string  `json:"name"`
	Lat  float64 `json:"lat"`
	Lng  float64 `json:"lng"`
}

// AggregateRequest 聚合查询请求
/*
{
    "filters": {
        "terms": {
            "city_code": "110000",
            "apartment_type": 1,
            "rent_unit_type": [2, 3]
        }
    },
    "groups": {
        "resblock_id": {
            "field": {
                "resblock_id": "terms"
            },
            "size": 3,
            "groups": {
                "min_price": {
                    "field": {
                        "sort_price": "min"
                    }
                },
                "max_price": {
                    "field": {
                        "sort_price": "max"
                    }
                }
            }
        }
    }
}
*/
type AggregateRequest struct {
	Filters Filters              `json:"filters"`
	Groups  map[string]GroupInfo `json:"groups"`
	//Sorts   map[string]string    `json:"sorts"`
	//Start   int                  `json:"start"`
	//Size    int                  `json:"size"`
}

type Filters struct {
	Terms map[string]interface{} `json:"terms"`
	Range map[string][]*int      `json:"range"`
}

type GroupInfo struct {
	Field  map[string]string           `json:"field"`
	Size   int                         `json:"size"`
	Groups map[string]GroupFieldConfig `json:"groups,omitempty"`
}

type GroupFieldConfig struct {
	Field  map[string]string           `json:"field"`
	Size   int                         `json:"size"`
	Groups map[string]GroupFieldConfig `json:"groups,omitempty"`
}

// AggregateResponse 聚合查询响应
type AggregateResponse struct {
	Status    string      `json:"status"`
	ErrorCode int         `json:"errorCode"`
	Message   string      `json:"message"`
	TraceId   string      `json:"traceId"`
	Data      GroupResult `json:"data"`
}

type GroupResult struct {
	Total int                                 `json:"total"`
	Aggs  map[string][]map[string]interface{} `json:"aggs"`
}

// CommonConditionRequest 通用过滤器请求
type CommonConditionRequest struct {
	CityCode    string `url:"city"`
	VersionCode string `url:"version_code"`
}

// CommonConditionResponse 通用过滤器响应
type CommonConditionResponse struct {
	Status    string     `json:"status"`
	ErrorCode int        `json:"errorCode"`
	Message   string     `json:"message"`
	TraceId   string     `json:"traceId"`
	Data      CommonData `json:"data"`
}

type CommonData struct {
	CheckinDate CheckinDate `json:"checkin_date"`
	Products    []Product   `json:"products"`
	Sort        Sort        `json:"sort"`
}

type CheckinDate struct {
	EndDate   string `json:"end_date"`
	Key       string `json:"key"`
	StartDate string `json:"start_date"`
	Title     string `json:"title"`
}
type Product struct {
	Children  []ProductChildren `json:"children"`
	IsOffline int               `json:"is_offline"`
	IsSingle  int               `json:"is_single"`
	Key       string            `json:"key"`
	Price     Price             `json:"price"`
	Title     string            `json:"title"`
	Value     int               `json:"value"`
}

type ProductChildren struct {
	Children  []ProductChildrenChildren `json:"children"`
	IsOffline int                       `json:"is_offline"` // 是否下线
	Key       string                    `json:"key"`
	Title     string                    `json:"title"`
}

type ProductChildrenChildren struct {
	IsOffline int    `json:"is_offline"` // 是否下线
	Title     string `json:"title"`
	IsSingle  int    `json:"is_single"` // 是否单选
	Value     string `json:"value"`
}

type Price struct {
	PriceChildren []PriceChildren `json:"price"`
	PriceLimit    PriceLimit      `json:"price_limit"`
}

type PriceChildren struct {
	IsOffline int    `json:"is_offline"`
	IsSingle  int    `json:"is_single"`
	Title     string `json:"title"`
	Value     string `json:"value"`
}

type PriceLimit struct {
	Diff    int `json:"diff"`
	FastSeg int `json:"fast_seg"`
	Max     int `json:"max"`
	Mid     int `json:"mid"`
	Step    int `json:"step"`
}

type Sort struct {
	Children []SortChildren `json:"children"`
	Key      string         `json:"key"`
	Title    string         `json:"title"`
}

type SortChildren struct {
	Icon          string `json:"icon"`
	ShortTitle    string `json:"short_title"`
	Title         string `json:"title"`
	UncheckedIcon string `json:"unchecked_icon"`
	Value         int    `json:"value"`
}
