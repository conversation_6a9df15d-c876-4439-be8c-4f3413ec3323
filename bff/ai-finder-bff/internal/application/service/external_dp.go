package service

type DarkPortalService interface {
	GetUserInfo(token string) (*DPUserInfoResponse, error)
}

type DPUserInfoResponse struct {
	Uid            string      `json:"uid"`
	CreateTime     int64       `json:"createTime"`
	Phone          string      `json:"phone"`
	PhoneChecked   int         `json:"phoneChecked"`
	EmailChecked   int         `json:"emailChecked"`
	LastOnlineTime int64       `json:"lastOnlineTime"`
	HasPassword    bool        `json:"hasPassword"`
	Id             int         `json:"id"`
	Email          interface{} `json:"email"`
	Status         int         `json:"status"`
	Username       string      `json:"username"`
}

type DPResponse struct {
	Code               string             `json:"code"`
	Message            string             `json:"message"`
	Sys                string             `json:"sys"`
	DPUserInfoResponse DPUserInfoResponse `json:"resp"`
}
