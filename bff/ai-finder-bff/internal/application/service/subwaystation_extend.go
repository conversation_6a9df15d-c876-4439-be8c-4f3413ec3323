package service

import repository "ai-finder-bff/internal/infrastructure/repository/localfile"

// SubwayStationExtend 商圈信息
type SubwayStationExtend struct {
	SearchResults map[string]interface{} `json:"search_results"`
	Highlight     string                 `json:"highlight"`
	Summary       string                 `json:"summary"`
}

// SubwayStationExtendService 商圈信息服务接口
type SubwayStationExtendService interface {
	GetSubwayStationExtend(name string) *SubwayStationExtend
}

type subwayStationExtendService struct {
	subwayStationRepo *repository.SubwayStationExtendRepository
}

// NewSubwayStationExtendService 创建地铁站服务
func NewSubwayStationExtendService(repo *repository.SubwayStationExtendRepository) SubwayStationExtendService {
	return &subwayStationExtendService{
		subwayStationRepo: repo,
	}
}

// GetSubwayStationExtend 获取商圈扩展信息
func (s *subwayStationExtendService) GetSubwayStationExtend(name string) *SubwayStationExtend {
	info, err := s.subwayStationRepo.GetSubwayStationExtend(name)
	if err != nil || info == nil {
		return nil
	}

	return &SubwayStationExtend{
		Highlight: info.Highlight,
		Summary:   info.Summary,
	}
}
