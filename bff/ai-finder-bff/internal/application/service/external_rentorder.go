package service

// RentOrderService defines the interface for rent order operations
type RentOrderService interface {
	CreateReservation(req ReserveRequest) (*ReserveResponse, error)
}

// ReserveRequest represents the request structure for creating a reservation
type ReserveRequest struct {
	Uid                        string      `json:"uid"`
	Token                      string      `json:"token"`                                // token， 无此参数
	KeeperID                   string      `json:"keeperId,omitempty"`                   // 管家ID
	VerifyCode                 string      `json:"verifyCode,omitempty"`                 // 验证码 当没有登录，或登录后手机号不是客户库的默认的手机号时，要求必传
	VerifyCodeType             string      `json:"verifyCodeType,omitempty"`             // 枚举见发送验证码接口定义
	NeedIm                     int64       `json:"needIm,omitempty"`                     // 标识
	UserName                   string      `json:"userName,omitempty"`                   // 客户电话
	UserPhone                  string      `json:"userPhone,omitempty"`                  // 客户姓名
	Gender                     int         `json:"gender,omitempty"`                     // 客户性别 非必传
	ReserveSource              string      `json:"reserveSoruce,omitempty"`              // 约看来源
	ReserveChannel             string      `json:"reserveChannel,omitempty"`             // 约看渠道
	CityCode                   string      `json:"cityCode,omitempty"`                   // 城市编码
	UserRequirementList        []string    `json:"userRequirementList,omitempty"`        // [着急看房,不着急] 【2020-06-03 客端App约看页优化】， 非必传
	Remark                     string      `json:"remark,omitempty"`                     // 备注
	MsgToKeeper                string      `json:"msgToKeeper,omitempty"`                // 给管家的留言
	UserChoiceResblockID       *int64      `json:"userChoiceResblockId,omitempty"`       // 选择的“先看这里”小区楼盘id，默认取第一个
	TripList                   []Trip      `json:"tripList"`                             // 行程列表
	AddOrderInfoList           []OrderInfo `json:"addOrderInfoList,omitempty"`           // 可添加看房单行程中的房源信息
	OpenHouseChannel           int         `json:"openHouseChannel,omitempty"`           // open house约看渠道， 非必传
	NeedSycCustomerShoppingCar bool        `json:"needSycCustomerShoppingCar,omitempty"` // 是否需要同步删除php购物车数据， true=是， false=否
	SearchSource               string      `json:"searchSource,omitempty"`               // 搜索来源
	ShowEntrance               string      `json:"showEntrance,omitempty"`               // 展位入口
}

// Trip represents a single trip in the reservation
type Trip struct {
	HouseInfoList             []HouseInfo `json:"houseInfoList,omitempty"`             // 房源详情
	AllocationID              string      `json:"allocationId,omitempty"`              // 分配ID 暂未使用
	KeeperID                  string      `json:"keeperId,omitempty"`                  // 管家ID 暂未使用
	ExpectTime                string      `json:"expectTime"`                          // 期望的看房时间 (或期望看房时间左区间)，格式： 2025-05-27 12:00:00
	ExpectEndTime             string      `json:"expectEndTime,omitempty"`             // 期望看房时间右区间
	Mode                      string      `json:"mode,omitempty"`                      // 约看模式，【2022-02-22 视频/自助看房新增】因历史问题存在无此字段的请求默认模式为实地带看。 ON_SPOT
	CentralResblockID         *int64      `json:"centralResblockId"`                   // 中心楼盘id
	VideoShootRequirementList []string    `json:"videoShootRequirementList,omitempty"` // 【拍卫生间、拍厨房、拍窗外、拍冰箱橱柜】， 非必传
}

type HouseInfo struct {
	VillageID  *int64 `json:"villageId"`            // 楼盘id
	HouseInvNo *int64 `json:"houseInvNo"`           // 房源ID
	ActivityID string `json:"activityId,omitempty"` // 活动id
}

// OrderInfo represents order information
type OrderInfo struct {
	MainOrderNum   int64   `json:"mainOrderNum"`
	HouseInvNoList []int64 `json:"houseInvNoList"`
	KeeperID       int64   `json:"keeperId,omitempty"`
	Mode           string  `json:"mode,omitempty"`
}

// ReserveResponse represents the response structure for reservation
type ReserveResponse struct {
	Message     string      `json:"message"`
	Data        ReserveData `json:"data"`
	Code        int64       `json:"code"`
	Status      string      `json:"status"`
	RequestID   string      `json:"requestId"`
	SegmentCode string      `json:"segmentCode"`
}

// ReserveData represents the data structure in the response
type ReserveData struct {
	Toast           string `json:"toast"` // 前端提示
	Success         bool   `json:"success"`
	AllInvNoInvalid bool   `json:"allInvNoInvalid"` // 是否全部失效
	Router          Router `json:"router"`          // 跳转路由
}

// Router represents the router information in the response
type Router struct {
	Target    string    `json:"target"`    //直接跳转
	Parameter Parameter `json:"parameter"` // 跳转参数
}

// Parameter represents the parameter information in the router
type Parameter struct {
	AppointOrderNum string `json:"appointOrderNum"`
}
