package service

// ResblockService 楼盘服务接口
type ResblockService interface {
	GetResblocks(request ResblockRequest) (*ResblockResponse, error)
}

// 楼盘请求
type ResblockRequest struct {
	BizcircleId string `url:"bizcircleId"`
}

// 楼盘响应
type ResblockResponse struct {
	Code      string     `json:"code"`
	Message   string     `json:"message"`
	Success   bool       `json:"success"`
	TimeStamp int64      `json:"timeStamp"`
	Data      []Resblock `json:"data"`
}

// 楼盘信息
type Resblock struct {
	CityCode    string `json:"cityCode"`
	FocusStatus int    `json:"focusStatus"`
	ID          string `json:"id"`
	Name        string `json:"name"`
}
