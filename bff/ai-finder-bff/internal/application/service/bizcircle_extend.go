package service

import repository "ai-finder-bff/internal/infrastructure/repository/localfile"

// BizcircleInfo 商圈信息
type BizcircleExtend struct {
	SearchResults map[string]interface{} `json:"search_results"`
	Highlight     string                 `json:"highlight"`
	Summary       string                 `json:"summary"`
}

// BizcircleExtendService 商圈信息服务接口
type BizcircleExtendService interface {
	GetBizcircleExtend(name string) *BizcircleExtend
}

type bizcircleService struct {
	bizcircleRepo *repository.BizcircleExtendRepository
}

// NewBizcircleService 创建商圈服务
func NewBizcircleService(repo *repository.BizcircleExtendRepository) BizcircleExtendService {
	return &bizcircleService{
		bizcircleRepo: repo,
	}
}

// GetBizcircleExtend 获取商圈扩展信息
func (s *bizcircleService) GetBizcircleExtend(name string) *BizcircleExtend {
	info, err := s.bizcircleRepo.GetBizcircleExtend(name)
	if err != nil || info == nil {
		return nil
	}

	return &BizcircleExtend{
		Highlight: info.Highlight,
		Summary:   info.Summary,
	}
}
