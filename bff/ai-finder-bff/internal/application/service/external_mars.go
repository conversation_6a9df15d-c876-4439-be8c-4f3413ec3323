package service

// MarsService 火星服务接口
type MarsService interface {
	GetBizcircleDetail(request BizcircleDetailRequest) (*BizcircleDetailResponse, error)
	GetResblockDetail(request ResblockDetailRequest) (*ResblockDetailResponse, error)
	GetResblockPicDetail(request ResblockDetailRequest) (*ResblockPicDetailResponse, error)
}

// 商圈详情请求
type BizcircleDetailRequest struct {
	BizcircleId string `url:"bizcircleId"`
}

// 商圈详情响应
type BizcircleDetailResponse struct {
	Code      string        `json:"code"`
	Message   string        `json:"message"`
	TimeStamp int64         `json:"timeStamp"`
	Data      BizcircleData `json:"data"`
	Success   bool          `json:"success"`
}

// 商圈详情数据
type BizcircleData struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	CityCode    string `json:"cityCode"`
	Lng         string `json:"lng"`
	Lat         string `json:"lat"`
	Desc        string `json:"desc"`
	Description string `json:"description"`
	//Districts   []string `json:"districts"`
	//Subways     []string `json:"subways"`
	//Pictures    []string `json:"pictures"`
	//Labels      []string `json:"lables"` // 注意：API 返回的是 "lables"
}

// 商圈详情
type BizcircleDetail struct {
	BizcircleId   string  `json:"bizcircle_id"`
	BizcircleName string  `json:"bizcircle_name"`
	Lat           float64 `json:"lat"`
	Lng           float64 `json:"lng"`
	Description   string  `json:"description"`
	Traffic       string  `json:"traffic"`
	Education     string  `json:"education"`
	Medical       string  `json:"medical"`
	Business      string  `json:"business"`
	Entertainment string  `json:"entertainment"`
}

// 楼盘详情请求
type ResblockDetailRequest struct {
	ResblockId string `url:"resblockId"`
}

// 楼盘详情响应
type ResblockDetailResponse struct {
	Code      string         `json:"code"`
	Message   string         `json:"message"`
	TimeStamp int64          `json:"timeStamp"`
	Data      ResblockDetail `json:"data"`
	Success   bool           `json:"success"`
}

// 楼盘详情数据
type ResblockDetail struct {
	ID              int64    `json:"id"`
	Name            string   `json:"name"`
	Lng             float64  `json:"lng"`
	Lat             float64  `json:"lat"`
	CityCode        string   `json:"cityCode"`
	BaseBizcircleId int      `json:"baseBizcircleId"`
	DistrictId      int      `json:"districtId"`
	DistrictName    string   `json:"districtName"`
	BuildBeginYear  int      `json:"buildBeginYear"`
	BuildEndYear    int      `json:"buildEndYear"`
	Alias           []string `json:"alias"`
	VirescenceRate  float64  `json:"virescenceRate"`
	CubageRate      float64  `json:"cubageRate"`
	FocusStatus     int      `json:"focusStatus"`
	BuildArea       int      `json:"buildArea"`
	BuildingCnt     int      `json:"buildingCnt"`
	HouseCnt        int      `json:"houseCnt"`
	CarRatio        string   `json:"carRatio"`
	BuildTypes      string   `json:"buildTypes"`
	ParkingFeeMonth int      `json:"parkingFeeMonth"`
	ParkingFeeYear  int      `json:"parkingFeeYear"`
	AdminAddress    string   `json:"adminAddress"`
	HasClose        int      `json:"hasClose"`
	Status          int      `json:"status"`
}

// 楼盘图片详情响应
type ResblockPicDetailResponse struct {
	Code      string            `json:"code"`
	Message   string            `json:"message"`
	TimeStamp int64             `json:"timeStamp"`
	Data      ResblockPicDetail `json:"data"`
	Success   bool              `json:"success"`
}

// 楼盘图片详情数据
type ResblockPicDetail struct {
	ResblockId    int             `json:"resblockId"`
	CityCode      string          `json:"cityCode"`
	ResblockImage []ResblockImage `json:"resblockImage"`
}

type ResblockImage struct {
	PicUrl string `json:"picUrl"`
}
