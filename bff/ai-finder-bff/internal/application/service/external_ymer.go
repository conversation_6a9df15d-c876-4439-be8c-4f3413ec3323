package service

import "encoding/json"

// RoomListV4Request represents the request structure for getting room list
type RoomListV4Request struct {
	CityCode                string `json:"city_code,omitempty" url:"city_code,omitempty"` // 城市
	Type                    string `json:"type,omitempty" url:"type,omitempty"`           // 租房类型
	SugType                 string `json:"sug_type,omitempty" url:"sug_type,omitempty"`
	SugValue                string `json:"sug_value,omitempty" url:"sug_value,omitempty"`
	SubwayStationCode       string `json:"subway_station_code,omitempty" url:"subway_station_code,omitempty"` // 地铁站
	SubwayCode              string `json:"subway_code,omitempty" url:"subway_code,omitempty"`                 // 地铁线
	DistrictCode            string `json:"district_code,omitempty" url:"district_code,omitempty"`             // 城区
	BizcircleCode           string `json:"bizcircle_code,omitempty" url:"bizcircle_code,omitempty"`           // 商圈
	BizcircleCode2          string `json:"bizcircle_code2,omitempty" url:"bizcircle_code2,omitempty"`         // 曼舍商圈
	ResblockId              string `json:"resblock_id,omitempty" url:"resblock_id,omitempty"`                 // 楼盘
	Clng                    string `json:"clng,omitempty" url:"clng,omitempty"`                               // 经度
	Clat                    string `json:"clat,omitempty" url:"clat,omitempty"`                               // 纬度
	Distance                *int   `json:"distance,omitempty" url:"distance,omitempty"`                       // 距离 （配合经纬度使用）
	IsFirstRent             string `json:"is_first_rent,omitempty" url:"is_first_rent,omitempty"`             // 是否首次出租
	Leasetype               string `json:"leasetype,omitempty" url:"leasetype,omitempty"`                     // 租约类型
	Bedroom                 string `json:"bedroom,omitempty" url:"bedroom,omitempty"`                         // 居室
	Price                   string `json:"price,omitempty" url:"price,omitempty"`                             // 价格区间
	Sort                    string `json:"sort,omitempty" url:"sort,omitempty"`                               // 排序规则
	Hface                   string `json:"hface,omitempty" url:"hface,omitempty"`                             // 房间朝向
	Area                    string `json:"area,omitempty" url:"area,omitempty"`                               // 面积区间
	CheckinDate             string `json:"checkin_date,omitempty" url:"checkin_date,omitempty"`               // 预计可入住日期，格式类似（2017-09-28）
	CheckinPeopleNum        string `json:"checkin_people_num,omitempty" url:"checkin_people_num,omitempty"`   // 入住人数
	Product                 *int   `json:"product,omitempty" url:"product,omitempty"`                         // 产品类型， 自如寓需要传6
	Minute                  *int   `json:"minute,omitempty" url:"minute,omitempty"`                           // 通勤时间，配合公司使用
	Transport               string `json:"transport,omitempty"`
	Company                 string `json:"company,omitempty"`
	Layout                  string `json:"layout,omitempty"`                   // 特色户型（合租) 1: 独立卫生间 2. 独立阳台
	Feature                 string `json:"feature,omitempty"`                  // 房源特色 高层视野：71 带起居室:61 全屋智能：63 南北通透：72 loft：44
	Version                 string `json:"version,omitempty"`                  // 产品风格 友家7.0：7  友家6.0：6   心舍3.0：34  心舍2.0：21   心舍1.0：17
	FeatureHouse            string `json:"feature_house,omitempty"`            // 小区特色 新小区:8
	Heating                 string `json:"heating,omitempty"`                  // 供暖方式 集体供暖:3  独立供暖:2  中央供暖:1
	Roommate                string `json:"roommate,omitempty"`                 // 合租室友 全男：2  全女：1
	Tag                     string `json:"tag,omitempty"`                      // 标签 可预定:9 有电梯:13
	LeaseTypeDurationLong   string `json:"leasetypeduration_long,omitempty"`   // 租约二级菜单 长租1年：62  长租2年：63  长租3年：64
	LeaseTypeDurationMonth  string `json:"leasetypeduration_month,omitempty"`  // 租约二级菜单 月租 1个月：67  2个月：68  3个月：69
	LeaseTypeDurationSeason string `json:"leasetypeduration_season,omitempty"` // 租约二级菜单 季租 4个月： 70  5个月：71  6个月：72
	Page                    *int   `json:"page,omitempty" url:"page,omitempty"`
	Size                    *int   `json:"size,omitempty" url:"size,omitempty"`
}

// RoomV3Response represents the response structure for room information
type RoomListV4Response struct {
	// Add fields as needed
	Data         RoomListV4ResponseData `json:"data"`
	ErrorCode    int                    `json:"error_code"`
	ErrorMessage string                 `json:"error_message"`
	Status       string                 `json:"status"`
	TraceId      string                 `json:"traceId"`
}

type RoomListV4ResponseData struct {
	Banner                   interface{} `json:"banner"`
	Title                    string      `json:"title"`
	Resblock                 interface{} `json:"resblock"`
	Rooms                    []Room      `json:"rooms"`
	QuerySessionId           string      `json:"query_session_id"`
	SugCity                  interface{} `json:"sug_city"`
	SugResblock              interface{} `json:"sug_resblock"`
	Options                  interface{} `json:"options"`
	Commute                  interface{} `json:"commute"`
	PassThrough              string      `json:"pass_through"`
	ReCallType               string      `json:"re_call_type"`
	EmptyTips                string      `json:"empty_tips"`
	GuideSwitchForBrowseMode int         `json:"guide_switch_for_browse_mode"`
	Total                    int         `json:"total"`
	AdTotal                  int         `json:"ad_total"`
	PoiType                  string      `json:"poi_type"`
	CardCountWithoutAd       int         `json:"card_count_without_ad"`
	CardMaxCountForDisplay   int         `json:"card_max_count_for_display"`
	TemplateTypesForCount    []int       `json:"template_types_for_count"`
}

type Room struct {
	Zrrefer            string  `json:"zrrefer"`
	LocationId         int     `json:"location_id"`
	TemplateType       int     `json:"template_type"`
	Text               string  `json:"text,omitempty"`
	ExperimentId       string  `json:"experimentId,omitempty"`
	GroupId            string  `json:"groupId,omitempty"`
	SessionId          string  `json:"session_id,omitempty"`
	ContentType        int     `json:"content_type,omitempty"`
	AirQualified       int     `json:"air_qualified,omitempty"`
	ApartmentType      int     `json:"apartment_type,omitempty"`
	Area               string  `json:"area,omitempty"`
	AreaOrder          float64 `json:"area_order,omitempty"`
	Bedroom            int     `json:"bedroom,omitempty"`
	BizcircleName      string  `json:"bizcircle_name,omitempty"`
	CanSignDate        int     `json:"can_sign_date,omitempty"`
	CanSignLong        int     `json:"can_sign_long,omitempty"`
	CanSignTime        int     `json:"can_sign_time,omitempty"`
	Code               string  `json:"code,omitempty"`
	DistrictName       string  `json:"district_name,omitempty"`
	Face               string  `json:"face,omitempty"`
	Floor              string  `json:"floor,omitempty"`
	FloorTotal         string  `json:"floor_total,omitempty"`
	HasVideo           int     `json:"has_video,omitempty"`
	HasImmersivePic    int     `json:"has_immersive_pic,omitempty"`
	HouseCode          string  `json:"house_code,omitempty"`
	HouseId            string  `json:"house_id,omitempty"`
	HouseType          int     `json:"house_type,omitempty"`
	HouseTypeSaleState int     `json:"house_type_sale_state,omitempty"` // 自如寓销售状态，2为可签约
	Id                 string  `json:"id,omitempty"`
	InvId              string  `json:"inv_id,omitempty"`
	InvNo              string  `json:"inv_no,omitempty"`
	DisplayCode        string  `json:"display_code,omitempty"`
	IsAiLock           int     `json:"is_ai_lock,omitempty"`
	Lat                float64 `json:"lat,omitempty"`
	Lng                float64 `json:"lng,omitempty"`
	BuildingLat        float64 `json:"building_lat,omitempty"`
	BuildingLng        float64 `json:"building_lng,omitempty"`
	Name               string  `json:"name,omitempty"`
	NameV2             string  `json:"name_v2,omitempty"`
	Parlor             int     `json:"parlor,omitempty"`
	PhotoIcons         []struct {
		Url  string `json:"url"`
		Type int    `json:"type"`
	} `json:"photo_icons,omitempty"`
	Photo             string          `json:"photo,omitempty"`
	PhotoMin          string          `json:"photo_min,omitempty"`
	PhotoMinWebp      string          `json:"photo_min_webp,omitempty"`
	PhotoWebp         string          `json:"photo_webp,omitempty"`
	PicExperimentId   string          `json:"pic_experiment_id,omitempty"`
	PicGroupId        string          `json:"pic_group_id,omitempty"`
	Price             int             `json:"price,omitempty"`
	PriceUnit         string          `json:"price_unit,omitempty"`
	ResblockId        json.RawMessage `json:"resblock_id,omitempty"`
	ResblockName      string          `json:"resblock_name,omitempty"`
	SaleStatus        int             `json:"sale_status,omitempty"`
	ClientSaleStatus  int             `json:"client_sale_status,omitempty"`
	Source            string          `json:"source,omitempty"`
	StockStatus       string          `json:"stock_status,omitempty"`
	SubwayStationInfo string          `json:"subway_station_info,omitempty"`
	TagsRow1          []struct {
		BgImg      string  `json:"bg_img"`
		BgImgRatio float64 `json:"bg_img_ratio"`
		TailTag    struct {
			Style struct {
				Background string `json:"background"`
				Color      string `json:"color"`
			} `json:"style"`
			Title string `json:"title"`
		} `json:"tail_tag"`
	} `json:"tags_row1,omitempty"`
	Tags []struct {
		Title string `json:"title"`
	} `json:"tags,omitempty"`
	TagsRow2 []struct {
		Icon  string `json:"icon"`
		Style struct {
			Background string `json:"background"`
			Color      string `json:"color"`
		} `json:"style"`
		Title string `json:"title"`
	} `json:"tags_row2,omitempty"`
	DistanceInfo    string `json:"distance_info,omitempty"`
	Type            int    `json:"type,omitempty"`
	TypeText        string `json:"type_text,omitempty"`
	StyleTag        string `json:"style_tag,omitempty"`
	ZiroomVersionId int    `json:"ziroom_version_id,omitempty"`
	VersionName     string `json:"version_name,omitempty"`
	PriceStyle      struct {
		Color string `json:"color"`
	} `json:"price_style,omitempty"`
	CityCode       int           `json:"city_code,omitempty"`
	AntiTheftPrice []interface{} `json:"anti_theft_price,omitempty"`
	PhysicalInvNo  string        `json:"physical_inv_no,omitempty"`
	HaveLift       int           `json:"have_lift,omitempty"`
	Link           string        `json:"link,omitempty"`
	RoomNo         string        `json:"room_no,omitempty"`
	Router         struct {
		Target    string `json:"target"`
		Parameter struct {
			InvNo string `json:"invNo"`
		} `json:"parameter"`
	} `json:"router,omitempty"`
	CommuteResult interface{} `json:"commute_result"`
	News          interface{} `json:"news"`
	Collection    struct {
		IsAdded int `json:"is_added"`
		Num     int `json:"num"`
	} `json:"collection,omitempty"`
	RoomIntro  string   `json:"room_intro,omitempty"`
	RoomIntros []string `json:"room_intros,omitempty"`
	Feedback   struct {
		Title         string `json:"title"`
		FeedbackLibId int    `json:"feedback_lib_id"`
	} `json:"feedback,omitempty"`
	Features      []string `json:"features,omitempty"`
	Addrs         string   `json:"addrs,omitempty"`
	MediaComplete struct {
		Video    bool `json:"video"`
		FirstPic bool `json:"first_pic"`
		SpacePic bool `json:"space_pic"`
		Vr       bool `json:"vr,omitempty"`
	} `json:"media_complete,omitempty"`
	Has3D int `json:"has_3d,omitempty"`
}

// 搜索建议请求
type SearchSugRequest struct {
	CityCode  string `url:"city_code"`
	FromScene string `url:"from_scene"`
	Keyword   string `url:"keyword"`
}

// 搜索建议响应
type SearchSugResponse struct {
	Data         SearchSugData `json:"data"`
	ErrorCode    int           `json:"error_code"`
	ErrorMessage string        `json:"error_message"`
	Status       string        `json:"status"`
	TraceId      string        `json:"traceId"`
}

type SearchSugData struct {
	Title              string          `json:"title"`
	Keyword            string          `json:"keyword"`
	SugType            int             `json:"sug_type"`
	Total              int             `json:"total"`
	Items              []SearchSugItem `json:"items"`
	ErrorRecoveryItems []SearchSugItem `json:"error_recovery_items"`
}

type SearchSugItem struct {
	Mark        SearchSugMark    `json:"mark"`
	Title       string           `json:"title"`
	SubTitle    string           `json:"sub_title"`
	Labels      []SearchSugLabel `json:"labels"`
	SkipText    string           `json:"skip_text"`
	HouseNum    string           `json:"house_num"`
	HouseNums   []string         `json:"house_nums"`
	MinPrice    string           `json:"min_price"`
	Memo        string           `json:"memo"`
	Icon        string           `json:"icon"`
	ID          int              `json:"id"`
	Type        int              `json:"type"`
	Name        string           `json:"name"`
	Value       string           `json:"value"`
	Location    string           `json:"location"`
	CityCode    string           `json:"city_code"`
	CityName    string           `json:"city_name"`
	KeywdType   int              `json:"keywd_type"`
	IsRecommend int              `json:"is_recommend"`
	DisplayType int              `json:"display_type"`
}

type SearchSugMark struct {
	Icon    string         `json:"icon"`
	BigIcon string         `json:"big_icon"`
	Label   SearchSugLabel `json:"label"`
}

type SearchSugLabel struct {
	Text       string `json:"text"`
	Color      string `json:"color"`
	Background string `json:"background"`
}

type YmerService interface {
	GetRoomList(request RoomListV4Request) (*RoomListV4Response, error)
	GetPromotionRoomList(request RoomListV4Request) (*RoomListV4Response, error)
	GetSearchSug(request SearchSugRequest) (*SearchSugResponse, error)
	GetBizcircle(request BizcircleRequest) (*BizcircleResponse, error)
	GetResblockListV4(request ResblockListV4Request) (*ResblockListV4Response, error)
}

// 商圈查询请求
type BizcircleRequest struct {
	CityCode string `url:"city_code"`
}

// 商圈查询响应
type BizcircleResponse struct {
	Data         []DistrictBizcircle `json:"data"`
	ErrorCode    int                 `json:"error_code"`
	ErrorMessage string              `json:"error_message"`
	Status       string              `json:"status"`
	TraceId      string              `json:"traceId"`
}

type DistrictBizcircle struct {
	DistrictCode string      `json:"district_code"`
	DistrictName string      `json:"district_name"`
	Bizcircle    []Bizcircle `json:"bizcircle"`
}

type Bizcircle struct {
	BizcircleCode string `json:"bizcircle_code"`
	BizcircleName string `json:"bizcircle_name"`
	PinyinInitial string `json:"pinyin_initial"`
}

// ResblockListV4Request represents the request for resblock list v4
type ResblockListV4Request struct {
	ResblockIDs []string `json:"resblock_ids"`
}

// ResblockListV4Response represents the response for resblock list v4
type ResblockListV4Response struct {
	Data struct {
		Banner                   interface{}      `json:"banner"`
		Title                    string           `json:"title"`
		Resblock                 interface{}      `json:"resblock"`
		Rooms                    []ResblockRoomV4 `json:"rooms"`
		QuerySessionID           string           `json:"query_session_id"`
		SugCity                  interface{}      `json:"sug_city"`
		SugResblock              interface{}      `json:"sug_resblock"`
		Options                  interface{}      `json:"options"`
		Commute                  interface{}      `json:"commute"`
		PassThrough              string           `json:"pass_through"`
		Experiments              interface{}      `json:"experiments"`
		ReCallType               string           `json:"re_call_type"`
		EmptyTips                string           `json:"empty_tips"`
		GuideSwitchForBrowseMode int              `json:"guide_switch_for_browse_mode"`
		Total                    int              `json:"total"`
		AdTotal                  int              `json:"ad_total"`
		PoiType                  string           `json:"poi_type"`
		CardMaxCountForDisplay   int              `json:"card_max_count_for_display"`
		TemplateTypesForCount    []int            `json:"template_types_for_count"`
		FeedbackLib              []FeedbackLib    `json:"feedback_lib"`
	} `json:"data"`
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Status       string `json:"status"`
	TraceID      string `json:"traceId"`
}

// ResblockRoomV4 represents a room in the resblock list v4
type ResblockRoomV4 struct {
	Zrrefer           string           `json:"zrrefer"`
	LocationID        int              `json:"location_id"`
	TemplateType      int              `json:"template_type"`
	ResblockID        string           `json:"resblock_id"`
	Pic               string           `json:"pic"`
	Title             string           `json:"title"`
	Infos             []string         `json:"infos"`
	Info              string           `json:"info"`
	SubwayStationInfo string           `json:"subway_station_info"`
	Surround          []SurroundInfoV4 `json:"surround"`
	Price             int              `json:"price"`
	PriceUnit         string           `json:"price_unit"`
	Tags              []TagV4          `json:"tags"`
	Router            RouterV4         `json:"router"`
}

// SurroundInfoV4 represents surrounding information v4
type SurroundInfoV4 struct {
	Title string `json:"title"`
	Count int    `json:"count"`
}

// TagV4 represents a tag v4
type TagV4 struct {
	Title string `json:"title"`
}

// RouterV4 represents router information v4
type RouterV4 struct {
	Target    string                 `json:"target"`
	Parameter map[string]interface{} `json:"parameter"`
}

// FeedbackLib represents feedback library information
type FeedbackLib struct {
	ID    int            `json:"id"`
	Items []FeedbackItem `json:"items"`
}

// FeedbackItem represents a feedback item
type FeedbackItem struct {
	ID    int          `json:"id"`
	Icon  string       `json:"icon"`
	Title string       `json:"title"`
	Sub   *FeedbackSub `json:"sub,omitempty"`
}

// FeedbackSub represents feedback sub information
type FeedbackSub struct {
	Title string `json:"title"`
	Type  int    `json:"type"`
}
