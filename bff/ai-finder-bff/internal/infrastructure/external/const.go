package external

/**
 * rent type 产品线(product)
 */
const RENT_TYPE_0 = "0"           //不限
const RENT_TYPE_ZZ = "2"          //整租
const RENT_TYPE_HZ = "1"          //合租
const RENT_TYPE_ZZ_NEW = "4"      //整租（新版）
const RENT_TYPE_NC = "5"          //NC整租
const RENT_TYPE_MANSION = "8"     //豪宅
const RENT_TYPE_CULLING = "9"     //精选
const RENT_TYPE_ZRA = "6"         //自如寓
const RENT_TYPE_NC_TWO = "10"     //直租2.0
const RENT_TYPE_HEART = "12"      //心舍
const RENT_TYPE_SUPER_FAST = "13" //精选.业主直租

const RENT_TYPE_ZZ_ONE = "100"           //整租1居
const RENT_TYPE_ZZ_TWO = "101"           //整租2居
const RENT_TYPE_ZZ_MORE_THAN_TWO = "102" //整租2居+

const NEW_TYPE_0 = "0"           //不限
const NEW_TYPE_HZ = "1"          //合租
const NEW_TYPE_ZZ = "2"          //整租
const NEW_TYPE_ZZ_NEW = "4"      //整租（新版）
const NEW_TYPE_NC = "5"          //NC整租
const NEW_TYPE_MANSION = "8"     //豪宅
const NEW_TYPE_CULLING = "9"     //精选
const NEW_TYPE_NC_TWO = "10"     //直租2.0
const NEW_TYPE_ZRA = "11"        //自如寓
const NEW_TYPE_ZINN = "12"       //自如驿
const NEW_TYPE_ZRL = "14"        //自如里
const NEW_TYPE_SUPER_ZRYY = "15" //自如友寓
const NEW_TYPE_SUPER_CSZG = "16" //城市之光

/**
 * 租约类型
 */
const LEASETYPE_MONTH = "1"
const LEASETYPE_YEAR = "2"
const LEASETYPE_ZZ_YEAR = "3"               //整租才有
const LEASETYPE_ZZ_LONG_YEAR = "4"          //整租才有
const LEASETYPE_ZZ_YEAR_AND_LONG_YEAR = "5" //整租才有
const LEASETYPE_SHARE_RENT = "6"            //可拼租
// 一级分类
const LEASETYPE_FIRST_CATE_MONTH = "7"     //月租
const LEASETYPE_FIRST_CATE_SEASON = "8"    //季租
const LEASETYPE_FIRST_CATE_HALF_YEAR = "9" //半年租
// 自如寓
const LEASETYPE_FIRST_CATE_1_MONTH = "10" //1个月起租
const LEASETYPE_FIRST_CATE_2_MONTH = "11" //2个月起租
const LEASETYPE_FIRST_CATE_3_MONTH = "12" //3个月起租
const LEASETYPE_ALL_TAIL = "13"           //所有尾房
const LEASETYPE_LONG_RENT_1YGE = "14"     //长租1年及以上
const LEASETYPE_LONG_RENT_2YGE = "15"     //长租2年及以上
const LEASETYPE_LONG_RENT_3YGE = "16"     //长租3年及以上

/**
 * 居室
 */
const BEDROOM_ONE = "1"
const BEDROOM_TWO = "2"
const BEDROOM_TWO_PLUS = "7"
const BEDROOM_THREE = "3"
const BEDROOM_THREE_PLUS = "6"
const BEDROOM_FOUR = "4"
const BEDROOM_FOUR_PLUS = "5"
const BEDROOM_FIVE_PLUS = "25"

// new
const HZ_BEDROOM_UNLIMIT = "8"
const HZ_BEDROOM_TWO = "9"
const HZ_BEDROOM_THREE = "10"
const HZ_BEDROOM_FOUR_PLUS = "11"
const ZZ_BEDROOM_UNLIMIT = "12"
const ZZ_BEDROOM_ONE = "13"
const ZZ_BEDROOM_TWO = "14"
const ZZ_BEDROOM_THREE_PLUS = "15"
const MANSION_BEDROOM_UNLIMIT = "16"
const MANSION_BEDROOM_ONE = "17"
const MANSION_BEDROOM_TWO = "18"
const MANSION_BEDROOM_THREE_PLUS = "19"
const ZRA_BEDROOM_UNLIMIT = "20"
const ZRA_BEDROOM_OPENING = "21"
const ZRA_BEDROOM_SUITES = "22"
const ZRA_BEDROOM_BED = "23"

const HZ_BEDROOM_FIVE_PLUS = "24"      //友家-5户以上
const ZRA_BEDROOM_HZ = "25"            //自如寓-合住+床位
const MANSION_BEDROOM_FOUR_PLUS = "26" //曼舍-4户以上
const MANSION_BEDROOM_VILLA = "27"     //曼舍-洋房/别墅
const MANSION_BEDROOM_THREE = "28"

/**
 * 自如寓类型
 */
const ZRA_OPENING = "0"
const ZRA_SUITES = "1"
const ZRA_BED = "2"

// 床位
const ZRA_NEW_BED = "6"
const ZRA_LOFT_OPENING = "3" //Loft 开间
const ZRA_LOFT_SUITES = "4"  //Loft 套间

//加入 loft 开间和loft套间

/**
 * 租住类型
 */
const TYPE_HZ = "1"
const TYPE_ZZ = "2"
const TYPE_ZZ_BEDROOM_ONE = "3"
const TYPE_ZZ_BEDROOM_TWO = "4"
const TYPE_ZZ_BEDROOM_THREE = "5"
const TYPE_HZ_BEDROOM_TWO = "6"
const TYPE_HZ_BEDROOM_THREE = "7"
const TYPE_HZ_BEDROOM_MORE = "8"
const TYPE_ZZ_NEW = "10"
const TYPE_ZZ_NEW_BEDROOM_ONE = "11"
const TYPE_ZZ_NEW_BEDROOM_TWO = "12"
const TYPE_ZZ_NEW_BEDROOM_THREE = "13"
const TYPE_NC = "20"
const TYPE_NC_BEDROOM_ONE = "21"
const TYPE_NC_BEDROOM_TWO = "22"
const TYPE_NC_BEDROOM_THREE = "23"
const TYPE_MANSION = "24"
const TYPE_MANSION_BEDROOM_ONE = "25"
const TYPE_MANSION_BEDROOM_TWO = "26"
const TYPE_MANSION_BEDROOM_TWO_PLUS = "27"
const TYPE_3LDK = "28"
const TYPE_CULLING = "29"
const TYPE_CULLING_BEDROOM_ONE = "30"
const TYPE_CULLING_BEDROOM_TWO = "31"
const TYPE_CULLING_BEDROOM_THREE = "32"
const TYPE_ZRA = "33"
const TYPE_ZRA_OPENING = "34" // 开间
const TYPE_ZRA_SUITES = "35"  // 套间
const TYPE_ZRA_BED = "36"     // 床位
const TYPE_NC_TWO = "37"
const TYPE_NC_TWO_BEDROOM_ONE = "38"
const TYPE_NC_TWO_BEDROOM_TWO = "39"
const TYPE_NC_TWO_BEDROOM_THREE = "40"
const TYPE_HZ_BEDROOM_FIVE_MORE = "41"

/**
 * 排序
 */
const SORT_DEFAULT = "1"
const SORT_PRICE_ASC = "2"
const SORT_PRICE_DESC = "3"
const SORT_AREA_ASC = "4"
const SORT_AREA_DESC = "5"
const SORT_RENT_DESC = "6"            //可入住日期从远到近
const SORT_DISTANCE_ASC = "7"         //附近找房时距离排序
const SORT_CREATETIME_DESC = "8"      //按照添加时间排序
const SORT_BY_BIZ_ASC = "9"           //按照商圈分数排序
const SORT_BY_DISTRICT_ASC = "10"     //按照商圈分数排序
const SORT_RENT_ASC = "11"            //可入住日期从近到远
const SORT_SUBWAY_DISTANCE_ASC = "12" //地铁站距离从近到远
// const SORT_FRESH_DESC = "13"          //新上优先
const SORT_HOT_DESC = "14"        //热度优先
const SORT_NOT_BROWSE_DESC = "15" //未浏览优先
const SORT_ACTIVITY_DESC = "16"   //活动房源优先

/**
 * 朝向
 */
const FACE_EAST = "1"
const FACE_SOUTH = "2"
const FACE_WEST = "3"
const FACE_NORTH = "4"
const FACE_SOUTH_NORTH = "10"

/**
 * 版本
 */
const HZ_VERSION_ONE = "1"
const HZ_VERSION_TWO = "2"
const HZ_VERSION_THREE = "3"
const HZ_VERSION_FOUR = "4"
const HZ_VERSION_FIVE = "5"
const HZ_VERSION_SIX = "6"
const HZ_VERSION_SIX_SMART = "6.1"
const HZ_VERSION_SEVEN = "7"
const HZ_VERSION_SEVEN_TF = "7.1"
const ZZ_VERSION_ONE = "10"
const ZZ_VERSION_THREE = "11"
const ZZ_VERSION_NC = "12"
const ZZ_VERSION_CULLING = "13"
const ZZ_VERSION_TWO = "14"
const ZZ_VERSION_FOUR = "15"
const ZZ_VERSION_HEART = "16"
const ZZ_VERSION_HEART_PLUS = "17"
const ZZ_VERSION_SUPER_FAST = "18"     //精选.业主直租
const ZZ_VERSION_NC_AND_CULLING = "19" //原业主原装 + 原自如精选
const ZZ_VERSION_ZENGYI_SJY = "20"     //增益速优家
const ZZ_VERSION_HEART20 = "21"        //心舍2.0
const ZZ_VERSION_MANSION_ONE = "31"    //曼舍1.0
const ZZ_VERSION_OWNER_DECORATE = "32" //业主原装
const ZZ_VERSION_MANSION_TWO = "33"    // 曼舍2.0
const ZZ_VERSION_HEART30 = "34"        // 心舍3.0

/**
 * 风格
 */
const STYLE_PLAIN = "1"
const STYLE_LATTE = "2"
const STYLE_PUDDING = "3"
const STYLE_KAPOK = "4"
const STYLE_MISOL = "5"
const STYLE_YOGURT = "6"
const STYLE_FRESH = "7"
const STYLE_BIRD_NEST = "8"
const STYLE_VINTAGE = "9"
const STYLE_FAIRYTALE = "10"
const STYLE_JANEEYRE = "11"
const STYLE_FENGNING = "12"
const STYLE_BIPO = "13"
const STYLE_GANLAN = "14"
const STYLE_LVYAN = "15"
const STYLE_XUESONG = "16"

/**
 * 特色
 */
const FEATURE_NEAR_SUBWAY = "1"          // 离地铁近(1000米内)
const FEATURE_NEW_RESBLOCK = "2"         // 新小区(2005后小区，包含2005)
const FEATURE_INDEPENDENT_TOLIET = "3"   // 独立卫生间
const FEATURE_INDEPENDENT_BALCONY = "4"  // 独立阳台
const FEATURE_FIRST_RENT = "5"           // 首次出租
const FEATURE_SCHOOL_DISTRICT = "6"      // 学区房
const FEATURE_ALLOW_PETS = "7"           // 可以养宠物
const FEATURE_HIGH_GREENRATIO = "8"      // 高绿化率(大于等于30%)
const FEATURE_HAS_CARPORT = "9"          // 有车位
const FEATURE_SHARE_HEAT = "10"          // 集体供暖
const FEATURE_CENTRAL_HEAT = "11"        // 中央供暖
const FEATURE_SELF_HEAT = "12"           // 独立供暖
const FEATURE_SMART_LOCK = "13"          // 智能锁
const FEATURE_HAS_LIFT = "14"            // 有电梯
const FEATURE_MORETHEN_TWO_TOLIET = "15" // 两个卫生间
const FEATURE_FREE_REPAIR = "16"         // 免费维修
const FEATURE_CLEAN_TWO_WEEK = "17"      // 双周保洁
const FEATURE_CLEAN_MONTH = "18"         // 月度保洁
const FEATURE_WIFI = "19"                // wifi覆盖
// const FEATURE_LIFT                  = "20" // 有电梯
const FEATURE_ZIROOM_REPAIR = "21"    // 自如维修
const FEATURE_WALLHANG_HEAT = "22"    // 壁挂炉供暖
const FEATURE_SHARE_LIVINGROOM = "23" // 共享客厅
const FEATURE_SMART_WE = "24"         // 智能水电
const FEATURE_SMART_HOME = "25"       // 智能家居
const FEATURE_PUBLIC_DISTRICT = "26"  // 公共区域大
const FEATURE_HAS_VIDEO = "27"        // 视频看房
const FEATURE_HAS_VR = "28"           // VR看房
const FEATURE_AIR_OK = "29"           // 空检合格
const FEATURE_NOT_FIRST_RENT = "30"   // 非首次出租
const FEATURE_SELF_HELP = "31"        // 自助看房
const FEATURE_BREATH = "32"           // E0+空检合格

const FEATURE_EMPTY_TWO_ROOM = "33"       // 有2间空房
const FEATURE_EMPTY_THREE_ROOM = "34"     // 有3间空房
const FEATURE_EMPTY_FOUR_ROOM = "35"      // 有4间空房
const FEATURE_EMPTY_FOUR_ROOM_PLUS = "36" // 有4间空房以上

const FEATURE_NEW_WIND = "37"    // 新风系统 智能新风
const FEATURE_WELL_CHOSEN = "38" // 精选软装房
const FEATURE_AIR_PURGE = "39"   // 智能空净

const FEATURE_BELOW_1500 = "40" // 1500以下

const FEATURE_EMPTY_ALL = "41"  // 全部空置
const FEATURE_SMART_LIFE = "43" //智能生活

const FEATURE_LOFT = "44"   //loft
const FEATURE_DUPLEX = "45" //复式

const FEATURE_GYM = "46"                       //健身房
const FEATURE_RESBLOCK_YOUNGER_THAN_TEN = "47" //新小区(建成时间小于10年)
const FEATURE_RESBLOCK_OLDER_THAN_TEN = "48"   //成熟小区(建成时间大于10年)
const FEATURE_CAN_SHARE_RENT = "49"            //可拼租
const FEATURE_CAN_SHARE_RENT_MASTER = "50"     //可拼租 主卧
const FEATURE_CAN_SHARE_RENT_SECOND = "51"     //可拼租 次卧
const FEATURE_NEWLY = "52"                     //房源上新
const FEATURE_GTE_TWO_TOLIET = "60"            //2022.7.14 新增筛选元素【户型特色-多卫生间】
const FEATURE_INDEPENDENT_SUITES = "61"        //套房
const FEATURE_IMMERSIVE_VIDEO = "62"           //全景视频
const FeatureXs2Intelligence = "63"            //xs2.0全屋智能
const FEATURE_MAKSIM = "64"                    //猫王音乐主题房
const FEATURE_WASHROOM = "65"                  //盥洗室
const FEATURE_SECROOMEMPTY = "66"              //次卧空配
const FEATURE_BIG_ROOM = "67"                  //卧室空间大
const FEATURE_BIG_PARLOR = "68"                //公共客厅大
const FEATURE_MASTER_ROOM_PRO = "69"           //卧室Pro
const FEATURE_NO_LIMIT = "70"                  //不限
const FEATURE_HIGH_LEVE_VIEW = "71"            //高层视野
const FEATURE_NORTH_SOUTH_VENTILATION = "72"   //南北通透
const FEATURE_WINDOW_WALL = "73"               //落地窗

/**
 * 周边
 */
const AROUND_SUBWAY = "1"          // 离地铁近(1000米内)
const AROUND_NEW_RESBLOCK = "2"    // 新小区(2005后小区，包含2005)
const AROUND_SCHOOL = "3"          // 离学校近
const AROUND_HIGH_GREENRATIO = "4" // 高绿化率(大于等于30%)

const IsFirstRentYes = "69" //首次出租
const IsFirstRentNo = "70"  //非首次出租

const ResblockFeatureHighGreenRatio = "72"                //高绿化率
const ResblockFeatureSeparationOfPeopleAndVehicles = "73" //人车分流
const ResblockFeatureCivilianWaterAndElectricity = "71"   //民水民电

/**
 * rapid
 */
const RAPID_BUD = "1"                           //惠蕾计划
const RAPID_HAIYAN = "2"                        //海燕计划
const RAPID_BREATH = "3"                        //深呼吸房源
const RAPID_CAN_SIGNING = "4"                   //可签约
const RAPID_INDEPENDENT_TOLIET = "5"            //独立卫生间
const RAPID_SUBWAY = "6"                        //离地铁近
const RAPID_TWO_TOLIET = "7"                    //两个卫生间
const RAPID_SMART_WE = "8"                      //智能水电
const RAPID_SMART_HOME = "9"                    //智能家居
const RAPID_VIDEO = "10"                        //视频看房
const RAPID_DAILY_PREFERENT = "11"              //每日特惠
const RAPID_IS_NOT_NEW = "12"                   // 非首次出租
const RAPID_AIR_IS_OK = "13"                    // 空气检测合格
const RAPID_PRICE_10000 = "14"                  // price 10000-15000
const RAPID_SELF_HELP = "15"                    //自助看房
const RAPID_ACTIVITY_TWO = "16"                 // 暖冬小心意 @王倩云
const RAPID_HAS_VR = "17"                       // VR看房
const RAPID_HIGH_GREENRATIO = "18"              // 高绿化率(大于等于30%)
const RAPID_FOREIGNER = "19"                    // 外国人专享
const RAPID_ACTIVITY_ONE = "20"                 // 限时优惠 @王倩云
const RAPID_SHORT_RENT_30_90 = "22"             // 短租【1-3个月】
const RAPID_SHORT_RENT_90_365 = "23"            // 3个月及以上
const RAPID_BIRD_NEST = "24"                    // 燕窝房源
const RAPID_TALENT_PLAN = "25"                  // 人才安居
const RAPID_FRESH_AIR = "26"                    // 新风系统
const RAPID_WELL_CHOSEN = "27"                  // 精选软装房
const RAPID_AIR_PURGE = "28"                    // 智能空净
const RAPID_LONG_RENT_366_729 = "30"            //长租2年以下
const RAPID_LONG_RENT_730 = "31"                //长租2年
const RAPID_LONG_RENT_731 = "32"                //长租2年以上
const RAPID_YEAR = "33"                         //长租一年
const RAPID_BELOW_1500 = "29"                   // 1500以下
const RAPID_HZ_VERSION_SIX_SMART = "34"         //友家6.0ZHOME
const RAPID_EMPTY_ALL = "41"                    //全部空置
const RAPID_CAN_PRE_SIGN_7_DAY = "42"           //可预签7天
const RAPID_SMART_LIEF = "43"                   // 智能生活
const RAPID_BIG_ZONG = "44"                     //big zong活动
const RAPID_BIG_ZONG_HZ = "45"                  //big zong 合租
const RAPID_BIG_ZONG_ZZ = "46"                  //big zong 整租
const RAPID_SHORT_RENT_1_29 = "21"              // 灵活短签【<=1个月】
const RAPID_SHORT_RENT_1M2M = "50"              // 灵活短签【1-2个月】
const RAPID_SHORT_RENT_2M4M = "51"              // 灵活短签【2-4个月】
const RAPID_SHORT_RENT_4M7M = "52"              // 灵活短签【4-7个月】
const RAPID_SHORT_RENT_7M12M = "53"             // 灵活短签【7-12个月】
const RAPID_SUBWAY_DISTANCE_300 = "54"          // 距离地铁站<300米
const RAPID_SUBWAY_DISTANCE_600 = "55"          // 距离地铁站300-600米
const RAPID_SUBWAY_DISTANCE_1000 = "56"         // 距离地铁站600-1000米
const RAPID_SUBWAY_DISTANCE_2000 = "57"         // 距离地铁站1000-2000米
const RAPID_SUBWAY_DISTANCE_3000 = "58"         // 距离地铁站>2000米
const RAPID_PREFERENTIAL_SHORT_SIGNATURE = "59" //特惠短签
const RAPID_LONG_RENT_3Y = "60"                 //长租3年
const RAPID_LONG_RENT_3YP = "61"                //长租3年以上
const RAPID_LONG_RENT_1YGE = "62"               //长租1年及以上
const RAPID_LONG_RENT_2YGE = "63"               //长租2年及以上
const RAPID_LONG_RENT_3YGE = "64"               //长租3年及以上
const RAPID_SHORT = "65"                        //灵活短签（一年以下）
const RAPID_SHORT_RENT_4M12M = "66"             //灵活短签【4-12个月】
const RAPID_SHORT_RENT_CM1 = "67"               //灵活短签(含1月)
const RAPID_SHORT_RENT_CM2 = "68"               //灵活短签(含2月)
const RAPID_SHORT_RENT_CM3 = "69"               //灵活短签(含3月)
const RAPID_SHORT_RENT_CM4 = "70"               //灵活短签(含4月)
const RAPID_SHORT_RENT_CM5 = "71"               //灵活短签(含5月)
const RAPID_SHORT_RENT_CM6 = "72"               //灵活短签(含6月)
const RAPID_SHORT_RENT_CM7 = "73"               //灵活短签(含7月)
const RAPID_SHORT_RENT_CM8 = "74"               //灵活短签(含8月)
const RAPID_SHORT_RENT_CM9 = "75"               //灵活短签(含9月)
const RAPID_SHORT_RENT_CM10 = "76"              //灵活短签(含10月)
const RAPID_SHORT_RENT_CM11 = "77"              //灵活短签(含11月)
const RAPID_SHORT_RENT_0M4M = "78"              //灵活短签【<=4个月】

const CHANNEL_ALL = "1"
const CHANNEL_SPECAIL_OFFER = "2"
const CHANNEL_BROWSED = "3"
const CHANNEL_NOT_BROWSE = "4"
const CHANNEL_COLLECT = "5"
const CHANNEL_NEW_SHELVES = "6"
const CHANNEL_HZ_70 = "7"
const CHANNEL_ZZ_HEART_20 = "8"
const CHANNEL_SOFT_DECORATION_XINSHE = "9" //心舍软装

/**
 * 标签
 */
const TAG_CAN_SIGNING = "1"        // 可签约
const TAG_CAN_RESERVE = "2"        // 可预订
const TAG_CAN_TURN = "3"           // 转租房
const TAG_TRAINEE = "4"            // 实习生
const TAG_ACTIVITY_BUD = "5"       // 惠蕾计划
const TAG_ACTIVITY_BIRD_NEST = "6" // 燕窝房源
const TAG_CAN_PRE_SIGN = "7"       // 可预签
const TAG_CAN_PRE_BOOK = "9"       // 可预定
const TagShanDing = "11"           //可闪订-放在房源状态分组里（实际使用houseTag来标识）
const TAG_CAN_SHARE_RENT = "12"    //可拼租
const TAG_HAS_LIFT = "13"          // 有电梯
const TAG_FOCUS_HOUSE = "14"       // 聚焦房源

const TagShanDingHouseTag = "77" //houseTag的值

/**
 * 自如活动
 */
const ACTIVITY_TWO = "1"                    // 暖冬小心意
const ACTIVITY_BUD = "2"                    // 惠蕾房源
const ACTIVITY_TRAINEE = "3"                // 自如实习生
const ACTIVITY_ONE = "4"                    // 限时优惠
const ACTIVITY_BIRD_NEST = "5"              // 燕窝房源
const ACTIVITY_TALENT_PLAN = "8"            // 人才安居
const ACTIVITY_BIG_ZONG = "9"               // 北京促销BigZong
const ACTIVITY_BIG_ZONG_HZ = "10"           // 北京促销BigZong
const ACTIVITY_BIG_ZONG_ZZ = "11"           // 北京促销BigZong
const ACTIVITY_20201111_FLUSHSALE = "12"    //2020双十一秒杀
const ACTIVITY_GOOD_HOUSE_FOR_SEE = "13"    //必看好房
const ACTIVITY_GAIN_RENT = "14"             //增益活动房
const ACTIVITY_SOFT_DECORATION_RENEW = "15" //软装焕新
const ACTIVITY_SZNTH = "16"                 //十周年特惠
const ACTIVITY_YXHF = "17"                  //优选好房
const ACTIVITY_BYSYX = "18"                 //毕业生优选
const ACTIVITY_OPEN_HOUSE = "19"            //open house
const ACTIVITY_SUPRISE_HOUSE = "20"         //惊喜彩蛋房
const ACTIVITY_ENJOYZIROOM = "21"           //人人享自如
const ACTIVITY_WARMWINTER = "22"            //品质租房节
const ACTIVITY_SPECIAL_PRICE_ZRA = "23"     //限时特价
const ACTIVITY_TAIL_DISCOUNT = "24"         //尾房折扣活动
const ACTIVITY_FOCUS_HOUSE = "25"           //聚焦房源
const ACTIVITY_MAISON_DISCOUNT = "26"       //曼舍补贴房源

var Activities = []string{
	ACTIVITY_TWO,
	ACTIVITY_BUD,
	ACTIVITY_TRAINEE,
	ACTIVITY_ONE,
	ACTIVITY_BIRD_NEST,
	ACTIVITY_TALENT_PLAN,
	ACTIVITY_BIG_ZONG,
	ACTIVITY_BIG_ZONG_HZ,
	ACTIVITY_BIG_ZONG_ZZ,
	ACTIVITY_20201111_FLUSHSALE,
	ACTIVITY_GOOD_HOUSE_FOR_SEE,
	ACTIVITY_GAIN_RENT,
	ACTIVITY_SOFT_DECORATION_RENEW,
	ACTIVITY_SZNTH,
	ACTIVITY_YXHF,
	ACTIVITY_BYSYX,
	ACTIVITY_OPEN_HOUSE,
	ACTIVITY_SUPRISE_HOUSE,
	ACTIVITY_ENJOYZIROOM,
	ACTIVITY_WARMWINTER,
	ACTIVITY_SPECIAL_PRICE_ZRA,
	ACTIVITY_TAIL_DISCOUNT,
	ACTIVITY_FOCUS_HOUSE,
}

/**
 * 空气质量
 */
const AIR_IS_OK = "1" // 空气质量已检测

/**
 * 供暖方式
 */
const HEATING_CENTRAL = "1" // 中央供暖
const HEATING_SELF = "2"    // 独立供暖
const HEATING_SHARE = "3"   // 集体供暖

/**
 * 房型
 */
const LAYOUT_INDEPENDENT_TOLIET = "1"        // 独立卫生间
const LAYOUT_INDEPENDENT_BALCONY = "2"       // 独立阳台
const LAYOUT_INDEPENDENT_SUITES = "3"        //  套房
const LAYOUT_SIHEYUAN = "4"                  //  四合院
const LAYOUT_GYM = "5"                       //  健身房
const LAYOUT_MORE_THAN_TWO_TOLIET = "6"      // 多卫生间
const LAYOUT_LOFT = "7"                      // loft
const LAYOUT_INDEPENDENT_TOLIET_NEW = "l-1"  // 独立卫生间
const LAYOUT_INDEPENDENT_BALCONY_NEW = "l-2" // 独立阳台
const LAYOUT_INDEPENDENT_SUITES_NEW = "l-3"  //  套房
const LAYOUT_SIHEYUAN_NEW = "l-4"            //  四合院

/**
 * 首次出租空置天数
 */
const VACANCY_DAYS = 30

/**
 * 床位字段
 */
const NEW_BEDROOM_BED = 200

/**
 * 出租单位
 */
const RENT_UNIT_BED = 1
const RENT_UNIT_HOUSE = 2
const RENT_UNIT_ROOM = 3
const RENT_UNIT_ZHENGE = 4

const LIVE_WITH_SELF = "1"
const LIVE_WITH_FRENDS = "2"
const LIVE_WITH_KIDS = "3"
const LIVE_WITH_OLD = "4"
const LIVE_WITH_KIDS_AND_OLD = "5"

const TransportTypeWalk = "walk"       //步行
const TransportTypeRide = "ride"       //骑行
const TransportTypeDrive = "drive"     //驾车
const TransportTypeTransit = "transit" //公交

// feature_house
const FEATURE_HOUSE_NEAR_PARK = "1"            //近公园
const FEATURE_HOUSE_HIGH_LEVE_VIEW = "2"       //高层视野
const FEATURE_HOUSE_NEAR_SUBWAY = "3"          //离地铁近
const FEATURE_HOUSE_XS2INTELLIGENCE = "4"      //全屋智能
const FEATURE_HOUSE_PUBLIC_DISTRICT = "5"      //公共区域大
const FEATURE_HOUSE_SMART_WE = "6"             //智能水电
const FEATURE_HOUSE_NEAR_MALL = "7"            //近商场
const FEATURE_HOUSE_NEW_RESBLOCK = "8"         //新小区
const FEATURE_HOUSE_GYM = "9"                  //健身房
const FEATURE_HOUSE_LEISURE_PUBLIC_ZONE = "10" //休闲公区
const FEATURE_HOUSE_SHARED_KITCHEN = "11"      //公共厨房

/**
 * 特色
 * @var array
 */
var Features = map[string]string{
	FEATURE_ALLOW_PETS:                "可养宠物",
	FEATURE_CENTRAL_HEAT:              "中央供暖",
	FEATURE_FIRST_RENT:                "首次出租",
	FEATURE_HAS_CARPORT:               "有车位",
	FEATURE_HIGH_GREENRATIO:           "绿化率高",
	FEATURE_INDEPENDENT_BALCONY:       "独立阳台",
	FEATURE_INDEPENDENT_TOLIET:        "独立卫生间",
	FEATURE_NEAR_SUBWAY:               "离地铁近",
	FEATURE_NEW_RESBLOCK:              "新小区",
	FEATURE_SCHOOL_DISTRICT:           "离学校近",
	FEATURE_SELF_HEAT:                 "独立供暖",
	FEATURE_SHARE_HEAT:                "集体供暖",
	FEATURE_SMART_LOCK:                "智能锁",
	FEATURE_HAS_LIFT:                  "有电梯",
	FEATURE_MORETHEN_TWO_TOLIET:       "多卫生间",
	FEATURE_EMPTY_TWO_ROOM:            "有2间空房",
	FEATURE_EMPTY_THREE_ROOM:          "有3间空房",
	FEATURE_EMPTY_FOUR_ROOM:           "有4间空房",
	FEATURE_EMPTY_FOUR_ROOM_PLUS:      "有4间空房",
	FEATURE_EMPTY_ALL:                 "全部空置",
	FEATURE_FREE_REPAIR:               "及时维修",
	FEATURE_CLEAN_TWO_WEEK:            "月度双次保洁",
	FEATURE_CLEAN_MONTH:               "月度保洁",
	FEATURE_WIFI:                      "WiFi覆盖",
	FEATURE_ZIROOM_REPAIR:             "自如维修",
	FEATURE_WALLHANG_HEAT:             "壁挂炉",
	FEATURE_SHARE_LIVINGROOM:          "共享客厅",
	FEATURE_SMART_HOME:                "智能家居",
	FEATURE_SMART_WE:                  "智能水电",
	FEATURE_PUBLIC_DISTRICT:           "公共区域大",
	FEATURE_HAS_VIDEO:                 "视频看房",
	FEATURE_HAS_VR:                    "VR看房",
	FEATURE_AIR_OK:                    "空气质量已检测",
	FEATURE_NOT_FIRST_RENT:            "非首次出租",
	FEATURE_SELF_HELP:                 "自助看房",
	FEATURE_BREATH:                    "深呼吸",
	FEATURE_NEW_WIND:                  "智能新风",
	FEATURE_WELL_CHOSEN:               "精选软装房",
	FEATURE_AIR_PURGE:                 "智能空净",
	FEATURE_BELOW_1500:                "1500元以下",
	FEATURE_SMART_LIFE:                "智能生活",
	FEATURE_LOFT:                      "loft",
	FEATURE_DUPLEX:                    "复式",
	FEATURE_GYM:                       "健身房",
	FEATURE_RESBLOCK_YOUNGER_THAN_TEN: "新小区",
	FEATURE_RESBLOCK_OLDER_THAN_TEN:   "成熟小区",
	FEATURE_CAN_SHARE_RENT:            "可拼租",
	FEATURE_CAN_SHARE_RENT_MASTER:     "可拼主卧",
	FEATURE_CAN_SHARE_RENT_SECOND:     "可拼次卧",
	FEATURE_NEWLY:                     "上新",
	FEATURE_GTE_TWO_TOLIET:            "多卫生间",
	FEATURE_INDEPENDENT_SUITES:        "独立起居室",
	FEATURE_MAKSIM:                    "心舍软装焕新",
	FEATURE_SECROOMEMPTY:              "次卧DIY",
	FEATURE_WASHROOM:                  "公共盥洗室",
	FEATURE_BIG_ROOM:                  "卧室空间大",
	FEATURE_BIG_PARLOR:                "公共客厅大",
	FEATURE_MASTER_ROOM_PRO:           "卧室Pro",
	FeatureXs2Intelligence:            "全屋智能",
}

// 排序
var DicSorts = map[string]string{
	SORT_DEFAULT:             "默认",
	SORT_PRICE_ASC:           "价格从低到高",
	SORT_PRICE_DESC:          "价格从高到低",
	SORT_AREA_ASC:            "面积从小到大",
	SORT_AREA_DESC:           "面积从大到小",
	SORT_RENT_DESC:           "可入住日期从远到近",
	SORT_DISTANCE_ASC:        "距离从近到远",
	SORT_CREATETIME_DESC:     "添加时间先后",
	SORT_BY_BIZ_ASC:          "按照商圈分数排序",
	SORT_BY_DISTRICT_ASC:     "按照城区分数排序",
	SORT_RENT_ASC:            "可入住日期从近到远",
	SORT_SUBWAY_DISTANCE_ASC: "地铁站距离从近到远",
}

/**
 * 产品线
 */
var Products = map[string]string{
	"0":                        "不限",
	"":                         "不限",
	RENT_TYPE_HZ:               "合租",
	RENT_TYPE_ZZ:               "整租",
	RENT_TYPE_MANSION:          "曼舍",
	RENT_TYPE_ZRA:              "自如寓",
	RENT_TYPE_ZZ_ONE:           "整租1居",
	RENT_TYPE_ZZ_TWO:           "整租2居",
	RENT_TYPE_ZZ_MORE_THAN_TWO: "整租2居+",
}

/**
 * 标签
 * @var array
 */
var Tags = map[string]string{
	TAG_CAN_SIGNING:        "可立即入住",
	TAG_CAN_RESERVE:        "可预订",
	TAG_CAN_TURN:           "自如客转租",
	TAG_TRAINEE:            "自如实习生",
	TAG_ACTIVITY_BUD:       "惠蕾房源",
	TAG_ACTIVITY_BIRD_NEST: "燕窝房源",
	TAG_CAN_PRE_SIGN:       "可预签",
	TAG_CAN_PRE_BOOK:       "可预定",
}

/**
 * 朝向
 * @var array
 */
var Faces = map[string]string{
	FACE_EAST:        "东",
	FACE_NORTH:       "北",
	FACE_SOUTH:       "南",
	FACE_SOUTH_NORTH: "南北",
	FACE_WEST:        "西",
}

/**
 * 版本
 */
var Versions = map[string]string{
	HZ_VERSION_FOUR:           "友家4.0",
	HZ_VERSION_ONE:            "友家1.0",
	HZ_VERSION_THREE:          "友家3.0",
	HZ_VERSION_TWO:            "友家2.0",
	HZ_VERSION_FIVE:           "友家5.0",
	HZ_VERSION_SIX_SMART:      "友家6.0ZHOME",
	HZ_VERSION_SIX:            "友家6.0",
	HZ_VERSION_SEVEN:          "友家7.0",
	HZ_VERSION_SEVEN_TF:       "友家7.1套房",
	ZZ_VERSION_ONE:            "整租1.0",
	ZZ_VERSION_TWO:            "整租2.0",
	ZZ_VERSION_THREE:          "整租3.0",
	ZZ_VERSION_NC:             "业主直租",
	ZZ_VERSION_CULLING:        "精选",
	ZZ_VERSION_FOUR:           "整租4.0",
	ZZ_VERSION_HEART:          "心舍",
	ZZ_VERSION_HEART_PLUS:     "心舍Plus",
	ZZ_VERSION_SUPER_FAST:     "精选·业主直租",
	ZZ_VERSION_NC_AND_CULLING: "业主原装",
	ZZ_VERSION_ZENGYI_SJY:     "速优家",
	ZZ_VERSION_HEART20:        "心舍2.0",
	ZZ_VERSION_MANSION_ONE:    "曼舍1.0",
	ZZ_VERSION_OWNER_DECORATE: "业主原装",
	ZZ_VERSION_MANSION_TWO:    "曼舍2.0",
	ZZ_VERSION_HEART30:        "心舍3.0",
}

/**
 * 风格
 */
var Styles = map[string]string{
	STYLE_FRESH:     "清语",
	STYLE_KAPOK:     "木棉",
	STYLE_LATTE:     "拿铁",
	STYLE_MISOL:     "米苏",
	STYLE_PLAIN:     "原味",
	STYLE_PUDDING:   "布丁",
	STYLE_YOGURT:    "优格",
	STYLE_BIRD_NEST: "燕窝",
	STYLE_VINTAGE:   "古着",
	STYLE_FAIRYTALE: "维思",
	STYLE_JANEEYRE:  "简爱",
}

/**
 * 空置房间数
 */
var EmptyRooms = map[string]string{
	FEATURE_EMPTY_TWO_ROOM:       "2间",
	FEATURE_EMPTY_THREE_ROOM:     "3间",
	FEATURE_EMPTY_FOUR_ROOM:      "4间",
	FEATURE_EMPTY_FOUR_ROOM_PLUS: "4间以上",
	FEATURE_EMPTY_ALL:            "全部空置",
}

/**
 * 租住类型
 * @var array
 */
var Types = map[string]string{
	TYPE_HZ:               "友家",
	TYPE_ZZ:               "整租",
	TYPE_ZZ_BEDROOM_ONE:   "整租一居",
	TYPE_ZZ_BEDROOM_TWO:   "整租二居",
	TYPE_ZZ_BEDROOM_THREE: "整租三居",
}

/**
 * 签约类型
 * @var array
 */
var LeaseTypes = map[string]string{
	LEASETYPE_MONTH:        "可短签",
	LEASETYPE_YEAR:         "年租",
	LEASETYPE_ZZ_YEAR:      "长租1年",
	LEASETYPE_ZZ_LONG_YEAR: "长租1年以上",
}

var LeaseTypesDuration = map[string]string{
	RAPID_SHORT_RENT_1_29:   "<=1个月",
	RAPID_SHORT_RENT_30_90:  "1-3个月",
	RAPID_SHORT_RENT_90_365: "3个月及以上",
	RAPID_LONG_RENT_366_729: "长租2年以下",
	RAPID_LONG_RENT_730:     "长租2年",
	RAPID_LONG_RENT_731:     "长租2年以上",
	RAPID_SHORT_RENT_1M2M:   "1-2个月",
	RAPID_SHORT_RENT_2M4M:   "2-4个月",
	RAPID_SHORT_RENT_4M7M:   "4-7个月",
	RAPID_SHORT_RENT_7M12M:  "7-12个月",
	RAPID_SHORT_RENT_4M12M:  "4-12个月",
	RAPID_LONG_RENT_3Y:      "长租3年",
	RAPID_LONG_RENT_3YP:     "长租3年以上",
	RAPID_LONG_RENT_1YGE:    "长租1年及以上",
	RAPID_LONG_RENT_2YGE:    "长租2年及以上",
	RAPID_LONG_RENT_3YGE:    "长租3年及以上",
	RAPID_SHORT:             "短租不限",
	RAPID_SHORT_RENT_CM1:    "1月",
	RAPID_SHORT_RENT_CM2:    "2月",
	RAPID_SHORT_RENT_CM3:    "3月",
	RAPID_SHORT_RENT_CM4:    "4月",
	RAPID_SHORT_RENT_CM5:    "5月",
	RAPID_SHORT_RENT_CM6:    "6月",
	RAPID_SHORT_RENT_CM7:    "7月",
	RAPID_SHORT_RENT_CM8:    "8月",
	RAPID_SHORT_RENT_CM9:    "9月",
	RAPID_SHORT_RENT_CM10:   "10月",
	RAPID_SHORT_RENT_CM11:   "11月",
}

/**
 * 居室
 * @var array
 */
var Bedrooms = map[string]string{
	BEDROOM_ONE:       "1居",
	BEDROOM_TWO:       "2居",
	BEDROOM_THREE:     "3居",
	BEDROOM_FOUR:      "4居",
	BEDROOM_FOUR_PLUS: "4居+",
	BEDROOM_FIVE_PLUS: "5居+",
	// new
	HZ_BEDROOM_UNLIMIT:         "不限",
	HZ_BEDROOM_TWO:             "2户合住",
	HZ_BEDROOM_THREE:           "3户合住",
	HZ_BEDROOM_FOUR_PLUS:       "4户合住",
	HZ_BEDROOM_FIVE_PLUS:       "5户及以上",
	ZZ_BEDROOM_UNLIMIT:         "不限",
	ZZ_BEDROOM_ONE:             "1居",
	ZZ_BEDROOM_TWO:             "2居",
	ZZ_BEDROOM_THREE_PLUS:      "3居+",
	MANSION_BEDROOM_UNLIMIT:    "不限",
	MANSION_BEDROOM_ONE:        "1居",
	MANSION_BEDROOM_TWO:        "2居",
	MANSION_BEDROOM_THREE:      "3居",
	MANSION_BEDROOM_THREE_PLUS: "3居+",
	MANSION_BEDROOM_FOUR_PLUS:  "4居+",
	ZRA_BEDROOM_UNLIMIT:        "不限",
	ZRA_BEDROOM_BED:            "床位",
	ZRA_BEDROOM_SUITES:         "套间",
	ZRA_BEDROOM_OPENING:        "开间",
}

// 活动
var Activitys = map[string]string{
	ACTIVITY_TWO:         "暖冬小心意",
	ACTIVITY_BUD:         "惠蕾房源",
	ACTIVITY_TRAINEE:     "自如实习生",
	ACTIVITY_ONE:         "8周年大促",
	ACTIVITY_BIRD_NEST:   "燕窝房源",
	ACTIVITY_TALENT_PLAN: "人才安居",
}

// 供暖
var Heatings = map[string]string{
	HEATING_CENTRAL: "中央供暖",
	HEATING_SELF:    "独立供暖",
	HEATING_SHARE:   "集体供暖",
}

var Lives = map[string]string{
	LIVE_WITH_SELF:         "自己",
	LIVE_WITH_FRENDS:       "情侣、朋友",
	LIVE_WITH_KIDS:         "小孩",
	LIVE_WITH_OLD:          "老人",
	LIVE_WITH_KIDS_AND_OLD: "老人&小孩",
}

// 精选房型
var Layouts = map[string]string{
	LAYOUT_INDEPENDENT_TOLIET:      "主卧独卫",
	LAYOUT_INDEPENDENT_BALCONY:     "独立阳台",
	LAYOUT_INDEPENDENT_SUITES:      "套房",
	LAYOUT_SIHEYUAN:                "四合院",
	LAYOUT_GYM:                     "健身房",
	LAYOUT_MORE_THAN_TWO_TOLIET:    "多卫生间",
	LAYOUT_LOFT:                    "loft",
	LAYOUT_INDEPENDENT_SUITES_NEW:  "套房",
	LAYOUT_SIHEYUAN_NEW:            "四合院",
	LAYOUT_INDEPENDENT_TOLIET_NEW:  "独立卫生间",
	LAYOUT_INDEPENDENT_BALCONY_NEW: "独立阳台",
}

var Arounds = map[string]string{
	AROUND_SUBWAY:          "离地铁近",
	AROUND_NEW_RESBLOCK:    "新小区",
	AROUND_SCHOOL:          "离学校近",
	AROUND_HIGH_GREENRATIO: "绿化率高",
}

// 快筛映射表
var Rapids = map[string]string{
	RAPID_BUD:                          "惠蕾房源",
	RAPID_HAIYAN:                       "海燕计划",
	RAPID_BREATH:                       "深呼吸",
	RAPID_CAN_SIGNING:                  "可签约",
	RAPID_INDEPENDENT_TOLIET:           "独立卫生间",
	RAPID_SUBWAY:                       "离地铁近",
	RAPID_TWO_TOLIET:                   "两个卫生间",
	RAPID_SMART_WE:                     "智能水电",
	RAPID_SMART_HOME:                   "智能家居",
	RAPID_VIDEO:                        "视频看房",
	RAPID_DAILY_PREFERENT:              "每日特惠",
	RAPID_SELF_HELP:                    "自助看房",
	RAPID_IS_NOT_NEW:                   "非首次出租",
	RAPID_AIR_IS_OK:                    "空气质量已检测",
	RAPID_PRICE_10000:                  "<1.5万",
	RAPID_ACTIVITY_TWO:                 "限时特惠",
	RAPID_HAS_VR:                       "VR看房",
	RAPID_HIGH_GREENRATIO:              "绿化率高",
	RAPID_FOREIGNER:                    "外国人专享",
	RAPID_ACTIVITY_ONE:                 "限时优惠",
	RAPID_BIRD_NEST:                    "燕窝房源",
	RAPID_TALENT_PLAN:                  "人才安居",
	RAPID_FRESH_AIR:                    "智能新风",
	RAPID_WELL_CHOSEN:                  "精选软装房",
	RAPID_AIR_PURGE:                    "智能空净",
	RAPID_BELOW_1500:                   "1500元以下",
	RAPID_EMPTY_ALL:                    "全部空置",
	RAPID_HZ_VERSION_SIX_SMART:         "友家6.0ZHOME",
	RAPID_CAN_PRE_SIGN_7_DAY:           "可预签5天",
	RAPID_SMART_LIEF:                   "智能生活",
	RAPID_BIG_ZONG:                     "最高免2月租金",
	RAPID_BIG_ZONG_HZ:                  "最高免2月租金",
	RAPID_BIG_ZONG_ZZ:                  "最高免1.2月租金",
	RAPID_PREFERENTIAL_SHORT_SIGNATURE: "特惠短签",

	RAPID_SHORT_RENT_1_29:   "<=1个月",
	RAPID_SHORT_RENT_30_90:  "1-3个月",
	RAPID_SHORT_RENT_90_365: "3个月及以上",
	RAPID_LONG_RENT_366_729: "长租2年以下",
	RAPID_LONG_RENT_730:     "长租2年",
	RAPID_LONG_RENT_731:     "长租2年以上",
	RAPID_SHORT_RENT_1M2M:   "1-2个月",
	RAPID_SHORT_RENT_2M4M:   "2-4个月",
	RAPID_SHORT_RENT_4M7M:   "4-7个月",
	RAPID_SHORT_RENT_7M12M:  "7-12个月",
	RAPID_SHORT_RENT_4M12M:  "4-12个月",
	RAPID_LONG_RENT_3Y:      "长租3年",
	RAPID_LONG_RENT_3YP:     "长租3年以上",
	RAPID_LONG_RENT_1YGE:    "长租1年及以上",
	RAPID_LONG_RENT_2YGE:    "长租2年及以上",
	RAPID_LONG_RENT_3YGE:    "长租3年及以上",
	RAPID_SHORT:             "短租不限",
	RAPID_SHORT_RENT_CM1:    "1月",
	RAPID_SHORT_RENT_CM2:    "2月",
	RAPID_SHORT_RENT_CM3:    "3月",
	RAPID_SHORT_RENT_CM4:    "4月",
	RAPID_SHORT_RENT_CM5:    "5月",
	RAPID_SHORT_RENT_CM6:    "6月",
	RAPID_SHORT_RENT_CM7:    "7月",
	RAPID_SHORT_RENT_CM8:    "8月",
	RAPID_SHORT_RENT_CM9:    "9月",
	RAPID_SHORT_RENT_CM10:   "10月",
	RAPID_SHORT_RENT_CM11:   "11月",
}

/**
 * 楼盘特色
 * @var array
 */
var ResblockFeature = map[string]string{
	ResblockFeatureCivilianWaterAndElectricity:   "民水民电",
	ResblockFeatureSeparationOfPeopleAndVehicles: "人车分流",
	ResblockFeatureHighGreenRatio:                "绿化率高",
}

var IsFirstRent = map[string]string{
	IsFirstRentYes: "首次出租",
	IsFirstRentNo:  "非首次出租",
}

var FeatureHouse = map[string]string{
	FEATURE_HOUSE_NEAR_PARK:           "近公园",
	FEATURE_HOUSE_HIGH_LEVE_VIEW:      "高层视野",
	FEATURE_HOUSE_NEAR_SUBWAY:         "近地铁",
	FEATURE_HOUSE_XS2INTELLIGENCE:     "全屋智能",
	FEATURE_HOUSE_PUBLIC_DISTRICT:     "公共区域大",
	FEATURE_HOUSE_SMART_WE:            "智能水电",
	FEATURE_HOUSE_NEAR_MALL:           "近商场",
	FEATURE_HOUSE_NEW_RESBLOCK:        "新小区",
	FEATURE_HOUSE_GYM:                 "健身房",
	FEATURE_HOUSE_LEISURE_PUBLIC_ZONE: "休闲公区",
	FEATURE_HOUSE_SHARED_KITCHEN:      "公共厨房",
}

// 获取特色
func GetFeature(feature string) string {
	if val, ok := Features[feature]; ok {
		return val
	}
	return ""
}

// 获取特色
func GetLayout(val string) string {
	if val, ok := Layouts[val]; ok {
		return val
	}
	return ""
}

// 获取特色
func GetSort(val string) string {
	if val, ok := DicSorts[val]; ok {
		return val
	}
	return ""
}
