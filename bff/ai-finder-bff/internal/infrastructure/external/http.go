package external

import (
	"net/http"
	"time"
)

type HTTPClientConfig struct {
	BaseURL string

	Timeout time.Duration
}

type baseHTTPClient struct {
	client *http.Client
}

func newBaseHTTPClient(cfg HTTPClientConfig) *baseHTTPClient {
	return &baseHTTPClient{
		client: &http.Client{
			Timeout: cfg.Timeout,
		},
	}
}

func (c *baseHTTPClient) Do(req *http.Request) (*http.Response, error) {
	// common logging
	return c.client.Do(req)
}
