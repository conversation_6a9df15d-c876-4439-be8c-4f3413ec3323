package external

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/google/go-querystring/query"

	appsvc "ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

type resbHttpClient struct {
	client  *baseHTTPClient
	baseURL string
}

func NewResbHttpClient(cfg HTTPClientConfig) appsvc.ResblockService {
	return &resbHttpClient{
		client:  newBaseHTTPClient(cfg),
		baseURL: cfg.BaseURL,
	}
}

func (c *resbHttpClient) GetResblocks(request appsvc.ResblockRequest) (*appsvc.ResblockResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/bizcircle/resblocks", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	log.Info("invoke resb resblocks", zap.String("url", finalURL))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.ResblockResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}
