package external

import (
	appsvc "ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"
	"bytes"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"net/http"
)

type rentOqHttpClient struct {
	*baseHTTPClient
	baseURL string
}

func NewRentOqHttpClient(cfg HTTPClientConfig) appsvc.RentOqService {
	return &rentOqHttpClient{
		baseHTTPClient: newBaseHTTPClient(cfg),
		baseURL:        cfg.BaseURL,
	}
}

func (c *rentOqHttpClient) WatchingDtlListKeeper(req appsvc.RentOqReq) (*appsvc.WatchingDtlListKeeperResp, error) {
	url := fmt.Sprintf("%s/crm/house/watching/dtl/list/keeper", c.baseURL)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	request.Header.Set("token", req.Token)
	request.Header.Set("Content-Type", "application/json")

	response, err := c.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer response.Body.Close()

	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	// 打印原始 JSON 字符串（用于调试）
	logger.GetLogger().Info("WatchingDtlListBase", zap.String("url", fmt.Sprintf("%s/crm/house/watching/dtl/list/keeper", c.baseURL)), zap.Any("resp", string(bodyBytes)))

	var resp appsvc.RentOqResp[appsvc.WatchingDtlListKeeperResp]
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, fmt.Errorf("failed to invoke rentoq'keeper, decode response: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("failed to invoke rentoq'keeper, response: %s", resp.Message)
	}

	return &resp.Data, nil
}

func (c *rentOqHttpClient) WatchingDtlListHouse(req appsvc.RentOqReq) (*appsvc.WatchingDtlListHouseResp, error) {
	url := fmt.Sprintf("%s/crm/house/watching/dtl/list/house", c.baseURL)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	request.Header.Set("token", req.Token)
	request.Header.Set("Content-Type", "application/json")

	response, err := c.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer response.Body.Close()

	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	// 打印原始 JSON 字符串（用于调试）
	logger.GetLogger().Info("WatchingDtlListBase", zap.String("url", fmt.Sprintf("%s/crm/house/watching/dtl/list/house", c.baseURL)), zap.Any("resp", string(bodyBytes)))

	var resp appsvc.RentOqResp[appsvc.WatchingDtlListHouseResp]
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, fmt.Errorf("failed to invoke rentoq'house, decode response: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("failed to invoke rentoq'house, response: %s", resp.Message)
	}

	return &resp.Data, nil
}

func (c *rentOqHttpClient) WatchingDtlListBase(req appsvc.RentOqReq) (*appsvc.WatchingDtlListBaseResp, error) {
	url := fmt.Sprintf("%s/crm/house/watching/dtl/list/base", c.baseURL)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	request.Header.Set("token", req.Token)
	request.Header.Set("Content-Type", "application/json")

	response, err := c.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer response.Body.Close()

	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	// 打印原始 JSON 字符串（用于调试）
	logger.GetLogger().Info("WatchingDtlListBase", zap.String("url", fmt.Sprintf("%s/crm/house/watching/dtl/list/base", c.baseURL)), zap.Any("resp", string(bodyBytes)))

	//if response.StatusCode != http.StatusOK {
	//	return nil, fmt.Errorf("failed to invoke dp, unexpected status code: %d", response.StatusCode)
	//}

	var resp appsvc.RentOqResp[appsvc.WatchingDtlListBaseResp]
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, fmt.Errorf("failed to invoke rentoq'base, decode response: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("failed to invoke rentoq'base, response: %s", resp.Message)
	}

	return &resp.Data, nil
}
