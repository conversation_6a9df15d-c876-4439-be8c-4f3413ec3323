package external

import (
	appsvc "ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"
	"bytes"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"net/http"
)

// RentOrderHttpClient represents the HTTP client for rent order service
type rentOrderHttpClient struct {
	*baseHTTPClient
	baseURL string
}

// NewRentOrderHttpClient creates a new instance of RentOrderHttpClient
func NewRentOrderHttpClient(cfg HTTPClientConfig) appsvc.RentOrderService {
	return &rentOrderHttpClient{
		baseHTTPClient: newBaseHTTPClient(cfg),
		baseURL:        cfg.BaseURL,
	}
}

// CreateReservation implements service.RentOrderService interface
func (c *rentOrderHttpClient) CreateReservation(req appsvc.ReserveRequest) (*appsvc.ReserveResponse, error) {
	url := fmt.Sprintf("%s/crm/reserve/user/app/shopping/car", c.baseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("token", req.Token)

	logger.GetLogger().Info("CreateReservation request", zap.String("url", fmt.Sprintf("%s/crm/reserve/user/app/shopping/car", c.baseURL)), zap.Any("request", string(jsonBody)))
	response, err := c.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", response.StatusCode)
	}

	bodyBytes, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 打印原始 JSON 字符串（用于调试）
	logger.GetLogger().Info("CreateReservation request", zap.String("url", fmt.Sprintf("%s/crm/reserve/user/app/shopping/car", c.baseURL)), zap.Any("resp", string(bodyBytes)))

	// 反序列化为结构体
	var resp appsvc.ReserveResponse
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &resp, nil
}
