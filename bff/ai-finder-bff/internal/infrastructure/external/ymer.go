package external

import (
	"ai-finder-bff/internal/pkg/logger"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"go.uber.org/zap"

	"github.com/google/go-querystring/query"

	appsvc "ai-finder-bff/internal/application/service"
)

type ymerHttpClient struct {
	client  *baseHTTPClient
	baseURL string
}

func NewYmerHttpClient(cfg HTTPClientConfig) appsvc.YmerService {
	return &ymerHttpClient{
		client:  newBaseHTTPClient(cfg),
		baseURL: cfg.BaseURL,
	}
}

func (c *ymerHttpClient) GetRoomList(request appsvc.RoomListV4Request) (*appsvc.RoomListV4Response, error) {
	log := logger.GetLogger()

	// 构建请求URL
	url := fmt.Sprintf("%s/v4/room/list.json", c.baseURL)

	// 将请求参数序列化为JSON
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	log.Info("invoke ymer room list", zap.Any("params", request))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	if request.AppVersion != "" && request.AppVersion != "null" {
		httpReq.Header.Set("Accept", fmt.Sprintf("application/json;version=%s", request.AppVersion))
	} else {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		fmt.Println("读取 Body 失败:", err)
	}
	bodyString := string(bodyBytes)
	println(bodyString)

	// 解析响应
	var response appsvc.RoomListV4Response
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

func (c *ymerHttpClient) GetPromotionRoomList(request appsvc.RoomListV4Request) (*appsvc.RoomListV4Response, error) {
	// 构建请求URL
	url := fmt.Sprintf("%s/v1/promotion/list.json", c.baseURL)
	params, _ := query.Values(request)

	finalUrl := url + "?" + params.Encode()
	// 创建HTTP请求

	httpReq, err := http.NewRequest(http.MethodGet, finalUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Accept", "version=5")
	httpReq.Header.Set("imei", "2a68abd3d6745cc20c069a2ffb2cd8ce168e8530")
	httpReq.Header.Set("uid", "4803129d-0b1f-4b3e-9d63-4151181a32a3")

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		fmt.Println("读取 Body 失败:", err)
	}
	bodyString := string(bodyBytes)
	println(bodyString)

	// 解析响应
	var response appsvc.RoomListV4Response
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

func (c *ymerHttpClient) GetSearchSug(request appsvc.SearchSugRequest) (*appsvc.SearchSugResponse, error) {
	// 构建请求URL
	baseURL := fmt.Sprintf("%s/v1/index/sug.json", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.SearchSugResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

func (c *ymerHttpClient) GetBizcircle(request appsvc.BizcircleRequest) (*appsvc.BizcircleResponse, error) {
	// 构建请求URL
	baseURL := fmt.Sprintf("%s/v1/setting/bizcircle.json", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.BizcircleResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

// GetResblockListV4 gets the list of resblocks by their IDs
func (c *ymerHttpClient) GetResblockListV4(req appsvc.ResblockListV4Request) (*appsvc.ResblockListV4Response, error) {
	url := fmt.Sprintf("%s/v4/resblock/list.json", c.baseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal request body failed: %w", err)
	}

	httpReq, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	if req.AppVersion != "" && req.AppVersion != "null" {
		httpReq.Header.Set("Accept", fmt.Sprintf("application/json;version=%s", req.AppVersion))
	} else {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("send request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.ResblockListV4Response
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}
