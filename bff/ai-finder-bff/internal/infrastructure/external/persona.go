package external

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"go.uber.org/zap"
)

type httpPersonaClient struct {
	client *baseHTTPClient
	cfg    HTTPClientConfig
}

func NewPersonaHttpClient(cfg HTTPClientConfig) service.PersonaService {
	return &httpPersonaClient{
		cfg:    cfg,
		client: newBaseHTTPClient(cfg),
	}
}

type personaResponse[T any] struct {
	Status  int    `json:"status"`
	Data    T      `json:"data"`
	Message string `json:"message"`
}

type PersonaAPIError struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
}

func (e *PersonaAPIError) Error() string {
	return fmt.Sprintf("persona service return error: status: %d, message: %s", e.Status, e.Message)
}

// 获取用户标签
// 调用 http://wiki.ziroom.com/pages/viewpage.action?pageId=663258283
func (c *httpPersonaClient) GetUserLabels(request service.UserLabelsRequest) (service.UserLabels, error) {
	body, err := json.Marshal(request)
	if err != nil {
		return nil, errors.New("json marshal request failed")
	}

	logger.GetLogger().Info("personarequest", zap.String("url", fmt.Sprintf("%s/api/public/v2/user/info", c.cfg.BaseURL)), zap.Any("request", request))
	httpReq, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/api/public/v2/user/info", c.cfg.BaseURL), bytes.NewReader(body))
	if err != nil {
		return nil, err
	}

	httpReq.Header.Set("Content-Type", "application/json")

	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, err
	}

	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("persona service return http status: %s", httpResp.Status)
	}

	// parse response
	var response personaResponse[service.UserLabels]
	err = json.NewDecoder(httpResp.Body).Decode(&response)
	if err != nil {
		return nil, err
	}

	if response.Status != 0 {
		return nil, &PersonaAPIError{
			Status:  response.Status,
			Message: response.Message,
		}
	}

	return response.Data, nil
}
