package external

import (
	appsvc "ai-finder-bff/internal/application/service"
	"encoding/json"
	"fmt"
	"net/http"
)

type darkPortalHttpClient struct {
	*baseHTTPClient
	baseURL string
}

func NewDarkPortalHttpClient(cfg HTTPClientConfig) appsvc.DarkPortalService {
	return &darkPortalHttpClient{
		baseHTTPClient: newBaseHTTPClient(cfg),
		baseURL:        cfg.BaseURL,
	}
}

func (c *darkPortalHttpClient) GetUserInfo(token string) (*appsvc.DPUserInfoResponse, error) {
	url := fmt.Sprintf("%s/users/info/v1", c.baseURL)

	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	request.Header.Set("token", token)
	request.Header.Set("Client-Version", "1.1")
	request.Header.Set("Sys", "app")
	request.Header.Set("Request-Id", "1")
	request.Header.Set("Accept", "*/*")

	response, err := c.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer response.Body.Close()

	//if response.StatusCode != http.StatusOK {
	//	return nil, fmt.Errorf("failed to invoke dp, unexpected status code: %d", response.StatusCode)
	//}

	var resp appsvc.DPResponse
	if err := json.NewDecoder(response.Body).Decode(&resp); err != nil {
		return nil, fmt.Errorf("failed to invoke dp, decode response: %w", err)
	}

	if resp.Code != "20000" {
		return nil, fmt.Errorf("failed to invoke dp, response: %s", resp.Message)
	}

	return &resp.DPUserInfoResponse, nil
}
