package external

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/google/go-querystring/query"

	appsvc "ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

type erebusHttpClient struct {
	client  *baseHTTPClient
	baseURL string
}

func NewErebusHttpClient(cfg HTTPClientConfig) appsvc.ErebusService {
	return &erebusHttpClient{
		client:  newBaseHTTPClient(cfg),
		baseURL: cfg.BaseURL,
	}
}

func (c *erebusHttpClient) GetBaseData(request appsvc.BaseDataRequest) (*appsvc.BaseDataResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/filter", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	log.Info("invoke erebus filter", zap.String("url", finalURL))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.BaseDataResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		log.Error("decode response failed", zap.Error(err))
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

func (c *erebusHttpClient) Aggregate(request appsvc.AggregateRequest) (*appsvc.AggregateResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	url := fmt.Sprintf("%s/aggregate", c.baseURL)

	// 将请求体序列化为JSON
	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("marshal request failed: %w", err)
	}

	log.Info("invoke erebus aggregate",
		zap.String("url", url),
		zap.String("request", string(reqBody)))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.AggregateResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

// GetCommonCondition 获取公共条件
func (c *erebusHttpClient) GetCommonCondition(request appsvc.CommonConditionRequest) (*appsvc.CommonConditionResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/filter/common", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	log.Info("invoke erebus common filter", zap.String("url", finalURL))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.CommonConditionResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		log.Error("decode response failed", zap.Error(err))
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}
