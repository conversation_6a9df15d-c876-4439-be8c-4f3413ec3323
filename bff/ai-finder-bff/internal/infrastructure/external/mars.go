package external

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/google/go-querystring/query"

	appsvc "ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

type marsHttpClient struct {
	client  *baseHTTPClient
	baseURL string
}

func NewMarsHttpClient(cfg HTTPClientConfig) appsvc.MarsService {
	return &marsHttpClient{
		client:  newBaseHTTPClient(cfg),
		baseURL: cfg.BaseURL,
	}
}

func (c *marsHttpClient) GetBizcircleDetail(request appsvc.BizcircleDetailRequest) (*appsvc.BizcircleDetailResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/mars/bizcircle/k2/detail", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	log.Info("invoke mars bizcircle detail", zap.String("url", finalURL))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.BizcircleDetailResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		log.Error("decode response", zap.Error(err))
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

func (c *marsHttpClient) GetResblockDetail(request appsvc.ResblockDetailRequest) (*appsvc.ResblockDetailResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/mars/resblock/detail/id", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	log.Info("invoke mars resblock detail", zap.String("url", finalURL))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.ResblockDetailResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		log.Error("decode response", zap.Error(err))
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}

func (c *marsHttpClient) GetResblockPicDetail(request appsvc.ResblockDetailRequest) (*appsvc.ResblockPicDetailResponse, error) {
	log := logger.GetLogger()

	// 构建请求URL
	baseURL := fmt.Sprintf("%s/mars/resblock/imageInfo/id", c.baseURL)

	// 将请求参数转换为查询字符串
	params, err := query.Values(request)
	if err != nil {
		return nil, fmt.Errorf("encode params failed: %w", err)
	}

	finalURL := baseURL + "?" + params.Encode()

	log.Info("invoke mars resblock detail", zap.String("url", finalURL))

	// 创建HTTP请求
	httpReq, err := http.NewRequest(http.MethodGet, finalURL, nil)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 发送请求
	httpResp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("do request failed: %w", err)
	}
	defer httpResp.Body.Close()

	// 检查响应状态码
	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", httpResp.StatusCode)
	}

	// 读取响应体
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	var response appsvc.ResblockPicDetailResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		log.Error("decode response", zap.Error(err))
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	return &response, nil
}
