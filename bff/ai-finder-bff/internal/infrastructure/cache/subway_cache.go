package cache

import (
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

// SubwayCache 地铁数据缓存
type SubwayCache struct {
	data        atomic.Value // 存储 *cacheData
	mutex       sync.Mutex   // 只用于更新操作
	ttl         time.Duration
	cities      []string // 支持的城市列表
	dataFetcher SubwayDataFetcher
}

type SubwayDataFetcher interface {
	GetBaseData(request service.BaseDataRequest) (*service.BaseDataResponse, error)
}

type cacheData struct {
	// city -> line -> stations
	subways map[string]map[string]domain.SubwayLine
	// city -> station_code -> (line_code, station)
	stations  map[string]map[string]stationIndex
	timestamp time.Time
}

type stationIndex struct {
	lineCode string
	station  domain.SubwayStation
}

// NewSubwayCache 创建地铁数据缓存
func NewSubwayCache(ttl time.Duration, fetcher SubwayDataFetcher) *SubwayCache {
	cache := &SubwayCache{
		ttl:         ttl,
		dataFetcher: fetcher,
		cities:      []string{"110000", "310000", "440100", "440300", "330100", "320100", "510100", "120000", "420100", "320500"},
	}

	// 初始化 atomic.Value
	cache.data.Store(&cacheData{
		subways:   make(map[string]map[string]domain.SubwayLine),
		stations:  make(map[string]map[string]stationIndex),
		timestamp: time.Time{},
	})

	// 启动定期更新
	go cache.startPeriodicUpdate(ttl)
	return cache
}

// GetLines 获取城市所有地铁线
func (c *SubwayCache) GetLines(cityCode string) ([]domain.SubwayLine, bool) {
	data := c.data.Load().(*cacheData)

	// 检查缓存是否过期
	if time.Since(data.timestamp) > c.ttl {
		return nil, false
	}

	cityLines, ok := data.subways[cityCode]
	if !ok {
		return nil, false
	}

	// 转换 map 为 slice
	lines := make([]domain.SubwayLine, 0, len(cityLines))
	for _, line := range cityLines {
		lines = append(lines, line)
	}

	log := logger.GetLogger()
	log.Debug("subway cache hit",
		zap.String("city", cityCode),
		zap.Int("lines", len(lines)))
	return lines, true
}

// GetLine 获取指定地铁线
func (c *SubwayCache) GetLine(cityCode, lineCode string) (*domain.SubwayLine, bool) {
	data := c.data.Load().(*cacheData)

	// 检查缓存是否过期
	if time.Since(data.timestamp) > c.ttl {
		return nil, false
	}

	cityLines, ok := data.subways[cityCode]
	if !ok {
		return nil, false
	}

	line, ok := cityLines[lineCode]
	if !ok {
		return nil, false
	}

	log := logger.GetLogger()
	log.Debug("subway line cache hit",
		zap.String("city", cityCode),
		zap.String("line", lineCode))
	return &line, true
}

// GetStation 获取指定地铁站
func (c *SubwayCache) GetStation(cityCode, stationCode string) (*domain.SubwayStation, string, bool) {
	data := c.data.Load().(*cacheData)

	// 检查缓存是否过期
	if time.Since(data.timestamp) > c.ttl {
		return nil, "", false
	}

	cityStations, ok := data.stations[cityCode]
	if !ok {
		return nil, "", false
	}

	idx, ok := cityStations[stationCode]
	if !ok {
		return nil, "", false
	}

	log := logger.GetLogger()
	log.Debug("subway station cache hit",
		zap.String("city", cityCode),
		zap.String("station", stationCode),
		zap.String("line", idx.lineCode))
	return &idx.station, idx.lineCode, true
}

// Update 更新缓存
func (c *SubwayCache) Update() error {
	log := logger.GetLogger()
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 再次检查缓存是否已被其他协程更新
	if !c.IsExpired() {
		return nil
	}

	log.Info("updating subway cache")

	// 获取所有城市的数据
	allData := make(map[string][]domain.SubwayLine)
	for _, cityCode := range c.cities {
		lines, err := c.fetchCitySubways(cityCode)
		if err != nil {
			log.Error("fetch city subways failed",
				zap.String("city", cityCode),
				zap.Error(err))
			continue
		}
		allData[cityCode] = lines
	}

	c.updateData(allData)
	return nil
}

// 获取单个城市的地铁数据
func (c *SubwayCache) fetchCitySubways(cityCode string) ([]domain.SubwayLine, error) {
	baseReq := service.BaseDataRequest{
		CityCode: cityCode,
	}

	baseResp, err := c.dataFetcher.GetBaseData(baseReq)
	if err != nil {
		return nil, fmt.Errorf("get base data failed: %w", err)
	}

	lines := make([]domain.SubwayLine, 0)
	for _, subway := range baseResp.Data.Subway {
		stations := make([]domain.SubwayStation, 0)
		for _, station := range subway.Stations {
			stations = append(stations, domain.SubwayStation{
				Code: strconv.Itoa(station.Code),
				Name: station.Name,
				Lat:  station.Lat,
				Lng:  station.Lng,
			})
		}

		lines = append(lines, domain.SubwayLine{
			Code:     fmt.Sprint(subway.Code),
			Name:     subway.Name,
			Stations: stations,
		})
	}

	return lines, nil
}

// 启动定期更新
func (c *SubwayCache) startPeriodicUpdate(interval time.Duration) {
	// 立即执行一次 Update
	if err := c.Update(); err != nil {
		log := logger.GetLogger()
		log.Error("initial update failed", zap.Error(err))
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		if err := c.Update(); err != nil {
			log := logger.GetLogger()
			log.Error("periodic update failed", zap.Error(err))
		}
	}
}

// IsExpired 检查缓存是否过期
func (c *SubwayCache) IsExpired() bool {
	data := c.data.Load().(*cacheData)
	return time.Since(data.timestamp) > c.ttl
}

// updateData 更新缓存数据
func (c *SubwayCache) updateData(newData map[string][]domain.SubwayLine) {
	// 转换数据结构
	subways := make(map[string]map[string]domain.SubwayLine)
	stations := make(map[string]map[string]stationIndex)

	for city, lines := range newData {
		cityLines := make(map[string]domain.SubwayLine)
		cityStations := make(map[string]stationIndex)

		for _, line := range lines {
			// 通过Name检索
			cityLines[line.Name] = line
			// 建立站点索引
			for _, station := range line.Stations {
				// 通过Name检索
				cityStations[station.Name] = stationIndex{
					lineCode: line.Name,
					station:  station,
				}
			}
		}
		subways[city] = cityLines
		stations[city] = cityStations
	}

	// 创建新的缓存数据
	data := &cacheData{
		subways:   subways,
		stations:  stations,
		timestamp: time.Now(),
	}

	// 原子替换
	c.data.Store(data)

	log := logger.GetLogger()
	log.Info("subway cache updated",
		zap.Int("cities", len(subways)),
		zap.Time("timestamp", data.timestamp))
}
