package cache

import (
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

// SubwayCache 地铁数据缓存
type CommonConditionCache struct {
	data        atomic.Value // 存储 *cacheData
	mutex       sync.Mutex   // 只用于更新操作
	ttl         time.Duration
	cities      []string // 支持的城市列表
	dataFetcher CommonConditionDataFetcher
}

type CommonConditionDataFetcher interface {
	GetCommonCondition(request service.CommonConditionRequest) (*service.CommonConditionResponse, error)
}

type commonConditionCache struct {
	common    map[string]map[string]map[string][]Pair
	timestamp time.Time
}

type Pair struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// NewCommonConditionCache 创建公共条件缓存
func NewCommonConditionCache(ttl time.Duration, fetcher CommonConditionDataFetcher) *CommonConditionCache {
	cache := &CommonConditionCache{
		ttl:         ttl,
		dataFetcher: fetcher,
		cities:      []string{"110000", "310000", "440100", "440300", "330100", "320100", "510100", "120000", "420100", "320500"},
	}

	// 初始化 atomic.Value
	cache.data.Store(&commonConditionCache{
		common:    make(map[string]map[string]map[string][]Pair),
		timestamp: time.Time{},
	})

	// 启动定期更新
	go cache.startPeriodicUpdate(ttl)
	return cache
}

// GetParams 获取城市所有地铁线
func (c *CommonConditionCache) GetParams(cityCode string, product string, paramKey string) ([]Pair, bool) {
	data := c.data.Load().(*commonConditionCache)

	// 检查缓存是否过期
	//if time.Since(data.timestamp) > c.ttl {
	//	return nil, false
	//}

	cityProductParams, ok := data.common[cityCode]
	if !ok {
		return nil, false
	}

	productParams, ok := cityProductParams[product]
	if !ok {
		return nil, false
	}

	pairs, ok := productParams[paramKey]
	if !ok {
		return nil, false
	}

	return pairs, true
}

// Update 更新缓存
func (c *CommonConditionCache) Update() error {
	log := logger.GetLogger()
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 再次检查缓存是否已被其他协程更新
	if !c.IsExpired() {
		return nil
	}

	log.Info("updating subway cache")

	// 获取所有城市的数据
	allData := make(map[string]*service.CommonConditionResponse)
	for _, cityCode := range c.cities {
		commonConditionResp, err := c.fetchCommonData(cityCode)
		if err != nil {
			log.Error("fetch city subways failed",
				zap.String("city", cityCode),
				zap.Error(err))
			continue
		}
		allData[cityCode] = commonConditionResp
	}

	c.updateData(allData)
	return nil
}

// 获取单个城市的地铁数据
func (c *CommonConditionCache) fetchCommonData(cityCode string) (*service.CommonConditionResponse, error) {
	baseReq := service.CommonConditionRequest{
		CityCode:    cityCode,
		VersionCode: "V7",
	}

	baseResp, err := c.dataFetcher.GetCommonCondition(baseReq)
	if err != nil {
		return nil, fmt.Errorf("get base data failed: %w", err)
	}

	return baseResp, nil
}

// 启动定期更新
func (c *CommonConditionCache) startPeriodicUpdate(interval time.Duration) {
	// 立即执行一次 Update
	if err := c.Update(); err != nil {
		log := logger.GetLogger()
		log.Error("initial update failed", zap.Error(err))
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		if err := c.Update(); err != nil {
			log := logger.GetLogger()
			log.Error("periodic update failed", zap.Error(err))
		}
	}
}

// IsExpired 检查缓存是否过期
func (c *CommonConditionCache) IsExpired() bool {
	data := c.data.Load().(*commonConditionCache)
	return time.Since(data.timestamp) > c.ttl
}

// updateData 更新缓存数据
func (c *CommonConditionCache) updateData(newData map[string]*service.CommonConditionResponse) {
	// 转换数据结构
	cache := make(map[string]map[string]map[string][]Pair)

	for city, commons := range newData {
		productParams := make(map[string]map[string][]Pair)

		for _, product := range commons.Data.Products {
			if product.IsOffline == 1 {
				continue
			}

			params := make(map[string][]Pair)
			for _, children := range product.Children {
				if children.IsOffline == 1 {
					continue
				}
				// 参数key
				key := children.Key
				pairs := make([]Pair, 0, 5)
				for _, childrenChildren := range children.Children {
					if childrenChildren.IsOffline == 1 {
						continue
					}
					pair := Pair{childrenChildren.Title, childrenChildren.Value}
					pairs = append(pairs, pair)
				}
				params[key] = pairs
			}

			productParams[strconv.Itoa(product.Value)] = params
		}

		cache[city] = productParams
	}

	// 创建新的缓存数据
	data := &commonConditionCache{
		common:    cache,
		timestamp: time.Now(),
	}

	// 原子替换
	c.data.Store(data)

	log := logger.GetLogger()
	log.Info("commonCondition cache updated",
		zap.Int("cities", len(cache)),
		zap.Time("timestamp", data.timestamp))
}
