package mysql

import (
	"database/sql"
	"fmt"
	"time"

	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"

	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
)

type userRepository struct {
	db  *sqlx.DB
	log *zap.Logger
}

// NewUserRepository creates a new MySQL user repository
func NewUserRepository(db *sqlx.DB) domain.UserRepository {
	return &userRepository{
		db:  db,
		log: logger.GetLogger().With(zap.String("component", "user_repository")),
	}
}

// userModel represents the user database model
type userModel struct {
	ID        int64     `db:"id"`
	Username  string    `db:"username"`
	Email     string    `db:"email"`
	CreatedAt time.Time `db:"created_at"`
	UpdatedAt time.Time `db:"updated_at"`
}

func (r *userRepository) GetByID(id int64) (*domain.User, error) {
	r.log.Debug("fetching user by id", zap.Int64("user_id", id))

	query := `SELECT id, username, email, created_at, updated_at FROM users WHERE id = ?`

	var user userModel
	err := r.db.Get(&user, query, id)
	if err == sql.ErrNoRows {
		r.log.Debug("user not found", zap.Int64("user_id", id))
		return nil, fmt.Errorf("user not found")
	}
	if err != nil {
		r.log.Error("error getting user",
			zap.Int64("user_id", id),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error getting user: %w", err)
	}

	r.log.Debug("user found", zap.Int64("user_id", id))
	return &domain.User{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
	}, nil
}

func (r *userRepository) Store(user *domain.User) error {
	r.log.Debug("storing new user", zap.String("username", user.Username))

	query := `
		INSERT INTO users (username, email, created_at, updated_at)
		VALUES (?, ?, ?, ?)
	`

	result, err := r.db.Exec(query,
		user.Username,
		user.Email,
		user.CreatedAt,
		user.UpdatedAt,
	)
	if err != nil {
		r.log.Error("error storing user",
			zap.String("username", user.Username),
			zap.Error(err),
		)
		return fmt.Errorf("error storing user: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		r.log.Error("error getting last insert id",
			zap.String("username", user.Username),
			zap.Error(err),
		)
		return fmt.Errorf("error getting last insert id: %w", err)
	}

	user.ID = id
	r.log.Info("user stored successfully",
		zap.Int64("user_id", id),
		zap.String("username", user.Username),
	)
	return nil
}

func (r *userRepository) Update(user *domain.User) error {
	r.log.Debug("updating user",
		zap.Int64("user_id", user.ID),
		zap.String("username", user.Username),
	)

	query := `
		UPDATE users
		SET username = ?,
			email = ?,
			updated_at = ?
		WHERE id = ?
	`

	result, err := r.db.Exec(query,
		user.Username,
		user.Email,
		time.Now(),
		user.ID,
	)
	if err != nil {
		r.log.Error("error updating user",
			zap.Int64("user_id", user.ID),
			zap.Error(err),
		)
		return fmt.Errorf("error updating user: %w", err)
	}

	rows, err := result.RowsAffected()
	if err != nil {
		r.log.Error("error getting rows affected",
			zap.Int64("user_id", user.ID),
			zap.Error(err),
		)
		return fmt.Errorf("error getting rows affected: %w", err)
	}

	if rows == 0 {
		r.log.Debug("user not found for update",
			zap.Int64("user_id", user.ID),
		)
		return fmt.Errorf("user not found")
	}

	r.log.Info("user updated successfully",
		zap.Int64("user_id", user.ID),
		zap.String("username", user.Username),
	)
	return nil
}

func (r *userRepository) Delete(id int64) error {
	r.log.Debug("deleting user", zap.Int64("user_id", id))

	query := `DELETE FROM users WHERE id = ?`

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.log.Error("error deleting user",
			zap.Int64("user_id", id),
			zap.Error(err),
		)
		return fmt.Errorf("error deleting user: %w", err)
	}

	rows, err := result.RowsAffected()
	if err != nil {
		r.log.Error("error getting rows affected",
			zap.Int64("user_id", id),
			zap.Error(err),
		)
		return fmt.Errorf("error getting rows affected: %w", err)
	}

	if rows == 0 {
		r.log.Debug("user not found for deletion",
			zap.Int64("user_id", id),
		)
		return fmt.Errorf("user not found")
	}

	r.log.Info("user deleted successfully",
		zap.Int64("user_id", id),
	)
	return nil
}
