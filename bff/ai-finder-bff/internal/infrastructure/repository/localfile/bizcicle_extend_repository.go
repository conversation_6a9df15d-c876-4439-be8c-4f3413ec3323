package repository

import (
	"ai-finder-bff/internal/domain/repository"
	"embed"
	"encoding/csv"
	"encoding/json"
	"io"
	"log"
	"strings"

	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

//go:embed resources/bizcircle.csv
var bizcircleCSV embed.FS

// BizcircleExtendRepository 商圈信息服务实现
type BizcircleExtendRepository struct {
	bizcircleMap map[string]*repository.BizcircleExtend
}

// NewBizcircleExtendRepository 创建商圈信息服务
func NewBizcircleExtendRepository() *BizcircleExtendRepository {

	bizcircleCSVData, err := bizcircleCSV.ReadFile("resources/bizcircle.csv")
	if err != nil {
		log.Fatal("Failed to load bizcircle CSV data", zap.Error(err))
	}

	bizcircleExtendRepository := &BizcircleExtendRepository{
		bizcircleMap: make(map[string]*repository.BizcircleExtend),
	}

	// 加载CSV数据
	bizcircleExtendRepository.loadCSVData(bizcircleCSVData)

	return bizcircleExtendRepository
}

// loadCSVData 加载CSV数据
func (s *BizcircleExtendRepository) loadCSVData(csvData []byte) {
	log := logger.GetLogger()

	reader := csv.NewReader(strings.NewReader(string(csvData)))
	// 跳过标题行
	_, err := reader.Read()
	if err != nil {
		log.Error("Failed to read CSV header", zap.Error(err))
		return
	}

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Error("Failed to read CSV record", zap.Error(err))
			continue
		}

		// CSV格式: 类型,位置,Completion result
		if len(record) < 3 {
			log.Error("Invalid CSV record format", zap.Strings("record", record))
			continue
		}

		// 只处理商圈类型
		if record[0] != "商圈" {
			continue
		}

		bizcircleName := record[1]
		jsonData := record[2]

		var info repository.BizcircleExtend
		err = json.Unmarshal([]byte(jsonData), &info)
		if err != nil {
			log.Error("Failed to parse JSON data",
				zap.String("bizcircle", bizcircleName),
				zap.Error(err))
			continue
		}

		s.bizcircleMap[bizcircleName] = &info
		log.Debug("Loaded bizcircle info",
			zap.String("name", bizcircleName),
			zap.String("highlight", info.Highlight))
	}

	log.Info("Bizcircle info loaded", zap.Int("count", len(s.bizcircleMap)))
}

// GetBizcircleExtend 获取商圈信息
func (s *BizcircleExtendRepository) GetBizcircleExtend(name string) (*repository.BizcircleExtend, error) {
	info, ok := s.bizcircleMap[name]
	if !ok {
		return nil, nil // 没有找到对应的商圈信息，返回nil
	}
	return info, nil
}
