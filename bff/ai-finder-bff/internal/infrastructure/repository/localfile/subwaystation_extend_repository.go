package repository

import (
	"ai-finder-bff/internal/domain/repository"
	"embed"
	"encoding/csv"
	"encoding/json"
	"io"
	"log"
	"strings"

	"ai-finder-bff/internal/pkg/logger"

	"go.uber.org/zap"
)

//go:embed resources/subwaystation.csv
var subwayStationExtendCVS embed.FS

// SubwayStationExtendRepository 商圈信息服务实现
type SubwayStationExtendRepository struct {
	subwayStationExtendMap map[string]*repository.SubwayStationExtend
}

// NewSubwayStationExtendRepository 创建商圈信息服务
func NewSubwayStationExtendRepository() *SubwayStationExtendRepository {

	subwayStationExtendCSVData, err := subwayStationExtendCVS.ReadFile("resources/subwaystation.csv")
	if err != nil {
		log.Fatal("Failed to load bizcircle CSV data", zap.Error(err))
	}

	subwayStationExtendRepository := &SubwayStationExtendRepository{
		subwayStationExtendMap: make(map[string]*repository.SubwayStationExtend),
	}

	// 加载CSV数据
	subwayStationExtendRepository.loadCSVData(subwayStationExtendCSVData)

	return subwayStationExtendRepository
}

// loadCSVData 加载CSV数据
func (s *SubwayStationExtendRepository) loadCSVData(csvData []byte) {
	log := logger.GetLogger()

	reader := csv.NewReader(strings.NewReader(string(csvData)))
	// 跳过标题行
	_, err := reader.Read()
	if err != nil {
		log.Error("Failed to read CSV header", zap.Error(err))
		return
	}

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Error("Failed to read CSV record", zap.Error(err))
			continue
		}

		// CSV格式: 类型,位置,Completion result
		if len(record) < 3 {
			log.Error("Invalid CSV record format", zap.Strings("record", record))
			continue
		}

		// 只处理商圈类型
		if record[0] != "地铁站" {
			continue
		}

		subwayStationName := record[1]
		jsonData := record[2]

		var info repository.SubwayStationExtend
		err = json.Unmarshal([]byte(jsonData), &info)
		if err != nil {
			log.Error("Failed to parse JSON data",
				zap.String("subwayStationName", subwayStationName),
				zap.Error(err))
			continue
		}

		s.subwayStationExtendMap[subwayStationName] = &info
		log.Debug("Loaded subwayStation info",
			zap.String("name", subwayStationName),
			zap.String("highlight", info.Highlight))
	}

	log.Info("SubwayStation info loaded", zap.Int("count", len(s.subwayStationExtendMap)))
}

// GetSubwayStationExtend 获取商圈信息
func (s *SubwayStationExtendRepository) GetSubwayStationExtend(name string) (*repository.SubwayStationExtend, error) {
	info, ok := s.subwayStationExtendMap[name]
	if !ok {
		return nil, nil // 没有找到对应的信息，返回nil
	}
	return info, nil
}
