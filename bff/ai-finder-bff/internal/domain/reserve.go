package domain

import "ai-finder-bff/internal/application/service"

// ReserveRequest represents the request structure for creating a reservation
type ReserveRequest struct {
	Token      string      `json:"token"`              // 用户TOKEN
	CityCode   string      `json:"cityCode,omitempty"` // 城市编码
	HouseList  []HouseInfo `json:"houseList"`          // house清单
	ExpectTime string      `json:"expectTime"`         // 期望时间， e.g. 2025-05-27 12:00:00
}

// HouseInfo represents house information in a trip
type HouseInfo struct {
	ResblockId *int64 `json:"resblockId,omitempty"` // 楼盘ID
	HouseId    *int64 `json:"houseId,omitempty"`    // 房源ID
}

// ReserveResponse represents the response structure for reservation
type ReserveResponse struct {
	Code         int                                `json:"code"`
	Message      string                             `json:"message"`
	Toast        string                             `json:"toast,omitempty"`        // 预约提示
	MainOrderNum string                             `json:"mainOrderNum,omitempty"` // 看房单号
	KeeperInfo   *service.WatchingDtlListKeeperResp `json:"keeperInfo"`             // 房源信息
	HouseInfo    *service.WatchingDtlListHouseResp  `json:"houseInfo"`              // 管家信息
}
