package domain

import "encoding/json"

// 房间领域信息(注意，这个结构会直接返回给前端，是在 ymer 的 RoomListV3Response 基础上进行剪裁，扩展得到)
type Room struct {
	TemplateType  int    `json:"template_type"`
	Area          string `json:"area,omitempty"`           // 面积
	Bedroom       int    `json:"bedroom,omitempty"`        // 居室数量
	BizcircleName string `json:"bizcircle_name,omitempty"` // 商圈
	CanSignDate   int    `json:"can_sign_date,omitempty"`  // 可签约时间
	CanSignLong   int    `json:"can_sign_long,omitempty"`  // 可签约时长
	CanSignTime   int    `json:"can_sign_time,omitempty"`  // 可能无用
	Code          string `json:"code,omitempty"`           // 房间编码
	DistrictName  string `json:"district_name,omitempty"`  // 城区
	Face          string `json:"face,omitempty"`           // 朝向
	Floor         string `json:"floor,omitempty"`          // 房屋所在楼层
	FloorTotal    string `json:"floor_total,omitempty"`    //总楼层
	//HasVideo        int     `json:"has_video,omitempty"`      //
	//HasImmersivePic int     `json:"has_immersive_pic,omitempty"`
	HouseCode   string     `json:"house_code,omitempty"`   // 房屋编号
	HouseId     string     `json:"house_id,omitempty"`     // 房屋ID
	HouseType   int        `json:"house_type,omitempty"`   // 房屋类型
	Id          string     `json:"id,omitempty"`           // 房间ID
	InvId       string     `json:"inv_id,omitempty"`       // 库存id
	InvNo       string     `json:"inv_no,omitempty"`       //库存编码
	Lat         float64    `json:"lat,omitempty"`          // 纬度
	Lng         float64    `json:"lng,omitempty"`          //经度
	BuildingLat float64    `json:"building_lat,omitempty"` // 楼栋纬度
	BuildingLng float64    `json:"building_lng,omitempty"` //楼栋经度
	Name        string     `json:"name,omitempty"`         // 房间名称
	NameV2      string     `json:"name_v2,omitempty"`      //房间名称（带整租合租）
	Parlor      int        `json:"parlor,omitempty"`       //厅的数量
	PhotoIcons  []struct { // ICONS
		Url  string `json:"url"`
		Type int    `json:"type"`
	} `json:"photo_icons,omitempty"`
	Photo             string          `json:"photo,omitempty"`     // 照片
	PhotoMin          string          `json:"photo_min,omitempty"` // 照片
	PhotoMinWebp      string          `json:"photo_min_webp,omitempty"`
	PhotoWebp         string          `json:"photo_webp,omitempty"`
	Price             int             `json:"price,omitempty"`         // 价格
	PriceUnit         string          `json:"price_unit,omitempty"`    //价格单位
	ResblockId        json.RawMessage `json:"resblock_id,omitempty"`   //小区id
	ResblockName      string          `json:"resblock_name,omitempty"` //小区名
	SaleStatus        int             `json:"sale_status,omitempty"`   //房源销售状态1：可立即签约、2：可预订、3：转租、4：待释放
	ClientSaleStatus  int             `json:"client_sale_status,omitempty"`
	Source            string          `json:"source,omitempty"`              // 未知
	StockStatus       string          `json:"stock_status,omitempty"`        // 未知
	SubwayStationInfo string          `json:"subway_station_info,omitempty"` // 最近地铁信息
	TagsRow1          []struct {      // 标签
		BgImg      string  `json:"bg_img"`
		BgImgRatio float64 `json:"bg_img_ratio"`
		TailTag    struct {
			Style struct {
				Background string `json:"background"`
				Color      string `json:"color"`
			} `json:"style"`
			Title string `json:"title"`
		} `json:"tail_tag"`
	} `json:"tags_row1,omitempty"`
	Tags []struct { // 标签
		Title string `json:"title"`
	} `json:"tags,omitempty"`
	TagsRow2 []struct { // 标签
		Icon  string `json:"icon"`
		Style struct {
			Background string `json:"background"`
			Color      string `json:"color"`
		} `json:"style"`
		Title string `json:"title"`
	} `json:"tags_row2,omitempty"`
	DistanceInfo    string   `json:"distance_info,omitempty"`     // 具体查询经纬度距离
	Type            int      `json:"type,omitempty"`              // 租房类型， 整租&合租。。
	TypeText        string   `json:"type_text,omitempty"`         // 租房类型， 整租&合租。。
	StyleTag        string   `json:"style_tag,omitempty"`         // 朝向
	ZiroomVersionId int      `json:"ziroom_version_id,omitempty"` // 装修版本
	VersionName     string   `json:"version_name,omitempty"`      // 装修版本
	CityCode        int      `json:"city_code,omitempty"`         // 城市编码
	RoomNo          string   `json:"room_no,omitempty"`           // 房间编号， 01， 02，03卧室
	RoomIntro       string   `json:"room_intro,omitempty"`        // 房间基本信息
	RoomIntros      []string `json:"room_intros,omitempty"`       // 房间信息 8.13㎡ | 3/7层 | 朝北
	Features        []string `json:"features,omitempty"`          //房间特性，比如理想家
	Addrs           string   `json:"addrs,omitempty"`             // 地址
}

type RoomListResponse struct {
	//
	Summary string `json:"summary"`
	// 房源
	Rooms []Room `json:"rooms"`
	// 推荐房源
	PromotionRooms []Room `json:"promotionRooms"`
}

// 房间过滤条件
type RoomFilter struct {
	CityCode string `json:"city_code,omitempty"`
	Type     int    `json:"type,omitempty"` // 租房类型， 1合租；2整租；3老整租；4整租3.0；5NC整租 8:豪宅，6：自如寓
	// -- 位置信息
	SugType           int    `json:"sug_type,omitempty"`
	LocationType      string `json:"location_type,omitempty"`
	LocationValue     string `json:"location_value,omitempty"`
	SubwayStationCode string `json:"subway_station_code,omitempty"` //地铁站
	SubwayCode        string `json:"subway_code,omitempty"`         // 地铁线
	DistrictCode      string `json:"district_code,omitempty"`       // 城区
	BizcircleCode     string `json:"bizcircle_code,omitempty"`      // 商圈
	BizcircleCode2    string `json:"bizcircle_code2,omitempty"`     //曼舍商圈
	ResblockId        string `json:"resblock_id,omitempty"`         // 楼盘
	Clng              string `json:"clng,omitempty"`                // 经度
	Clat              string `json:"clat,omitempty"`                // 纬度
	Distance          *int   `json:"distance,omitempty"`            // 距离 （配合经纬度使用）
	// -- 位置信息结束

	// 通勤信息
	Minute    string `json:"minute,omitempty"`    //通勤分钟数
	Transport string `json:"transport,omitempty"` //通勤方式

	// 通勤信息结束

	Bedroom                 string `json:"bedroom,omitempty"`                  //居室
	Leasetype               string `json:"leasetype,omitempty"`                //租约类型 1:可短租， 2:长租
	IsFirstRent             string `json:"is_first_rent,omitempty"`            // 是否首次出租
	Price                   string `json:"price,omitempty"`                    //价格区间
	Hface                   string `json:"hface,omitempty"`                    // 房间朝向
	Area                    string `json:"area,omitempty"`                     // 面积区间
	CheckinDate             string `json:"checkin_date"`                       //预计可入住日期，格式类似（2017-09-28）
	CheckinPeopleNum        string `json:"checkin_people_num,omitempty"`       // 入住人数
	Layout                  string `json:"layout,omitempty"`                   // 特色户型（合租) 1: 独立卫生间 2. 独立阳台
	Feature                 string `json:"feature,omitempty"`                  // 房源特色 高层视野：71 带起居室:61 全屋智能：63 南北通透：72 loft：44
	Version                 string `json:"version,omitempty"`                  // 产品风格 友家7.0：7  友家6.0：6   心舍3.0：34  心舍2.0：21   心舍1.0：17
	FeatureHouse            string `json:"feature_house,omitempty"`            // 小区特色 新小区:8
	Heating                 string `json:"heating,omitempty"`                  // 供暖方式 集体供暖:3  独立供暖:2  中央供暖:1
	Roommate                string `json:"roommate,omitempty"`                 // 合租室友 全男：2  全女：1
	Tag                     string `json:"tag,omitempty"`                      // 标签 可预定:9 有电梯:13
	LeaseTypeDurationLong   string `json:"leasetypeduration_long,omitempty"`   // 租约二级菜单 长租1年：62  长租2年：63  长租3年：64
	LeaseTypeDurationMonth  string `json:"leasetypeduration_month,omitempty"`  // 租约二级菜单 月租 1个月：67  2个月：68  3个月：69
	LeaseTypeDurationSeason string `json:"leasetypeduration_season,omitempty"` // 租约二级菜单 季租 4个月： 70  5个月：71  6个月：72
	Page                    *int   `json:"page,omitempty"`
	Size                    *int   `json:"size,omitempty"`
	Sort                    string `json:"sort,omitempty"` // 排序规则

}
