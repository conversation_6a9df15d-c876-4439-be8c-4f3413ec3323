package domain

// 聚合查询请求
type AggregateRequest struct {
	CityCode      string `json:"city_code"`
	BizcircleCode string `json:"bizcircle_code"`
	ApartmentType int    `json:"apartment_type"`
	RentUnitTypes []int  `json:"rent_unit_types"`
	SortField     string `json:"sort_field"`
	SortOrder     string `json:"sort_order"`
	Start         int    `json:"start"`
	Size          int    `json:"size"`
}

// 聚合查询响应
type AggregateResponse struct {
	RentUnitTypeStats map[string]RentUnitTypeStat `json:"rent_unit_type_stats"`
}

type RentUnitTypeStat struct {
	DocCount int     `json:"doc_count"`
	MinPrice float64 `json:"min_price"`
}
