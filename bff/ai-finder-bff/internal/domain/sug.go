package domain

// 搜索建议请求
type SearchSugRequest struct {
	CityCode    string `json:"city_code"`
	FromScene   string `json:"from_scene"`
	Keyword     string `json:"keyword"`
	SugTypeName string `json:"sug_type_name"`
}

// 搜索建议响应
type SearchSugResponse struct {
	Data SearchSugData `json:"data"`
}

type SearchSugData struct {
	Title   string          `json:"title"`
	Keyword string          `json:"keyword"`
	SugType int             `json:"sug_type"`
	Total   int             `json:"total"`
	Items   []SearchSugItem `json:"items"`
}

type SearchSugItem struct {
	Title       string               `json:"title"`
	SubTitle    string               `json:"sub_title"`
	Labels      []SearchSugItemLabel `json:"labels"`
	SkipText    string               `json:"skip_text"`
	HouseNum    string               `json:"house_num"`
	MinPrice    string               `json:"min_price"`
	Memo        string               `json:"memo"`
	Icon        string               `json:"icon"`
	ID          int                  `json:"id"`
	Type        int                  `json:"type"`
	TypeName    string               `json:"type_name"`
	Name        string               `json:"name"`
	Value       string               `json:"value"`
	Location    string               `json:"location"`
	CityCode    string               `json:"city_code"`
	CityName    string               `json:"city_name"`
	KeywdType   int                  `json:"keywd_type"`
	IsRecommend int                  `json:"is_recommend"`
}

type SearchSugItemLabel struct {
	Text       string `json:"text"`
	Color      string `json:"color"`
	Background string `json:"background"`
}
