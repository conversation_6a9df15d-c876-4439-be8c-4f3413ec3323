package domain

// 楼盘请求
type ResblockRequest struct {
	CityCode      string `json:"city_code"`
	BizcircleCode string `json:"bizcircle_code"`
	Type          *int   `json:"type,omitempty"` // 类型

	// 位置
	SugType       int    `json:"sug_type"`
	LocationType  string `json:"location_type,omitempty"`
	LocationValue string `json:"location_value,omitempty"`
	Distance      *int   `json:"distance,omitempty"` // 距离 （配合经纬度使用）

	// 通勤
	Minute    string `json:"minute,omitempty"`    //通勤分钟数
	Transport string `json:"transport,omitempty"` //通勤方式

	// 其他参数
	Price   string `json:"price,omitempty"`   //价格
	Bedroom string `json:"bedroom,omitempty"` // 居室
	Hface   string `json:"hface,omitempty"`   // 房间朝向
	Area    string `json:"area,omitempty"`    // 面积区间

	TestInfo   string `json:"test_info,omitempty"`   // AB test
	AppVersion string `json:"app_version,omitempty"` // APP api 版本
}

// 商圈查询响应
type ResblockResponse struct {
	Data         []ResblockData `json:"data"`
	ErrorCode    int            `json:"error_code"`
	ErrorMessage string         `json:"error_message"`
	Status       string         `json:"status"`
}

// 商圈信息
type ResblockData struct {
	ResblockId        string         `json:"resblock_id"`
	ResblockName      string         `json:"resblock_name"`
	MinPrice          string         `json:"min_price"`
	PicInfo           string         `json:"pic_info"`
	MaxPrice          string         `json:"max_price"`
	HouseCount        string         `json:"house_count"`
	Lng               float64        `json:"lng"`
	Lat               float64        `json:"lat"`
	Tags              []Tag          `json:"tags,omitempty"`
	Surround          []SurroundInfo `json:"surround,omitempty"`
	Infos             []string       `json:"infos,omitempty"`
	SubwayStationInfo string         `json:"subway_station_info,omitempty"`
	TemplateType      int            `json:"template_type,omitempty"`
	Pic               string         `json:"pic,omitempty"`
	Title             string         `json:"title,omitempty"`
	Price             int            `json:"price,omitempty"`
	PriceUnit         string         `json:"price_unit,omitempty"`
	Router            Router         `json:"router"`
}

type Router struct {
	Target    string                 `json:"target"`
	Parameter map[string]interface{} `json:"parameter"`
}

type SurroundInfo struct {
	Title string `json:"title"`
	Count int    `json:"count"`
}

type Tag struct {
	Title string `json:"title"`
}
