package domain

// 地铁线路
type SubwayLine struct {
	Code     string          `json:"code"`
	Name     string          `json:"name"`
	Stations []SubwayStation `json:"stations"`
}

// 地铁站
type SubwayStation struct {
	Code string  `json:"code"`
	Name string  `json:"name"`
	Lat  float64 `json:"lat"`
	Lng  float64 `json:"lng"`
}

// 地铁请求
type SubwayRequest struct {
	CityCode string `json:"city_code"`
}

// 地铁响应
type SubwayResponse struct {
	Data []SubwayLine `json:"data"`
}

// 楼盘请求
type SubwayStationRequest struct {
	CityCode        string `json:"city_code"`
	SubwayLine      string `json:"subway_line"`
	ProductCategory string `json:"product_category"`
	Price           string `json:"sort_price"`
	Bedroom         string `json:"house_bedroom"`
	Face            string `json:"face"`
}

// 商圈查询响应
type SubwayStationResponse struct {
	Data         []SubwayStationData `json:"data"`
	ErrorCode    int                 `json:"error_code"`
	ErrorMessage string              `json:"error_message"`
	Status       string              `json:"status"`
}

// 商圈信息
type SubwayStationData struct {
	SubwayStationCode string  `json:"subway_station_code"`
	SubwayStationName string  `json:"subway_station_name"`
	Lat               float64 `json:"lat"`
	Lng               float64 `json:"lng"`
	MinPrice          string  `json:"min_price"`
	MaxPrice          string  `json:"max_price"`
	HouseCount        string  `json:"house_count"`
	Highlight         string  `json:"highlight"`
	Summary           string  `json:"summary"`
}
