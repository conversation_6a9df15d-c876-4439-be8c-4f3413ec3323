package domain

// 商圈查询请求
type BizcircleRequest struct {
	CityCode     string `json:"city_code"`
	DistrictCode string `json:"district_code"`
	Price        string `json:"price"`
	Bedroom      string `json:"bedroom"`
	HFace        string `json:"hface"`
	Type         string `json:"type,omitempty"` // 类型
}

// 商圈查询响应
type BizcircleResponse struct {
	Data         []BizcircleData `json:"data"`
	ErrorCode    int             `json:"error_code"`
	ErrorMessage string          `json:"error_message"`
	Status       string          `json:"status"`
}

// 商圈信息
type BizcircleData struct {
	BizcircleCode string `json:"bizcircle_code"`
	BizcircleName string `json:"bizcircle_name"`
	Desc          string `json:"desc"`
	Description   string `json:"description"`
	Lng           string `json:"lng"`
	Lat           string `json:"lat"`
	// MinPrice      string `json:"min_price"`
	// MaxPrice      string `json:"max_price"`
	HouseCount string `json:"house_count"`
	Highlight  string `json:"highlight"`
	Summary    string `json:"summary"`

	Products []ProductData `json:"products"`
}

type ProductData struct {
	ProductCategory string `json:"product_category"`
	Count           string `json:"count"`
	MinPrice        string `json:"min_price"`
	MaxPrice        string `json:"max_price"`
}
