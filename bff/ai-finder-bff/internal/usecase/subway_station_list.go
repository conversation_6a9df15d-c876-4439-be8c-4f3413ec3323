package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/infrastructure/cache"
	"fmt"
	"strconv"
	"time"
)

type SubwayStationUseCase interface {
	GetSubwayStations(request domain.SubwayStationRequest) (*domain.SubwayStationResponse, error)
}

type subwayStationUseCase struct {
	erebusService              service.ErebusService
	subwayCache                *cache.SubwayCache
	subwayStationExtendService service.SubwayStationExtendService
}

func NewSubwayStationUseCase(erebusService service.ErebusService, subwayStationExtendService service.SubwayStationExtendService) SubwayStationUseCase {
	return &subwayStationUseCase{
		erebusService:              erebusService,
		subwayCache:                cache.NewSubwayCache(3*time.Hour, erebusService), // 3小时缓存
		subwayStationExtendService: subwayStationExtendService,
	}
}

func (uc *subwayStationUseCase) GetSubwayStations(request domain.SubwayStationRequest) (*domain.SubwayStationResponse, error) {
	// 直接从erebus 获取 统计数据

	terms := map[string]interface{}{
		"apartment_type": 1,
		"city_code":      request.CityCode,
		"subway_line":    request.SubwayLine,
	}
	if request.ProductCategory != "" {
		atoi, err := strconv.Atoi(request.ProductCategory)
		if err == nil {
			terms["product_category"] = []int{atoi}
		} else {
			terms["product_category"] = []int{1, 2}
		}
	} else {
		terms["product_category"] = []int{1, 2}
	}

	erebusRequest := service.AggregateRequest{
		Filters: service.Filters{
			Terms: terms,
		},
		Groups: map[string]service.GroupInfo{
			"subway_station": service.GroupInfo{
				Field: map[string]string{"subway_station": "terms"},
				Size:  3,
				Groups: map[string]service.GroupFieldConfig{
					"min_price": service.GroupFieldConfig{
						Field: map[string]string{
							"sort_price": "min",
						},
					},
					"max_price": service.GroupFieldConfig{
						Field: map[string]string{
							"sort_price": "max",
						},
					},
				},
			},
		},
	}

	/*
		{"filters":{"terms":{"apartment_type":1,"bizcircleCode":"","city_code":"110000","rent_unit_type":[2,3]}},"groups":{"resblock_id":{"field":{"resblock_id":"terms"},"size":3,"groups":{"max_price":{"field":{"sort_price":"max"}},"min_price":{"field":{"sort_price":"min"}}}}}}
	*/
	aggregate, err := uc.erebusService.Aggregate(erebusRequest)
	if err != nil {
		return nil, fmt.Errorf("call resb service failed: %w", err)
	}

	// 转换响应实体
	return &domain.SubwayStationResponse{
		ErrorCode: 200,
		Status:    "success",
		Data:      uc.convertToSubwayStationData(aggregate, request.CityCode),
	}, nil

}

func (uc *subwayStationUseCase) convertToSubwayStationData(aggregate *service.AggregateResponse, cityCode string) []domain.SubwayStationData {

	aggs := aggregate.Data.Aggs["subway_station"]

	ret := make([]domain.SubwayStationData, 0, 5)
	for _, v := range aggs {
		subwayStationCode := fmt.Sprintf("%v", v["key"])

		subwayStation := domain.SubwayStationData{
			SubwayStationCode: subwayStationCode,
			SubwayStationName: fmt.Sprintf("%v", v["key"]),
			HouseCount:        fmt.Sprintf("%v", v["docCount"]),
			MaxPrice:          fmt.Sprintf("%v", v["max_price"]),
			MinPrice:          fmt.Sprintf("%v", v["min_price"]),
		}

		station, line, b := uc.subwayCache.GetStation(cityCode, subwayStationCode)
		if b {
			subwayStation.SubwayStationName = station.Name
			subwayStation.Lat = station.Lat
			subwayStation.Lng = station.Lng

			extend := uc.subwayStationExtendService.GetSubwayStationExtend(line + " - " + station.Name + "站")
			if extend != nil {
				subwayStation.Summary = extend.Summary
				subwayStation.Highlight = extend.Highlight
			}

		}

		ret = append(ret, subwayStation)
	}
	return ret
}
