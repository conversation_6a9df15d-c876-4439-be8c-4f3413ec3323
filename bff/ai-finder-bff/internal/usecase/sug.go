package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"fmt"
	"github.com/go-ego/gse"
	"go.uber.org/zap"
)

// 搜索建议用例
type SearchSugUseCase interface {
	// 获取搜索建议
	GetSearchSug(request domain.SearchSugRequest) (*domain.SearchSugResponse, error)
}

type searchSugUseCase struct {
	ymerService service.YmerService
	seg         *gse.Segmenter
}

func NewSearchSugUseCase(ymerService service.YmerService) SearchSugUseCase {
	var seg gse.Segmenter
	seg.LoadDict("/app/conf/dict.txt") // 自动加载默认词典
	//seg.LoadDict("dict/dict.txt") // 自动加载默认词典

	text := "中航广场大慧寺6号院"
	words := seg.Cut(text, true) //
	fmt.Println("分词结果:", words)

	return &searchSugUseCase{ymerService: ymerService, seg: &seg}
}

func (uc *searchSugUseCase) GetSearchSug(request domain.SearchSugRequest) (*domain.SearchSugResponse, error) {
	// 转换为 ymer 请求
	ymerRequest := service.SearchSugRequest{
		CityCode:  request.CityCode,
		FromScene: request.FromScene,
		Keyword:   request.Keyword,
	}

	// 调用 ymer 服务
	resp, err := uc.ymerService.GetSearchSug(ymerRequest)
	if err != nil {
		return nil, fmt.Errorf("call ymer service failed: %w", err)
	}

	ret := convertAndFilterToSearchSugResponse(resp, request)

	// 返回数据为空时，需要分词
	if len(ret.Data.Items) == 0 {

		cut := uc.seg.Cut(request.Keyword, true)
		if len(cut) > 1 {
			// 取最后一个
			ymerRequest.Keyword = cut[len(cut)-1]
			resp, err = uc.ymerService.GetSearchSug(ymerRequest)
			if err != nil {
				return nil, fmt.Errorf("call ymer service failed: %w", err)
			}
			ret = convertAndFilterToSearchSugResponse(resp, request)
		}
	}

	// 转换响应为领域模型
	return ret, nil
}

func filterSug(request domain.SearchSugRequest, items []domain.SearchSugItem) []domain.SearchSugItem {
	log := logger.GetLogger()
	log.Info("sug filter", zap.Any("params", request))

	if request.SugTypeName == "" {
		return items
	}
	sugType := ShortName2SugTypeMap[request.SugTypeName]
	if sugType == 0 {
		return items
	}

	// 找到sugType, 需要进行过滤
	itemsNew := make([]domain.SearchSugItem, 0, 5)
	for _, item := range items {
		if item.Type == sugType {
			itemsNew = append(itemsNew, item)
		}
	}
	return itemsNew
}

func convertAndFilterToSearchSugResponse(resp *service.SearchSugResponse, request domain.SearchSugRequest) *domain.SearchSugResponse {

	items := make([]domain.SearchSugItem, 0, 5)
	for _, item := range resp.Data.Items {
		itemNew := domain.SearchSugItem{
			Title:       item.Title,
			SubTitle:    item.SubTitle,
			Labels:      convertLabels(item.Labels),
			SkipText:    item.SkipText,
			HouseNum:    item.HouseNum,
			MinPrice:    item.MinPrice,
			Memo:        item.Memo,
			Icon:        item.Icon,
			ID:          item.ID,
			Type:        item.Type,
			Name:        item.Name,
			Value:       item.Value,
			Location:    item.Location,
			CityCode:    item.CityCode,
			CityName:    item.CityName,
			KeywdType:   item.KeywdType,
			IsRecommend: item.IsRecommend,
		}
		itemNew.TypeName = SugType2ShortNameMap[itemNew.Type]
		items = append(items, itemNew)
	}

	log := logger.GetLogger()
	log.Info("sug filter", zap.Any("items", items), zap.Any("resp", resp))
	if len(items) == 0 && len(resp.Data.ErrorRecoveryItems) > 0 {
		for _, item := range resp.Data.ErrorRecoveryItems {
			itemNew := domain.SearchSugItem{
				Title:       item.Title,
				SubTitle:    item.SubTitle,
				Labels:      convertLabels(item.Labels),
				SkipText:    item.SkipText,
				HouseNum:    item.HouseNum,
				MinPrice:    item.MinPrice,
				Memo:        item.Memo,
				Icon:        item.Icon,
				ID:          item.ID,
				Type:        item.Type,
				Name:        item.Name,
				Value:       item.Value,
				Location:    item.Location,
				CityCode:    item.CityCode,
				CityName:    item.CityName,
				KeywdType:   item.KeywdType,
				IsRecommend: item.IsRecommend,
			}
			itemNew.TypeName = SugType2ShortNameMap[itemNew.Type]
			items = append(items, itemNew)
		}
	}

	items = filterSug(request, items)

	return &domain.SearchSugResponse{
		Data: domain.SearchSugData{
			Title:   resp.Data.Title,
			Keyword: request.Keyword,
			SugType: resp.Data.SugType,
			Total:   resp.Data.Total,
			Items:   items,
		},
	}
}

func convertLabels(labels []service.SearchSugLabel) []domain.SearchSugItemLabel {
	if labels != nil {
		ret := make([]domain.SearchSugItemLabel, 0, 5)

		for _, v := range labels {
			l := domain.SearchSugItemLabel{
				Text:       v.Text,
				Color:      v.Color,
				Background: v.Background,
			}
			ret = append(ret, l)
		}
	}
	return nil
}

var ShortName2SugTypeMap = make(map[string]int)

func init() {
	for k, v := range SugType2ShortNameMap {
		ShortName2SugTypeMap[v] = k
	}
}

var SugType2ShortNameMap = map[int]string{
	SugTypeBizcircle:      "商圈",
	SugTypeSubwayLine:     "地铁线",
	SugTypeSubwayStation:  "地铁",
	SugTypeResblock:       "小区",
	SugTypeDistrict:       "行政区",
	SugTypeCompany:        "公司",
	SugTypeOfficeBuilding: "写字楼",
	SugTypeTechPark:       "园区",
	SugTypeSchool:         "学校",
	SugTypeOrganization:   "机构",
	SugTypeHospital:       "医院",
	SugTypeShopping:       "商场",
	SugTypeTourist:        "景点",
	SugTypeTraffic:        "地点",
	SugTypeRoad:           "道路",
	SugTypeTags:           "标签",
	SugTypeMarketingWord:  "活动",
	SugTypeZRA:            "自如寓",
	SugTypeJumpH5:         "链接",
	SugTypeJumpAPP:        "链接",
	SugTypeSwitchCity:     "异地",
}

const (
	_ = iota
	SugTypeBizcircle
	SugTypeSubwayLine
	SugTypeSubwayStation
	SugTypeResblock
	SugTypeDistrict
	SugTypeCompany
	SugTypeOfficeBuilding
	SugTypeTechPark
	SugTypeSchool
	SugTypeOrganization
	SugTypeHospital
	SugTypeShopping
	SugTypeTourist
	SugTypeTraffic
	SugTypeRoad
	SugTypeTags
	SugTypeMarketingWord
	SugTypeZRA
	SugTypeJumpH5
	SugTypeJumpAPP
	SugTypeSwitchCity
	SugTypePoiOrigin = 30 //sug指定POI原生定位
)
