package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// 商圈查询用例
type BizcircleUseCase interface {
	GetBizcircle(request domain.BizcircleRequest) (*domain.BizcircleResponse, error)
}

type bizcircleUseCase struct {
	erebusClient           service.ErebusService
	marsClient             service.MarsService
	bizcircleExtendService service.BizcircleExtendService
}

func NewBizcircleUseCase(
	erebusService service.ErebusService,
	marsService service.MarsService,
	bizcircleExtendService service.BizcircleExtendService,
) BizcircleUseCase {
	return &bizcircleUseCase{
		erebusClient:           erebusService,
		marsClient:             marsService,
		bizcircleExtendService: bizcircleExtendService,
	}
}

func (uc *bizcircleUseCase) GetBizcircle(request domain.BizcircleRequest) (*domain.BizcircleResponse, error) {
	// 转换为 ymer 请求
	/*
		ymerRequest := service.BizcircleRequest{
			CityCode: request.CityCode,
		}

		// 调用 ymer 服务
		resp, err := uc.ymerService.GetBizcircle(ymerRequest)
		if err != nil {
			return nil, fmt.Errorf("call ymer service failed: %w", err)
		}
	*/
	// 直接从erebus 获取 统计数据
	terms := map[string]interface{}{
		"apartment_type": 1,
	}
	if request.HFace != "" {
		terms["face"] = request.HFace
	}
	if request.Bedroom != "" {
		terms["house_bedroom"] = request.Bedroom
	}
	if request.DistrictCode != "" {
		terms["district_code"] = request.DistrictCode
	} else {
		terms["city_code"] = request.CityCode
	}
	// 整租类型，租房类型， 1合租；2整租；3老整租；4整租3.0；5NC整租 8:豪宅，6：自如寓
	if request.Type != "" && request.Type != "null" {
		if request.Type == "1" {
			terms["product_category"] = 1
		} else if request.Type == "2" || request.Type == "3" || request.Type == "4" {
			terms["product_category"] = []int{2, 3, 4}
		} else {
			// 默认合租
			// terms["product_category"] = 1
		}
	} else {
		// 默认合租
		// terms["product_category"] = 1
	}

	ranges := make(map[string][]*int)

	if request.Price != "" {
		split := strings.Split(request.Price, ",")
		if len(split) >= 2 {
			min, err := strconv.Atoi(split[0])
			if err != nil {
				min = 0
			}
			max, err := strconv.Atoi(split[1])
			if err != nil {
				max = 0
			}

			if max > 0 {
				val := []*int{&min, &max}
				ranges["sort_price"] = val
			}

		}
	}
	filters := service.Filters{
		Terms: terms,
	}
	if len(ranges) > 0 {
		filters.Range = ranges
	}

	//
	erebusRequest := service.AggregateRequest{
		Filters: filters,
		Groups: map[string]service.GroupInfo{
			"bizcircle_code": {
				Field: map[string]string{"bizcircle_code": "terms"},
				Size:  3,
				Groups: map[string]service.GroupFieldConfig{
					"product_category_count": {
						Field: map[string]string{
							"product_category": "terms",
						},
						Size: 10,
						Groups: map[string]service.GroupFieldConfig{
							"min_price": {
								Field: map[string]string{
									"sort_price": "min",
								},
							},
							"max_price": {
								Field: map[string]string{
									"sort_price": "max",
								},
							},
						},
					},
				},
			},
		},
	}

	/*{"filters":{"terms":{"apartment_type":1,"city_code":"110000","rent_unit_type":[2,3]}},"groups":{"bizcircle_code":{"field":{"bizcircle_code":"terms"},"size":3,"groups":{"max_price":{"field":{"sort_price":"max"}},"min_price":{"field":{"sort_price":"min"}}}}}}
	 */
	aggregate, err := uc.erebusClient.Aggregate(erebusRequest)
	if err != nil {
		return nil, fmt.Errorf("call resb service failed: %w", err)
	}

	logger.GetLogger().Info("aggregate", zap.Any("aggregate", aggregate))

	// 转换响应实体
	return &domain.BizcircleResponse{
		ErrorCode: 200,
		Status:    "success",
		Data:      convertToBizcircleData(aggregate, uc.marsClient, uc.bizcircleExtendService),
	}, nil

}

func convertToBizcircleData(
	aggregate *service.AggregateResponse,
	marsService service.MarsService,
	bizcircleExtendService service.BizcircleExtendService,
) []domain.BizcircleData {

	aggs := aggregate.Data.Aggs["bizcircle_code"]

	ret := make([]domain.BizcircleData, 0, 5)
	for _, v := range aggs {
		bizcircleCode := fmt.Sprintf("%v", v["key"])

		bizcircle := domain.BizcircleData{
			BizcircleCode: fmt.Sprintf("%v", bizcircleCode),
			HouseCount:    fmt.Sprintf("%v", v["docCount"]),
			// MaxPrice:      fmt.Sprintf("%v", v["max_price"]),
			// MinPrice:      fmt.Sprintf("%v", v["min_price"]),
		}

		productCategory := v["product_category_count"].([]interface{})
		for _, v2 := range productCategory {
			v2Map := v2.(map[string]interface{})
			bizcircle.Products = append(bizcircle.Products, domain.ProductData{
				ProductCategory: fmt.Sprintf("%v", v2Map["key"]),
				Count:           fmt.Sprintf("%v", v2Map["docCount"]),
				MinPrice:        fmt.Sprintf("%v", v2Map["min_price"]),
				MaxPrice:        fmt.Sprintf("%v", v2Map["max_price"]),
			})
		}

		detail, _ := marsService.GetBizcircleDetail(service.BizcircleDetailRequest{BizcircleId: bizcircleCode})
		if detail != nil {
			bizcircle.BizcircleName = detail.Data.Name
			bizcircle.Desc = detail.Data.Desc
			bizcircle.Description = detail.Data.Description
			bizcircle.Lat = detail.Data.Lat
			bizcircle.Lng = detail.Data.Lng

			// 获取商圈亮点和摘要
			if info := bizcircleExtendService.GetBizcircleExtend(detail.Data.Name); info != nil {
				bizcircle.Highlight = info.Highlight
				bizcircle.Summary = info.Summary
			}
		}

		ret = append(ret, bizcircle)
	}
	return ret
}

//func convertToBizcircleResponse(resp *service.BizcircleResponse) *domain.BizcircleResponse {
//	districts := make([]domain.DistrictBizcircle, len(resp.Data))
//	for i, district := range resp.Data {
//		bizcircles := make([]domain.Bizcircle, len(district.Bizcircle))
//		for j, bizcircle := range district.Bizcircle {
//			bizcircles[j] = domain.Bizcircle{
//				BizcircleCode: bizcircle.BizcircleCode,
//				BizcircleName: bizcircle.BizcircleName,
//				PinyinInitial: bizcircle.PinyinInitial,
//			}
//		}
//
//		districts[i] = domain.DistrictBizcircle{
//			DistrictCode: district.DistrictCode,
//			DistrictName: district.DistrictName,
//			Bizcircle:    bizcircles,
//		}
//	}
//
//	return &domain.BizcircleResponse{
//		Data: districts,
//	}
//}
