package usecase

import (
	"ai-finder-bff/internal/application/service"
	"strings"
)

var (
	requiredUserLabels = map[string]string{
		"cuid":           "用户ID",
		"usertype":       "用户状态",
		"username":       "称呼",
		"age":            "年龄",
		"gender":         "性别",
		"is_identify_hy": "海燕认证状态",
	}
)

// 对话开场白
type UserInfoUseCase interface {
	// 获取用户扩展属性(在租状态/性别)
	GetExtendedUserInfo(uid, phone string) (map[string]string, error)
}

type userInfoUseCase struct {
	personaService service.PersonaService
}

func NewUserInfoUseCase(personaService service.PersonaService) UserInfoUseCase {
	return &userInfoUseCase{personaService: personaService}
}

func (u *userInfoUseCase) GetExtendedUserInfo(uid, phone string) (map[string]string, error) {
	userLabels, err := u.personaService.GetUserLabels(service.UserLabelsRequest{
		UserID:    uid,
		Phone:     phone,
		Type:      0, // -- 0: 租客, 1: 业主
		LabelKeys: mapKeys(requiredUserLabels),
	})
	if err != nil {
		return nil, err
	}

	extendedUserInfo := make(map[string]string)
	for k, v := range userLabels {
		if k != "username" {
			extendedUserInfo[requiredUserLabels[k]] = v
		}
	}

	// 在租用户，添加称呼
	username := userLabels["username"]
	if username != "" {
		callName := strings.ReplaceAll(username, "*", "") // 保留姓, 去掉 mask 字符
		if userLabels["gender"] == "男" {
			callName = callName + "先生"
		} else {
			callName = callName + "女士"
		}
		extendedUserInfo["称呼"] = callName
	}

	return extendedUserInfo, nil
}

func mapKeys(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
