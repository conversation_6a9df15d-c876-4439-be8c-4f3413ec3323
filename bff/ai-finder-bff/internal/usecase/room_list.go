package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/infrastructure/cache"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// 房间列表用例，根据过滤条件，返回房间列表
type RoomRetrieveUseCase interface {
	GetRoomList(filter domain.RoomFilter) (*domain.RoomListResponse, error)
}

type roomRetrieveUseCase struct {
	ymerService   service.YmerService
	erebusService service.ErebusService
	cache         *cache.CommonConditionCache
}

func NewRoomRetrieveUseCase(ymerService service.YmerService, erebusService service.ErebusService) RoomRetrieveUseCase {
	return &roomRetrieveUseCase{ymerService: ymerService,
		erebusService: erebusService,
		cache:         cache.NewCommonConditionCache(3*time.Hour, erebusService), // 3小时缓存
	}
}

func (uc *roomRetrieveUseCase) GetRoomList(filter domain.RoomFilter) (*domain.RoomListResponse, error) {
	// 转换为 ymer 请求
	request := uc.buildYmerRequest(filter)

	// 调用 ymer 服务
	resp, err := uc.ymerService.GetRoomList(request)
	if err != nil {
		return nil, fmt.Errorf("call ymer service failed: %w", err)
	}

	//
	ret := &domain.RoomListResponse{
		Summary: convertSummary(resp.Data.Rooms),
		Rooms:   convertRooms(resp.Data.Rooms),
	}

	// 获取推荐房源
	if len(ret.Rooms) == 0 {
		// 获取推荐房源
		promotionRooms, _ := uc.ymerService.GetPromotionRoomList(request)
		ret.PromotionRooms = convertRooms(promotionRooms.Data.Rooms)
	}
	// 转换响应为领域模型
	return ret, nil
}

// buildYmerRequest 构建 ymer 请求参数
func (uc *roomRetrieveUseCase) buildYmerRequest(filter domain.RoomFilter) service.RoomListV4Request {
	request := service.RoomListV4Request{}

	// 只设置非空值
	if filter.CityCode != "" && filter.CityCode != "null" {
		request.CityCode = filter.CityCode
	}
	// 整租类型，租房类型， 1合租；2整租；3老整租；4整租3.0；5NC整租 8:豪宅，6：自如寓
	if filter.Type != 0 {
		request.Type = strconv.Itoa(filter.Type)
		if filter.Type == 1 {
			i := 1
			request.Product = &i
			request.Type = "1"
		} else if filter.Type == 2 || filter.Type == 3 || filter.Type == 4 {
			i := 2
			request.Product = &i
			request.Type = "2|3|4"
		} else {
			// 默认合租
			i := 1
			request.Type = "1"
			request.Product = &i
		}
	} else {
		// 默认合租
		i := 1
		request.Product = &i
	}

	// 是否首次出租 & 未测试
	if filter.IsFirstRent != "" && filter.IsFirstRent != "null" {
		request.IsFirstRent = filter.IsFirstRent
	}
	// 租约类型
	if filter.Leasetype != "" && filter.Leasetype != "null" {
		request.Leasetype = filter.Leasetype
	}
	// 居室数量
	if filter.Bedroom != "" && filter.Bedroom != "null" {
		params, b := uc.cache.GetParams(request.CityCode, strconv.Itoa(*request.Product), "bedroom")
		if b {
			for _, pair := range params {
				if strings.Contains(pair.Key, filter.Bedroom) {
					request.Bedroom = pair.Value
					break
				}
			}
		}
	}
	// 面积区间
	if filter.Area != "" && filter.Area != "null" {
		request.Area = filter.Area
	}
	// 价格区间，格式：1000,2000
	if filter.Price != "" && filter.Price != "null" {
		request.Price = filter.Price
	}
	// 预计入住时间 "2024-05-10"
	if filter.CheckinDate != "" && filter.CheckinDate != "null" {
		request.CheckinDate = filter.CheckinDate
	}
	// 入住人数
	if filter.CheckinPeopleNum != "" && filter.CheckinPeopleNum != "null" {
		request.CheckinPeopleNum = filter.CheckinPeopleNum
	}
	// 朝向
	if filter.Hface != "" && filter.Hface != "null" {
		request.Hface = filter.Hface
	}

	// 特色户型（合租) 1: 独立卫生间 2. 独立阳台
	if filter.Layout != "" && filter.Layout != "null" {
		request.Layout = filter.Layout
	}

	// 房源特色 高层视野：71 带起居室:61 全屋智能：63 南北通透：72 loft：44
	if filter.Feature != "" && filter.Feature != "null" {
		request.Feature = filter.Feature
	}

	// 产品风格 友家7.0：7  友家6.0：6   心舍3.0：34  心舍2.0：21   心舍1.0：17
	if filter.Version != "" && filter.Version != "null" {
		request.Version = filter.Version
	}

	// 小区特色 新小区:8
	if filter.FeatureHouse != "" && filter.FeatureHouse != "null" {
		request.FeatureHouse = filter.FeatureHouse
	}

	// 供暖方式 集体供暖:3  独立供暖:2  中央供暖:1
	if filter.Heating != "" && filter.Heating != "null" {
		request.Heating = filter.Heating
	}

	// 合租室友 全男：2  全女：1
	if filter.Roommate != "" && filter.Roommate != "null" {
		request.Roommate = filter.Roommate
	}

	// 标签 可预定:9 有电梯:13
	if filter.Tag != "" && filter.Tag != "null" {
		request.Tag = filter.Tag
	}

	// 长租1年：62  长租2年：63  长租3年：64
	if filter.LeaseTypeDurationLong != "" && filter.LeaseTypeDurationLong != "null" {
		request.LeaseTypeDurationLong = filter.LeaseTypeDurationLong
	}

	// 月租 1个月：67  2个月：68  3个月：69
	if filter.LeaseTypeDurationMonth != "" && filter.LeaseTypeDurationMonth != "null" {
		request.LeaseTypeDurationMonth = filter.LeaseTypeDurationMonth
	}

	// 季租 4个月： 70  5个月：71  6个月：72
	if filter.LeaseTypeDurationSeason != "" && filter.LeaseTypeDurationSeason != "null" {
		request.LeaseTypeDurationSeason = filter.LeaseTypeDurationSeason
	}

	// 价钱由底到高
	request.Sort = "2"

	//if filter.Sort != "" && filter.Sort != "null" {
	//	request.Sort = filter.Sort
	//}

	if filter.Page != nil {
		request.Page = filter.Page
	}
	if filter.Size != nil {
		request.Size = filter.Size
	}

	// 位置
	if filter.LocationType != "" && filter.LocationValue != "" {
		request.SugType = strconv.Itoa(filter.SugType)
		request.SugValue = filter.LocationValue
	}
	// 距离
	if filter.Distance != nil {
		request.Distance = filter.Distance
	}

	// 通勤， 只有公司时，才通勤
	if filter.Minute != "" && filter.Minute != "null" && filter.Transport != "" && filter.Transport != "null" && filter.LocationType == "公司" {
		split := strings.Split(filter.LocationValue, ",")
		minut, err := strconv.Atoi(filter.Minute)
		if len(split) == 2 && err == nil {
			request.Price = ""
			longitude := split[0]
			latitude := split[1]
			request.Clng = longitude
			request.Clat = latitude
			request.Minute = &minut
			request.Transport = filter.Transport
			request.Company = "通勤公司"

			// 删除其他位置参数
			request.Distance = nil
			request.SugType = ""
			request.SugValue = ""
		}
	}

	return request
}

func convertToRoomListResponse(resp *service.RoomListV4Response) *domain.RoomListResponse {
	// 实现转换逻辑
	return &domain.RoomListResponse{
		Summary: convertSummary(resp.Data.Rooms),
		Rooms:   convertRooms(resp.Data.Rooms),
	}
}

func convertSummary(rooms []service.Room) string {
	for _, r := range rooms {
		if r.TemplateType == 26 {
			return r.Text
		}
	}
	return "未找到符合条件的房源"
}

func convertRooms(ymerRooms []service.Room) []domain.Room {
	rooms := make([]domain.Room, 0, 10)
	for _, r := range ymerRooms {
		// 只取15的
		if r.TemplateType != 15 && r.TemplateType != 1 {
			continue
		}
		if r.TemplateType == 1 && r.HouseTypeSaleState != 2 {
			continue
		}
		room := domain.Room{
			TemplateType:      r.TemplateType,
			Area:              r.Area,
			Bedroom:           r.Bedroom,
			BizcircleName:     r.BizcircleName,
			CanSignDate:       r.CanSignDate,
			CanSignLong:       r.CanSignLong,
			CanSignTime:       r.CanSignTime,
			Code:              r.Code,
			DistrictName:      r.DistrictName,
			Face:              r.Face,
			Floor:             r.Floor,
			FloorTotal:        r.FloorTotal,
			HouseCode:         r.HouseCode,
			HouseId:           r.HouseId,
			HouseType:         r.HouseType,
			Id:                r.Id,
			InvId:             r.InvId,
			InvNo:             r.InvNo,
			Lat:               r.Lat,
			Lng:               r.Lng,
			BuildingLat:       r.BuildingLat,
			BuildingLng:       r.BuildingLng,
			Name:              r.Name,
			NameV2:            r.NameV2,
			Parlor:            r.Parlor,
			PhotoIcons:        r.PhotoIcons,
			Photo:             r.Photo,
			PhotoMin:          r.PhotoMin,
			PhotoMinWebp:      r.PhotoMinWebp,
			PhotoWebp:         r.PhotoWebp,
			Price:             r.Price,
			PriceUnit:         r.PriceUnit,
			ResblockId:        r.ResblockId,
			ResblockName:      r.ResblockName,
			SaleStatus:        r.SaleStatus,
			ClientSaleStatus:  r.ClientSaleStatus,
			Source:            r.Source,
			StockStatus:       r.StockStatus,
			SubwayStationInfo: r.SubwayStationInfo,
			TagsRow1:          r.TagsRow1,
			Tags:              r.Tags,
			TagsRow2:          r.TagsRow2,
			DistanceInfo:      r.DistanceInfo,
			Type:              r.Type,
			TypeText:          r.TypeText,
			StyleTag:          r.StyleTag,
			ZiroomVersionId:   r.ZiroomVersionId,
			VersionName:       r.VersionName,
			CityCode:          r.CityCode,
			RoomNo:            r.RoomNo,
			RoomIntro:         r.RoomIntro,
			RoomIntros:        r.RoomIntros,
			Features:          r.Features,
			Addrs:             r.Addrs,
		}
		rooms = append(rooms, room)
	}
	return rooms
}
