package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
)

type ResblockUseCase interface {
	GetResblocks(request domain.ResblockRequest) (*domain.ResblockResponse, error)
}

type resblockUseCase struct {
	erebusService       service.ErebusService
	marsService         service.MarsService
	roomRetrieveUseCase RoomRetrieveUseCase
	ymerService         service.YmerService
}

func NewResblockUseCase(erebusService service.ErebusService, marsService service.MarsService, roomRetrieveUseCase RoomRetrieveUseCase, ymerService service.YmerService) ResblockUseCase {
	return &resblockUseCase{erebusService: erebusService, marsService: marsService, roomRetrieveUseCase: roomRetrieveUseCase, ymerService: ymerService}
}

func (uc *resblockUseCase) GetResblocks(request domain.ResblockRequest) (*domain.ResblockResponse, error) {
	// 商圈、地铁站、附近3Km，通勤
	// 1. 查询房源列表， 取前10套房子， 按顺序取3个不同的小区（小区）
	// 2. 通过聚合接口查询这3个小区的套数，以及价格区间等

	resblockIds, err := getResblockIds(request, uc)
	if err != nil {
		return &domain.ResblockResponse{
			ErrorCode:    200,
			Status:       "success",
			ErrorMessage: err.Error(),
		}, nil
	}

	terms := map[string]interface{}{
		"apartment_type": 1,
		"city_code":      request.CityCode,
	}
	// 历史参数
	if request.BizcircleCode != "" {
		terms["bizcircle_code"] = request.BizcircleCode
	} else if len(resblockIds) > 0 {
		terms["resblock_id"] = resblockIds
	}
	// 历史参数
	if request.BizcircleCode != "" {
		terms["bizcircle_code"] = request.BizcircleCode
	}
	if request.Hface != "" {
		terms["face"] = request.Hface
	}
	if request.Bedroom != "" {
		terms["house_bedroom"] = request.Bedroom
	}
	// 整租类型，租房类型， 1合租；2整租；3老整租；4整租3.0；5NC整租 8:豪宅，6：自如寓
	if request.Type != nil && *request.Type != 0 {
		if *request.Type == 1 {
			terms["product_category"] = 1
		} else if *request.Type == 2 || *request.Type == 3 || *request.Type == 4 {
			terms["product_category"] = []int{2, 3, 4}
		} else {
			// 默认合租
			terms["product_category"] = 1
		}
	} else {
		// 默认合租
		terms["product_category"] = 1
	}

	// range
	ranges := make(map[string][]*int)
	if request.Price != "" {
		split := strings.Split(request.Price, ",")
		if len(split) >= 2 {
			min, err := strconv.Atoi(split[0])
			if err != nil {
				min = 0
			}
			max, err := strconv.Atoi(split[1])
			if err != nil {
				max = 0
			}

			if max > 0 {
				val := []*int{&min, &max}
				ranges["sort_price"] = val
			}

		}
	}
	if request.Area != "" {
		split := strings.Split(request.Area, ",")
		if len(split) >= 2 {
			min, err := strconv.Atoi(split[0])
			if err != nil {
				min = 0
			}
			max, err := strconv.Atoi(split[1])
			if err != nil {
				max = 0
			}

			if max > 0 {
				val := []*int{&min, &max}
				ranges["size"] = val
			}

		}
	}

	filters := service.Filters{
		Terms: terms,
	}
	if len(ranges) > 0 {
		filters.Range = ranges
	}

	erebusRequest := service.AggregateRequest{
		Filters: service.Filters{
			Terms: terms,
		},
		Groups: map[string]service.GroupInfo{
			"resblock_id": service.GroupInfo{
				Field: map[string]string{"resblock_id": "terms"},
				Size:  3,
				Groups: map[string]service.GroupFieldConfig{
					"min_price": service.GroupFieldConfig{
						Field: map[string]string{
							"sort_price": "min",
						},
					},
					"max_price": service.GroupFieldConfig{
						Field: map[string]string{
							"sort_price": "max",
						},
					},
				},
			},
		},
	}

	/*
		{"filters":{"terms":{"apartment_type":1,"bizcircleCode":"","city_code":"110000","rent_unit_type":[2,3]}},"groups":{"resblock_id":{"field":{"resblock_id":"terms"},"size":3,"groups":{"max_price":{"field":{"sort_price":"max"}},"min_price":{"field":{"sort_price":"min"}}}}}}
	*/
	aggregate, err := uc.erebusService.Aggregate(erebusRequest)
	if err != nil {
		return nil, fmt.Errorf("call resb service failed: %w", err)
	}

	// 转换响应实体
	return &domain.ResblockResponse{
		ErrorCode: 200,
		Status:    "success",
		Data:      convertToResblockData(aggregate, uc.marsService, uc.ymerService),
	}, nil

}

func getResblockIds(request domain.ResblockRequest, uc *resblockUseCase) ([]json.RawMessage, error) {
	resblockIds := make([]json.RawMessage, 0, 3)

	page := 1
	size := 10
	roomfilter := domain.RoomFilter{
		CityCode:      request.CityCode,
		SugType:       request.SugType,
		LocationType:  request.LocationType,
		LocationValue: request.LocationValue,
		Distance:      request.Distance,
		Minute:        request.Minute,
		Transport:     request.Transport,
		Price:         request.Price,
		Bedroom:       request.Bedroom,
		Hface:         request.Hface,
		Area:          request.Area,
		Page:          &page,
		Size:          &size,
	}
	if request.Type != nil {
		roomfilter.Type = *request.Type
	}
	roomList, err := uc.roomRetrieveUseCase.GetRoomList(roomfilter)
	if err != nil {
		return resblockIds, fmt.Errorf("call ymer service failed: %w", err)
	}

	if len(roomList.Rooms) == 0 {
		return resblockIds, fmt.Errorf("no match rooms")
	}
	for _, r := range roomList.Rooms {
		resbId := r.ResblockId
		exists := false

		// 遍历 resblockIds 查找是否已经包含该 resbId
		for _, rm := range resblockIds {
			if bytes.Equal(rm, resbId) {
				exists = true
				break // 找到重复项时提前跳出循环
			}
		}

		if !exists {
			resblockIds = append(resblockIds, resbId)
		}

		if len(resblockIds) >= 3 {
			break // 达到3个元素后退出外层循环
		}
	}
	return resblockIds, err
}

func convertToResblockData(aggregate *service.AggregateResponse, marsService service.MarsService, ymerService service.YmerService) []domain.ResblockData {

	aggs := aggregate.Data.Aggs["resblock_id"]

	ret := make([]domain.ResblockData, 0, 5)
	for _, v := range aggs {
		resblockId := fmt.Sprintf("%v", v["key"])
		resblock := domain.ResblockData{
			ResblockId: resblockId,
			HouseCount: fmt.Sprintf("%v", v["docCount"]),
			MaxPrice:   fmt.Sprintf("%v", v["max_price"]),
			MinPrice:   fmt.Sprintf("%v", v["min_price"]),
		}

		detail, _ := marsService.GetResblockDetail(service.ResblockDetailRequest{ResblockId: resblockId})
		if detail != nil {
			resblock.ResblockName = detail.Data.Name
			resblock.Lat = detail.Data.Lat
			resblock.Lng = detail.Data.Lng
		}

		detailPic, _ := marsService.GetResblockPicDetail(service.ResblockDetailRequest{ResblockId: resblockId})
		if detailPic != nil {
			if len(detailPic.Data.ResblockImage) > 0 {
				resblock.PicInfo = detailPic.Data.ResblockImage[0].PicUrl
			}
		}

		// 获取小区标签等信息
		resblockList, _ := ymerService.GetResblockListV4(service.ResblockListV4Request{
			ResblockIDs: []string{resblockId},
		})
		if resblockList != nil && len(resblockList.Data.Rooms) > 0 {
			room := resblockList.Data.Rooms[0]
			// 添加标签信息
			tags := make([]domain.Tag, 0, len(room.Tags))
			for _, tag := range room.Tags {
				tags = append(tags, domain.Tag{
					Title: tag.Title,
				})
			}
			resblock.Tags = tags

			// 添加周边信息
			surrounds := make([]domain.SurroundInfo, 0, len(room.Surround))
			for _, surround := range room.Surround {
				surrounds = append(surrounds, domain.SurroundInfo{
					Title: surround.Title,
					Count: surround.Count,
				})
			}
			resblock.Surround = surrounds

			resblock.TemplateType = room.TemplateType
			resblock.Pic = room.Pic

			resblock.Infos = room.Infos
			resblock.Pic = room.Pic
			resblock.Title = room.Title
			resblock.Price = room.Price
			resblock.PriceUnit = room.PriceUnit
			resblock.Router = domain.Router{
				Target:    room.Router.Target,
				Parameter: room.Router.Parameter,
			}

			// 添加其他信息
			resblock.Infos = room.Infos
			resblock.SubwayStationInfo = room.SubwayStationInfo
		}

		ret = append(ret, resblock)
	}
	return ret
}
