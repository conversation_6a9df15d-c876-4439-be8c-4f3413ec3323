package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/domain"
	"fmt"
)

// ReserveUseCase represents the use case interface for reservation operations
type ReserveUseCase interface {
	CreateReservation(request domain.ReserveRequest) *domain.ReserveResponse
}

// reserveUseCase implements the ReserveUseCase interface
type reserveUseCase struct {
	rentOrderService  service.RentOrderService
	darkPortalService service.DarkPortalService
	rentOqService     service.RentOqService
}

// NewReserveUseCase creates a new instance of ReserveUseCase
func NewReserveUseCase(rentOrderService service.RentOrderService, darkPortalService service.DarkPortalService, rentOqService service.RentOqService) ReserveUseCase {
	return &reserveUseCase{
		rentOrderService:  rentOrderService,
		darkPortalService: darkPortalService,
		rentOqService:     rentOqService,
	}
}

func errorResponse(code int, message string) *domain.ReserveResponse {
	return &domain.ReserveResponse{
		Code:    code,
		Message: message,
	}
}

// CreateReservation creates a new reservation
func (uc *reserveUseCase) CreateReservation(request domain.ReserveRequest) *domain.ReserveResponse {
	// Validate required fields
	if request.Token == "" {
		return errorResponse(400, "token is null")
	}
	if request.CityCode == "" {
		return errorResponse(400, "cityCode is null")
	}
	if len(request.HouseList) == 0 {
		return errorResponse(400, "houseList is null")
	}
	for _, v := range request.HouseList {
		if v.ResblockId == nil {
			return errorResponse(400, "resblockId is null")
		}
		if v.HouseId == nil {
			return errorResponse(400, "houseId is null")
		}
	}

	if request.ExpectTime == "" {
		return errorResponse(400, "expectTime is null")
	}

	// 构建参数
	createReservationParams, err := uc.buildReserveParams(request)
	if err != nil {
		return errorResponse(500, err.Error())
	}

	// 预约看房，创建看房单
	createReservationResp, err := uc.rentOrderService.CreateReservation(*createReservationParams)
	if err != nil {
		return errorResponse(500, err.Error())
	}
	if createReservationResp.Code != 200 {
		return errorResponse(500, fmt.Sprintf("调用创建看房单接口失败:%s", createReservationResp.Message))
	}

	// 预约单号
	mainOrderNum := createReservationResp.Data.Router.Parameter.AppointOrderNum
	if mainOrderNum == "" {
		return errorResponse(500, "调用创建看房单接口失败: 返回mainOrderNum为空")
	}

	// 获取预约信息
	keeper, house := uc.getReserveInfo(mainOrderNum, request.Token)

	ret := domain.ReserveResponse{}
	ret.Code = 200
	ret.Message = "success"
	ret.Toast = createReservationResp.Data.Toast
	ret.MainOrderNum = mainOrderNum
	ret.KeeperInfo = keeper
	ret.HouseInfo = house
	return &ret
}

func (uc *reserveUseCase) getReserveInfo(mainOrderNum string, token string) (*service.WatchingDtlListKeeperResp, *service.WatchingDtlListHouseResp) {
	params := service.RentOqReq{
		Token:        token,
		MainOrderNum: mainOrderNum,
		AppVersion:   "1.1",
	}

	// 查询管家信息
	keeper, _ := uc.rentOqService.WatchingDtlListKeeper(params)

	// 查询房源信息
	house, _ := uc.rentOqService.WatchingDtlListHouse(params)
	return keeper, house
}

func (uc *reserveUseCase) buildReserveParams(request domain.ReserveRequest) (*service.ReserveRequest, error) {
	// 查询DP用户信息
	dpUserInfoResponse, err := uc.darkPortalService.GetUserInfo(request.Token)
	if err != nil {
		return nil, err
	}

	houseInfoList := make([]service.HouseInfo, 0, len(request.HouseList))
	for _, v := range request.HouseList {
		houseInfoList = append(houseInfoList, service.HouseInfo{
			VillageID:  v.ResblockId, // 映射字段
			HouseInvNo: v.HouseId,    // 映射字段
		})
	}

	req := service.ReserveRequest{
		Uid:                  dpUserInfoResponse.Uid,
		Token:                request.Token,
		UserName:             dpUserInfoResponse.Username, // 昵称，可能不存在
		UserPhone:            dpUserInfoResponse.Phone,
		ReserveSource:        "0",
		ReserveChannel:       "kanfangdan",
		CityCode:             request.CityCode,
		UserChoiceResblockID: request.HouseList[0].ResblockId, //取第一个楼盘
		TripList: []service.Trip{
			{
				Mode:              "ON_SPOT",
				CentralResblockID: request.HouseList[0].ResblockId,
				ExpectTime:        request.ExpectTime,
				HouseInfoList:     houseInfoList,
			},
		},
		NeedSycCustomerShoppingCar: false,
		ShowEntrance:               "k_kfd_houseviewing",
	}
	return &req, nil
}
