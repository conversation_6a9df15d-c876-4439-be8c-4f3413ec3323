package usecase

import (
	"ai-finder-bff/internal/application/service"
	"ai-finder-bff/internal/infrastructure/external"
	"encoding/json"
	"reflect"
	"testing"
)

func Test_reserveUseCase_getReserveInfo(t *testing.T) {
	rentOrderClient := external.NewRentOrderHttpClient(external.HTTPClientConfig{
		BaseURL: "http://rentorder.kq.ziroom.com",
	})
	rentOqClient := external.NewRentOqHttpClient(external.HTTPClientConfig{
		BaseURL: "http://rentoq.q.ziroom.com",
	})
	darkPortalClient := external.NewDarkPortalHttpClient(external.HTTPClientConfig{
		BaseURL: "http://darkportal-api.kq.ziroom.com",
	})

	uc := &reserveUseCase{
		rentOrderService:  rentOrderClient,
		darkPortalService: darkPortalClient,
		rentOqService:     rentOqClient,
	}

	tests := []struct {
		name string
		args struct {
			mainOrderNum string
			token        string
		}
		want  *service.WatchingDtlListKeeperResp
		want1 *service.WatchingDtlListHouseResp
	}{
		{
			name: "正常情况",
			args: struct {
				mainOrderNum string
				token        string
			}{
				mainOrderNum: "1621727928109311232",
				token:        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwOWQ0M2M3MS01OGRmLTRlMzYtODhlZi1mNjFiODI2NDUxODciLCJ0eXBlIjoxLCJsZW5ndGgiOjQzMjAwLCJ0b2tlbiI6IjY2ZGVkY2MwLTY4MDAtNDk3NC1iZmYzLTUyYzFkNGE5MTQ2YyIsImNyZWF0ZVRpbWUiOjE3NDgzNDI3NTA3NDF9.9xz5U80ELBCgi5txE02npDBBpqhMqzoWN0TVRB-ZjeI",
			},
			want:  &service.WatchingDtlListKeeperResp{ /* 填入期望数据 */ },
			want1: &service.WatchingDtlListHouseResp{ /* 填入期望数据 */ },
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := uc.getReserveInfo(tt.args.mainOrderNum, tt.args.token)
			gotJSON, _ := json.Marshal(got)
			gotJSON1, _ := json.Marshal(got1)
			t.Log("111111111")
			t.Log(string(gotJSON))
			t.Log(string(gotJSON1))
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getReserveInfo() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("getReserveInfo() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
