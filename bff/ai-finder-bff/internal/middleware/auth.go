package middleware

import (
	"context"
	"net/http"
	"strings"
)

// AuthMiddleware is a middleware that checks for a valid auth token
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		token := r.Header.Get("Authorization")
		if token == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Remove "Bearer " prefix if present
		token = strings.TrimPrefix(token, "Bearer ")

		// TODO: Validate token
		// This is where you would typically validate the JWT token
		// For now, we'll just add the token to the context

		ctx := context.WithValue(r.Context(), "user_token", token)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
