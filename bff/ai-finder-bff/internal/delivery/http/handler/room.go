package handler

import (
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"go.uber.org/zap"
)

type RoomRetrieveHandler struct {
	roomRetrieveUseCase usecase.RoomRetrieveUseCase
}

func NewRoomRetrieveHandler(roomRetrieveUseCase usecase.RoomRetrieveUseCase) *RoomRetrieveHandler {
	return &RoomRetrieveHandler{roomRetrieveUseCase: roomRetrieveUseCase}
}

func (h *RoomRetrieveHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/rooms", func(r chi.Router) {
		r.Post("/", h.GetRoomList)
	})
}

func (h *RoomRetrieveHandler) GetRoomList(w http.ResponseWriter, r *http.Request) {
	log := logger.GetLogger()

	roomFilter := domain.RoomFilter{}
	// 解析请求体
	if err := render.DecodeJSON(r.Body, &roomFilter); err != nil {
		log.Error("parse request body failed", zap.Error(err))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "参数格式错误",
		})
		return
	}

	log.Info("GetRoomList", zap.Any("params", roomFilter))

	// 调用用例获取房源列表
	rooms, err := h.roomRetrieveUseCase.GetRoomList(roomFilter)
	if err != nil {
		log.Error("get room list failed", zap.Error(err))
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]interface{}{
			"error": err.Error(),
		})
		return
	}

	render.JSON(w, r, map[string]interface{}{
		"data": rooms,
	})
}
