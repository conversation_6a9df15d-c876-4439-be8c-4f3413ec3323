package handler

import (
	"ai-finder-bff/internal/usecase"
	"fmt"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
)

type UserInfoHandler struct {
	userInfoUseCase usecase.UserInfoUseCase
}

func NewUserInfoHandler(userInfoUseCase usecase.UserInfoUseCase) *UserInfoHandler {
	return &UserInfoHandler{userInfoUseCase: userInfoUseCase}
}

func (h *UserInfoHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/user", func(r chi.Router) {
		r.Get("/info", h.GetUserInfo)
	})
}

func (h *UserInfoHandler) GetUserInfo(w http.ResponseWriter, r *http.Request) {
	uid := r.URL.Query().Get("uid")
	phone := r.URL.Query().Get("phone")

	extendedUserInfo, err := h.userInfoUseCase.GetExtendedUserInfo(uid, phone)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	background := `## 用户基础信息\n`
	for k, v := range extendedUserInfo {
		background += fmt.Sprintf("- %s: %s\n", k, v)
	}

	render.JSON(w, r, map[string]interface{}{
		"data": background,
	})
}
