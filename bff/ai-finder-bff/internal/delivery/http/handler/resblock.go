package handler

import (
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"go.uber.org/zap"
)

type ResblockHandler struct {
	resblockUseCase usecase.ResblockUseCase
}

func NewResblockHandler(resblockUseCase usecase.ResblockUseCase) *ResblockHandler {
	return &ResblockHandler{resblockUseCase: resblockUseCase}
}

func (h *ResblockHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/resblocks", func(r chi.Router) {
		r.Get("/", h.GetBizcircle)
	})
}

func (h *ResblockHandler) GetBizcircle(w http.ResponseWriter, r *http.Request) {
	log := logger.GetLogger()

	// 从查询参数获取请求参数
	request := domain.ResblockRequest{
		CityCode:      r.URL.Query().Get("city_code"),
		BizcircleCode: r.URL.Query().Get("bizcircle_code"),
		Type:          getIntFromQuery(r, "type"),
		SugType:       getIntValueFromQuery(r, "sug_type"),
		LocationType:  r.URL.Query().Get("location_type"),
		LocationValue: r.URL.Query().Get("location_value"),
		Minute:        r.URL.Query().Get("minute"),
		Transport:     r.URL.Query().Get("transport"),
		Price:         r.URL.Query().Get("price"),
		Bedroom:       r.URL.Query().Get("bedroom"),
		Hface:         r.URL.Query().Get("hface"),
		Area:          r.URL.Query().Get("area"),
		TestInfo:      r.URL.Query().Get("test_info"),   // AB test
		AppVersion:    r.URL.Query().Get("app_version"), // APP api 版本
	}
	distance := r.URL.Query().Get("distance")
	distanceI, err2 := strconv.Atoi(distance)
	if err2 == nil {
		request.Distance = &distanceI
	}

	// 参数验证
	if request.CityCode == "" {
		log.Error("invalid parameters", zap.String("city_code", request.CityCode))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "参数错误：city_code 不能为空",
		})
		return
	}

	// 调用用例获取商圈信息
	response, err := h.resblockUseCase.GetResblocks(request)

	if err != nil {
		log.Error("get resblocks failed",
			zap.Error(err),
			zap.Any("request", request))
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]interface{}{
			"error": "获取楼盘信息失败",
		})
		return
	}

	render.JSON(w, r, response)
}

func getIntFromQuery(r *http.Request, key string) *int {
	value := r.URL.Query().Get(key)
	if value == "" {
		return nil
	}
	intValue, err := strconv.Atoi(value)
	if err != nil {
		return nil
	}
	return &intValue
}

func getIntValueFromQuery(r *http.Request, key string) int {
	value := r.URL.Query().Get(key)
	if value == "" {
		return 0
	}
	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0
	}
	return intValue
}
