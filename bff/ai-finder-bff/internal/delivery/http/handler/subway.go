package handler

import (
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"go.uber.org/zap"
)

type SubwayHandler struct {
	subwayStationUserCase usecase.SubwayStationUseCase
}

func NewSubwayHandler(subwayStationUserCase usecase.SubwayStationUseCase) *SubwayHandler {
	return &SubwayHandler{subwayStationUserCase: subwayStationUserCase}
}

func (h *SubwayHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/subway", func(r chi.Router) {
		r.Get("/subwayStations", h.GetSubwayStations)
	})
}

func (h *SubwayHandler) GetSubwayStations(w http.ResponseWriter, r *http.Request) {
	log := logger.GetLogger()

	// 从查询参数获取请求参数
	request := domain.SubwayStationRequest{
		CityCode:        r.URL.Query().Get("city_code"),
		SubwayLine:      r.URL.Query().Get("subway_line"),
		ProductCategory: r.URL.Query().Get("product_category"),
	}

	// 参数验证
	if request.CityCode == "" {
		log.Error("invalid parameters", zap.String("city_code", request.CityCode))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "参数错误：city_code 不能为空",
		})
		return
	}
	if request.SubwayLine == "" {
		log.Error("invalid parameters", zap.String("subway_line", request.SubwayLine))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "参数错误：subway_line 不能为空",
		})
		return
	}

	// 调用用例获取商圈信息
	response, err := h.subwayStationUserCase.GetSubwayStations(request)

	if err != nil {
		log.Error("get resblocks failed",
			zap.Error(err),
			zap.Any("request", request))
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]interface{}{
			"error": "获取地铁站信息失败",
		})
		return
	}

	render.JSON(w, r, response)
}
