package handler

import (
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"go.uber.org/zap"
)

// ReserveHandler handles reservation-related HTTP requests
type ReserveHandler struct {
	reserveUseCase usecase.ReserveUseCase
}

// NewReserveHandler creates a new instance of ReserveHandler
func NewReserveHandler(reserveUseCase usecase.ReserveUseCase) *ReserveHandler {
	return &ReserveHandler{
		reserveUseCase: reserveUseCase,
	}
}

// RegisterRoutes registers the routes for reservation endpoints
func (h *ReserveHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/createReservation", func(r chi.Router) {
		r.Post("/", h.CreateReservation)
	})
}

// CreateReservation handles the creation of a new reservation
func (h *ReserveHandler) CreateReservation(w http.ResponseWriter, r *http.Request) {
	log := logger.GetLogger()

	// Parse request body
	var request domain.ReserveRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Error("failed to decode request body", zap.Error(err))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "invalid request body",
		})
		return
	}

	response := h.reserveUseCase.CreateReservation(request)

	render.JSON(w, r, response)
}
