package handler

import (
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"go.uber.org/zap"
)

type BizcircleHandler struct {
	bizcircleUseCase usecase.BizcircleUseCase
}

func NewBizcircleHandler(bizcircleUseCase usecase.BizcircleUseCase) *BizcircleHandler {
	return &BizcircleHandler{bizcircleUseCase: bizcircleUseCase}
}

func (h *BizcircleHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/bizcircles", func(r chi.Router) {
		r.Get("/", h.GetBizcircle)
	})
}

func (h *BizcircleHandler) GetBizcircle(w http.ResponseWriter, r *http.Request) {
	log := logger.GetLogger()

	// 从查询参数获取请求参数
	request := domain.BizcircleRequest{
		CityCode:     r.URL.Query().Get("city_code"),
		DistrictCode: r.URL.Query().Get("district_code"),
		Type:         r.URL.Query().Get("type"),
		Price:        r.URL.Query().Get("price"),
		Bedroom:      r.URL.Query().Get("bedroom"),
		HFace:        r.URL.Query().Get("hface"),
	}

	// 参数验证
	if request.CityCode == "" && request.DistrictCode == "" {
		log.Error("invalid parameters", zap.String("city_code", request.CityCode), zap.String("district_code", request.DistrictCode))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "参数错误：city_code，district_code 不能同时为空",
		})
		return
	}

	// 调用用例获取商圈信息
	response, err := h.bizcircleUseCase.GetBizcircle(request)

	if err != nil {
		log.Error("get bizcircle failed",
			zap.Error(err),
			zap.Any("request", request))
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]interface{}{
			"error": "获取商圈信息失败",
		})
		return
	}

	render.JSON(w, r, response)
}
