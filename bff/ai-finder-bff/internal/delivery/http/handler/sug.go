package handler

import (
	"ai-finder-bff/internal/domain"
	"ai-finder-bff/internal/pkg/logger"
	"ai-finder-bff/internal/usecase"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/render"
	"go.uber.org/zap"
)

type SearchSugHandler struct {
	searchSugUseCase usecase.SearchSugUseCase
}

func NewSearchSugHandler(searchSugUseCase usecase.SearchSugUseCase) *SearchSugHandler {
	return &SearchSugHandler{searchSugUseCase: searchSugUseCase}
}

func (h *SearchSugHandler) RegisterRoutes(r *chi.Mux) {
	r.Route("/search", func(r chi.Router) {
		r.Get("/sug", h.GetSearchSug)
	})
}

func (h *SearchSugHandler) GetSearchSug(w http.ResponseWriter, r *http.Request) {
	log := logger.GetLogger()

	// 从查询参数获取请求参数
	request := domain.SearchSugRequest{
		CityCode:    r.URL.Query().Get("city_code"),
		FromScene:   r.URL.Query().Get("from_scene"),
		Keyword:     r.URL.Query().Get("keyword"),
		SugTypeName: r.URL.Query().Get("sug_type_name"),
	}

	// 参数验证
	if request.CityCode == "" || request.Keyword == "" {
		log.Error("invalid parameters",
			zap.String("city_code", request.CityCode),
			zap.String("keyword", request.Keyword))
		render.Status(r, http.StatusBadRequest)
		render.JSON(w, r, map[string]interface{}{
			"error": "参数错误：city_code 和 keyword 不能为空",
		})
		return
	}

	// 如果 from_scene 为空，设置默认值
	if request.FromScene == "" {
		request.FromScene = "AppIndexSearch"
	}

	// 调用用例获取搜索建议
	response, err := h.searchSugUseCase.GetSearchSug(request)
	if err != nil {
		log.Error("get search suggestions failed",
			zap.Error(err),
			zap.Any("request", request))
		render.Status(r, http.StatusInternalServerError)
		render.JSON(w, r, map[string]interface{}{
			"error": "获取搜索建议失败",
		})
		return
	}

	render.JSON(w, r, response)
}
