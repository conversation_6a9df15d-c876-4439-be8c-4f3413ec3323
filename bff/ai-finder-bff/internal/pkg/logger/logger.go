package logger

import (
	"os"
	"sync"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	// Log is the global logger
	Log  *zap.Logger
	once sync.Once
)

// Config holds the logger configuration
type Config struct {
	Level      string         `json:"level"`       // debug, info, warn, error, dpanic, panic, fatal
	OutputPath string         `json:"output_path"` // stdout, stderr, or file path
	Format     string         `json:"format"`      // json or console
	Rotation   RotationConfig `json:"rotation"`
}

// RotationConfig holds the log rotation configuration
type RotationConfig struct {
	MaxSize    int  `json:"max_size"`    // megabytes
	MaxAge     int  `json:"max_age"`     // days
	MaxBackups int  `json:"max_backups"` // files
	Compress   bool `json:"compress"`    // compress rotated files
}

// TraceID represents the context key for trace ID
type TraceID string

const (
	// TraceIDKey is the key used in the context for trace ID
	TraceIDKey TraceID = "trace_id"
	// DefaultTraceIDKey is the key used in log fields for trace ID
	DefaultTraceIDKey = "trace_id"
)

// Initialize sets up the logger with the given configuration
func Initialize(config Config) error {
	var err error
	once.Do(func() {
		// 设置日志级别
		level := zap.NewAtomicLevel()
		err = level.UnmarshalText([]byte(config.Level))
		if err != nil {
			return
		}

		// 配置编码器
		encoderConfig := zap.NewProductionEncoderConfig()
		encoderConfig.TimeKey = "timestamp"
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

		// 选择编码器
		var encoder zapcore.Encoder
		if config.Format == "console" {
			encoder = zapcore.NewConsoleEncoder(encoderConfig)
		} else {
			encoder = zapcore.NewJSONEncoder(encoderConfig)
		}

		// 设置输出
		var output zapcore.WriteSyncer
		switch config.OutputPath {
		case "stdout":
			output = zapcore.AddSync(os.Stdout)
		case "stderr":
			output = zapcore.AddSync(os.Stderr)
		default:
			// 使用 lumberjack 进行日志轮转
			output = zapcore.AddSync(&lumberjack.Logger{
				Filename:   config.OutputPath,
				MaxSize:    config.Rotation.MaxSize,    // 每个文件最大尺寸（MB）
				MaxAge:     config.Rotation.MaxAge,     // 保留天数
				MaxBackups: config.Rotation.MaxBackups, // 保留文件个数
				Compress:   config.Rotation.Compress,   // 是否压缩
			})
		}

		// 创建核心
		core := zapcore.NewCore(encoder, output, level)

		// 创建logger
		Log = zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	})

	return err
}

// GetLogger returns the global logger instance
func GetLogger() *zap.Logger {
	if Log == nil {
		// 如果没有初始化，使用默认配置
		defaultConfig := Config{
			Level:      "info",
			OutputPath: "stdout",
			Format:     "json",
			Rotation: RotationConfig{
				MaxSize:    100,
				MaxAge:     7,
				MaxBackups: 3,
				Compress:   true,
			},
		}
		if err := Initialize(defaultConfig); err != nil {
			panic(err)
		}
	}
	return Log
}

// WithContext adds context fields to the logger
func WithContext(fields ...zapcore.Field) *zap.Logger {
	return GetLogger().With(fields...)
}

// WithTraceID adds trace ID to the logger
func WithTraceID(traceID string) *zap.Logger {
	return GetLogger().With(zap.String(DefaultTraceIDKey, traceID))
}
