package: go.uber.org/zap
license: MIT
import:
- package: go.uber.org/atomic
  version: ^1
- package: go.uber.org/multierr
  version: ^1
testImport:
- package: github.com/satori/go.uuid
- package: github.com/sirupsen/logrus
- package: github.com/apex/log
  subpackages:
  - handlers/json
- package: github.com/go-kit/kit
  subpackages:
  - log
- package: github.com/stretchr/testify
  subpackages:
  - assert
  - require
- package: gopkg.in/inconshreveable/log15.v2
- package: github.com/mattn/goveralls
- package: github.com/pborman/uuid
- package: github.com/pkg/errors
- package: github.com/rs/zerolog
- package: golang.org/x/tools
  subpackages:
  - cover
- package: golang.org/x/lint
  subpackages:
  - golint
- package: github.com/axw/gocov
  subpackages:
  - gocov
