# Object files
.DS_Store
.vscode
.idea

examples/examples
examples/jp/jp
# 
data/dict/zh/dict.txt
data/dict/zh/dictionary.txt

vendor
# vendor/github.com/vcaesar/tt

# Debug files
*.dSYM/
*.su
debug

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

*.o
*.ko
*.obj
*.elf

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Binaries for programs and plugins

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
# *.out

# Project-local glide cache, RE: https://github.com/Masterminds/glide/issues/736
.glide/
examples/dict/embed/embed
examples/dict/embed/main
oryxBuildBinary