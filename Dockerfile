# 基于Python 3.13镜像
FROM harbor.ziroom.com/public/python3:3.13-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_COLOR=1 \
    ENV_PATH=/app/conf/.env

# 创建配置目录
RUN mkdir -p /app/conf

# 复制requirements.txt
COPY requirements.txt .

# 使用一次性安装所有依赖，但禁用进度条以避免创建不必要的线程
RUN pip install --no-cache-dir --no-deps --progress-bar off \
    urllib3==1.26.15 requests==2.28.2 \
    -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 使用--no-deps --no-progress-bar选项安装其余依赖
RUN cat requirements.txt | grep -v urllib3 | xargs -L 1 pip install --no-cache-dir --no-deps --progress-bar off \
    -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 复制应用代码
COPY . .

# 创建非root用户运行应用
RUN adduser --disabled-password --gecos "" appuser
RUN chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 设置启动命令 - 修复uvicorn参数
CMD ["uvicorn", "app.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "4", \
     "--access-log", \
     "--log-level", "info"]