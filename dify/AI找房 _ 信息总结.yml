app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: AI找房 | 信息总结
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: code
      id: 1741244695210-source-1741251528666-target
      source: '1741244695210'
      sourceHandle: source
      target: '1741251528666'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 1741251528666-source-1741330681785-target
      source: '1741251528666'
      sourceHandle: source
      target: '1741330681785'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-true-1741250703991-target
      source: '1741330681785'
      sourceHandle: 'true'
      target: '1741250703991'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-c6200137-0051-4cb0-b65f-1dde7706fad3-1741250703991-target
      source: '1741330681785'
      sourceHandle: c6200137-0051-4cb0-b65f-1dde7706fad3
      target: '1741250703991'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-0984dcbb-b498-4e11-8be9-77f3570c700b-17413305926220-target
      selected: false
      source: '1741330681785'
      sourceHandle: 0984dcbb-b498-4e11-8be9-77f3570c700b
      target: '17413305926220'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 1741250703991-source-1741332699941-target
      source: '1741250703991'
      sourceHandle: source
      target: '1741332699941'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 17413305926220-source-1741332693003-target
      source: '17413305926220'
      sourceHandle: source
      target: '1741332693003'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-31e087ca-13e0-40c8-97cd-1c2145972b72-1741343544159-target
      source: '1741330681785'
      sourceHandle: 31e087ca-13e0-40c8-97cd-1c2145972b72
      target: '1741343544159'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 1741343544159-source-1741342505499-target
      source: '1741343544159'
      sourceHandle: source
      target: '1741342505499'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-false-1741343544159-target
      source: '1741330681785'
      sourceHandle: 'false'
      target: '1741343544159'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-2af62880-f703-41be-b348-932f0445ba3e-17422644481720-target
      source: '1741330681785'
      sourceHandle: 2af62880-f703-41be-b348-932f0445ba3e
      target: '17422644481720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 17422644481720-source-17422644574460-target
      source: '17422644481720'
      sourceHandle: source
      target: '17422644574460'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-1ddc92ba-462e-4417-80c9-58d8b17cd0ad-17422644481720-target
      source: '1741330681785'
      sourceHandle: 1ddc92ba-462e-4417-80c9-58d8b17cd0ad
      target: '17422644481720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-4c67a7d1-7e77-4281-b1b8-06903e878ae7-17422644481720-target
      source: '1741330681785'
      sourceHandle: 4c67a7d1-7e77-4281-b1b8-06903e878ae7
      target: '17422644481720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-11792e2f-2088-42fb-89c5-02634a08fabc-17422644481720-target
      source: '1741330681785'
      sourceHandle: 11792e2f-2088-42fb-89c5-02634a08fabc
      target: '17422644481720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-f1bda551-b3d8-4d0b-bd7e-fa0624fd9557-17422644481720-target
      source: '1741330681785'
      sourceHandle: f1bda551-b3d8-4d0b-bd7e-fa0624fd9557
      target: '17422644481720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1741330681785-656ed1a6-c17a-4eaf-a934-dc95381048d0-17422644481720-target
      source: '1741330681785'
      sourceHandle: 656ed1a6-c17a-4eaf-a934-dc95381048d0
      target: '17422644481720'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 位置类型
          max_length: 48
          options:
          - 城市
          - 行政区
          - 商圈
          - 地铁线
          - 地铁
          - 小区
          - 通勤
          - 地点周边
          - 公司
          - 学校
          - 地点
          - 写字楼
          - 商场
          required: false
          type: select
          variable: location_type
        - label: 房源数据，json 数组字符串
          max_length: 8192
          options: []
          required: true
          type: paragraph
          variable: data
        - label: 地理位置
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: location
        - label: 匹配房源数量(文本描述)
          max_length: 48
          options: []
          required: true
          type: text-input
          variable: matched_rooms_total
        - label: 推荐召回修改的条件描述
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: adjusted_search_params
        - label: 推荐房源/商圈/...列表
          max_length: 8192
          options: []
          required: false
          type: paragraph
          variable: recommend_data
        - label: 类型(整租/合租)
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: type
        - label: 找房参数
          max_length: 2048
          options: []
          required: false
          type: paragraph
          variable: room_params
      height: 272
      id: '1741244695210'
      position:
        x: -441.7674325269179
        y: 426.8585871413866
      positionAbsolute:
        x: -441.7674325269179
        y: 426.8585871413866
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '{% if data | length > 0 %}

          因为{{ location }}范围围比较大，小木为您推荐以下热门商圈：

          {% for biz in data %}

          【{{biz.bizcircle_name}}】

          💰房源：{{type}} {{biz.house_count}} 套, 价格区间 {{biz.min_price|int}} - {{biz.max_price|int}}元

          ✅ 优势: {{biz.advantage}}

          {% endfor %}

          {% endif %}


          {% if recommend_data | length > 0 %}

          按您的条件暂未找到匹配房源，小木将您的 {{adjusted_search_params }}条件适当放宽，为您推荐以下热门商圈:

          {% for biz in recommend_data %}

          【{{biz.name}}】 -- 【${biz.label}】

          💰房源：整租 {{biz.whole_rent_count}}, 价格区间 {{biz.price_range[0]}} - {{biz.price_range[1]}}元

          ✅ 优势: {{biz.advantage}}

          {% endfor %}

          {% endif %}


          您看下对哪个商圈比较感兴趣呢？ 小木可以为您推荐商圈内的合适小区'
        title: 商圈总结
        type: template-transform
        variables:
        - value_selector:
          - '1741251528666'
          - data
          variable: data
        - value_selector:
          - '1741244695210'
          - location
          variable: location
        - value_selector:
          - '1741244695210'
          - adjusted_search_params
          variable: adjusted_search_params
        - value_selector:
          - '1741251528666'
          - recommend_data
          variable: recommend_data
        - value_selector:
          - '1741244695210'
          - type
          variable: type
      height: 54
      id: '1741250703991'
      position:
        x: 1140.4818112969804
        y: 325.31518991940266
      positionAbsolute:
        x: 1140.4818112969804
        y: 325.31518991940266
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef safe_parse(s: str):\n    if s and s != \"\" and\
          \ s != \"null\":\n        try:\n            return json.loads(s)\n     \
          \   except:\n            return []\n    return []\n\ndef main(data: str,\
          \ recommend_data: str) -> dict:\n    return {\n        \"data\": json.loads(data),\n\
          \        \"recommend_data\": safe_parse(recommend_data)\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          data:
            children: null
            type: array[object]
          recommend_data:
            children: null
            type: array[object]
        selected: false
        title: 数据json解析
        type: code
        variables:
        - value_selector:
          - '1741244695210'
          - data
          variable: data
        - value_selector:
          - '1741244695210'
          - recommend_data
          variable: recommend_data
      height: 54
      id: '1741251528666'
      position:
        x: 72.14347482410517
        y: 426.8585871413866
      positionAbsolute:
        x: 72.14347482410517
        y: 426.8585871413866
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '小木优先为您推荐以下热门站点，您看下是否感兴趣：


          {% for r in data %}

          ## {{r.subway_station_code}}

          💰租金({{type}})：{{ r.min_price|int }} - {{ r.max_price|int }}元

          待租房源: {{r.house_count}}套

          {% endfor %}'
        title: 地铁站总结
        type: template-transform
        variables:
        - value_selector:
          - '1741251528666'
          - data
          variable: data
        - value_selector:
          - '1741244695210'
          - matched_rooms_total
          variable: matched_rooms_total
        - value_selector:
          - '1741244695210'
          - adjusted_search_params
          variable: adjusted_search_params
        - value_selector:
          - '1741251528666'
          - recommend_data
          variable: recommend_data
        - value_selector:
          - '1741244695210'
          - type
          variable: type
      height: 54
      id: '17413305926220'
      position:
        x: 1140.4818112969804
        y: 788.1365128689022
      positionAbsolute:
        x: 1140.4818112969804
        y: 788.1365128689022
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: dd4bf09a-b9c7-4074-9447-1c5192ab7c4c
            value: ''
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          - comparison_operator: contains
            id: c43bcfc1-c1b3-4547-a3e0-ad95dcee85d9
            value: 城市
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 'true'
          logical_operator: or
        - case_id: c6200137-0051-4cb0-b65f-1dde7706fad3
          conditions:
          - comparison_operator: contains
            id: 8347a8ee-52ba-45e3-8a0c-ffd5d2f9e84b
            value: 行政区
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: c6200137-0051-4cb0-b65f-1dde7706fad3
          logical_operator: and
        - case_id: 656ed1a6-c17a-4eaf-a934-dc95381048d0
          conditions:
          - comparison_operator: contains
            id: ca9ff4e9-2c21-462d-8b1e-67bbb46ac646
            value: 商圈
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 656ed1a6-c17a-4eaf-a934-dc95381048d0
          logical_operator: and
        - case_id: 0984dcbb-b498-4e11-8be9-77f3570c700b
          conditions:
          - comparison_operator: contains
            id: eb646620-de1c-4d8e-ba9c-5aa26129dd61
            value: 地铁线
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 0984dcbb-b498-4e11-8be9-77f3570c700b
          logical_operator: and
        - case_id: 2af62880-f703-41be-b348-932f0445ba3e
          conditions:
          - comparison_operator: contains
            id: 02d28ad0-5de3-40e6-abcc-190ec737629e
            value: 地铁
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 2af62880-f703-41be-b348-932f0445ba3e
          logical_operator: and
        - case_id: 31e087ca-13e0-40c8-97cd-1c2145972b72
          conditions:
          - comparison_operator: contains
            id: 0c082e1d-3444-40ab-af95-9d4649142b30
            value: 小区
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 31e087ca-13e0-40c8-97cd-1c2145972b72
          logical_operator: and
        - case_id: 1ddc92ba-462e-4417-80c9-58d8b17cd0ad
          conditions:
          - comparison_operator: contains
            id: ca5f6aaa-007a-47be-899c-a25e2ce31fbb
            value: 公司
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 1ddc92ba-462e-4417-80c9-58d8b17cd0ad
          logical_operator: and
        - case_id: 4c67a7d1-7e77-4281-b1b8-06903e878ae7
          conditions:
          - comparison_operator: contains
            id: fb59fdfa-cd4a-420b-81b0-fe4531484056
            value: 地点
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 4c67a7d1-7e77-4281-b1b8-06903e878ae7
          logical_operator: and
        - case_id: 11792e2f-2088-42fb-89c5-02634a08fabc
          conditions:
          - comparison_operator: contains
            id: 4c0b22d0-f476-46d2-8c71-e41ddc1b8544
            value: 写字楼
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: 11792e2f-2088-42fb-89c5-02634a08fabc
          logical_operator: and
        - case_id: f1bda551-b3d8-4d0b-bd7e-fa0624fd9557
          conditions:
          - comparison_operator: contains
            id: b33589d5-dcd4-44af-8cd0-bd9c2791b70d
            value: 商场
            varType: string
            variable_selector:
            - '1741244695210'
            - location_type
          id: f1bda551-b3d8-4d0b-bd7e-fa0624fd9557
          logical_operator: and
        desc: ''
        selected: true
        title: 条件分支
        type: if-else
      height: 584
      id: '1741330681785'
      position:
        x: 577.7503206465151
        y: 426.8585871413866
      positionAbsolute:
        x: 577.7503206465151
        y: 426.8585871413866
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17413305926220'
          - output
          variable: output
        - value_selector: []
          variable: ''
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1741332693003'
      position:
        x: 1666.3129601309297
        y: 788.1365128689022
      positionAbsolute:
        x: 1666.3129601309297
        y: 788.1365128689022
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1741250703991'
          - output
          variable: output
        selected: false
        title: 结束 4
        type: end
      height: 90
      id: '1741332699941'
      position:
        x: 1660.4980682254218
        y: 325.31518991940266
      positionAbsolute:
        x: 1660.4980682254218
        y: 325.31518991940266
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '好的，为您找到{{matched_rooms_total}}，本次为您推荐以下{{ data | length }}套



          {% for r in data %}

          ### {{r.name_v2}}

          ![]({{r.photo}})

          [点击查看](https://www.ziroom.com/x/{{r.inv_no}}.html)


          {{r.room_intro}}

          {% if r.subway_station_info %}

          📍{{r.subway_station_info}}

          {% endif %}

          ¥ {{r.price}}{{r.price_unit}}

          {%for t in r.tags %}{{t.title}} {% endfor %}



          {% endfor %}



          {% if recommend_data | length > 0 %}

          ----

          按您的条件匹配到的房源较少，小木将您的{{ adjusted_search_params}}条件适当放宽，您也可以看看是否符合要求

          {% for r in recommend_data %}



          ### {{r.name_v2}}

          ![]({{r.photo}})



          {{r.room_intro}}

          {% if r.subway_station_info %}

          📍{{r.subway_station_info}}

          {% endif %}

          ¥ {{r.price}}{{r.price_unit}}

          {%for t in r.tags %}{{t.title}} {% endfor %}

          {% endfor %}

          {% endif %}



          您看对哪套房源比较感兴趣，可以去现场看房哦'
        title: 房源总结
        type: template-transform
        variables:
        - value_selector:
          - '1741244695210'
          - matched_rooms_total
          variable: matched_rooms_total
        - value_selector:
          - '1741251528666'
          - data
          variable: data
        - value_selector:
          - '1741251528666'
          - recommend_data
          variable: recommend_data
      height: 54
      id: '1741343544159'
      position:
        x: 1140.4818112969804
        y: 1050.7315949588465
      positionAbsolute:
        x: 1140.4818112969804
        y: 1050.7315949588465
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1741343544159'
          - output
          variable: output
        selected: false
        title: 结束 5
        type: end
      height: 90
      id: '1741342505499'
      position:
        x: 1653.1668983748825
        y: 1050.7315949588465
      positionAbsolute:
        x: 1653.1668983748825
        y: 1050.7315949588465
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: '好的，为您找到附近热门小区，本次为您推荐以下{{ data | length }}个


          {% for r in data %}

          ### 📍{{r.resblock_name}}


          👀小区概览![]({{r.pic_info}})


          🏠当前有{{r.house_count}}套房子出租


          💰平均租金在{{r.min_price}}～{{r.max_price}}


          {% endfor %}



          您看对哪个小区比较感兴趣，可以去现场看房哦'
        title: 小区总结
        type: template-transform
        variables:
        - value_selector:
          - '1741251528666'
          - data
          variable: data
        - value_selector:
          - '1741244695210'
          - matched_rooms_total
          variable: matched_rooms_total
        - value_selector:
          - '1741244695210'
          - adjusted_search_params
          variable: adjusted_search_params
        - value_selector:
          - '1741251528666'
          - recommend_data
          variable: recommend_data
      height: 54
      id: '17422644481720'
      position:
        x: 1140.4818112969804
        y: 578.44020829115
      positionAbsolute:
        x: 1140.4818112969804
        y: 578.44020829115
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17422644481720'
          - output
          variable: output
        selected: false
        title: '结束 3 '
        type: end
      height: 90
      id: '17422644574460'
      position:
        x: 1666.3129601309297
        y: 578.44020829115
      positionAbsolute:
        x: 1666.3129601309297
        y: 578.44020829115
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -13.442758450748897
      y: 53.48118766693926
      zoom: 0.5159167270432775
