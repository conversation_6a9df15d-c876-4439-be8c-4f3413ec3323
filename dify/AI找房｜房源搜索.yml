app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: AI找房｜房源搜索
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 17414170450540-source-17414178378350-target
      selected: false
      source: '17414170450540'
      sourceHandle: source
      target: '17414178378350'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-ad4a6fe3-fc7a-4da9-83e5-f7663ee613b5-17414179364500-target
      selected: false
      source: '17414178378350'
      sourceHandle: ad4a6fe3-fc7a-4da9-83e5-f7663ee613b5
      target: '17414179364500'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-9c845e88-8600-41df-b106-a74bd8ba4318-17414179333060-target
      selected: false
      source: '17414178378350'
      sourceHandle: 9c845e88-8600-41df-b106-a74bd8ba4318
      target: '17414179333060'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: code
      id: 17414179364500-source-17414180343140-target
      selected: false
      source: '17414179364500'
      sourceHandle: source
      target: '17414180343140'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: code
      id: 17414179333060-source-17414180343140-target
      selected: false
      source: '17414179333060'
      sourceHandle: source
      target: '17414180343140'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 17414180504240-source-17414180589540-target
      selected: false
      source: '17414180504240'
      sourceHandle: source
      target: '17414180589540'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: 17414180589540-source-17414180680570-target
      selected: false
      source: '17414180589540'
      sourceHandle: source
      target: '17414180680570'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 1741429577014-true-1741429590698-target
      selected: false
      source: '1741429577014'
      sourceHandle: 'true'
      target: '1741429590698'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: code
      id: 1741429590698-source-17414296980080-target
      selected: false
      source: '1741429590698'
      sourceHandle: source
      target: '17414296980080'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: tool
      id: 17414296980080-source-17414297124090-target
      selected: false
      source: '17414296980080'
      sourceHandle: source
      target: '17414297124090'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 17414297124090-source-17414297241200-target
      selected: false
      source: '17414297124090'
      sourceHandle: source
      target: '17414297241200'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: 17414297241200-source-17414297304490-target
      selected: false
      source: '17414297241200'
      sourceHandle: source
      target: '17414297304490'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-79e83a22-6c14-4d6f-91ab-24964870b830-17415715487970-target
      selected: false
      source: '17414178378350'
      sourceHandle: 79e83a22-6c14-4d6f-91ab-24964870b830
      target: '17415715487970'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: code
      id: 17415715487970-source-17415715705150-target
      selected: false
      source: '17415715487970'
      sourceHandle: source
      target: '17415715705150'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: tool
      id: 17415715705150-source-17415718866720-target
      selected: false
      source: '17415715705150'
      sourceHandle: source
      target: '17415718866720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 17415718866720-source-17415718891620-target
      selected: false
      source: '17415718866720'
      sourceHandle: source
      target: '17415718891620'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: 17415718891620-source-17415718915040-target
      selected: false
      source: '17415718891620'
      sourceHandle: source
      target: '17415718915040'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-414ed828-ceb7-4a85-bc25-6f60810aa8f5-17415896702830-target
      selected: false
      source: '17414178378350'
      sourceHandle: 414ed828-ceb7-4a85-bc25-6f60810aa8f5
      target: '17415896702830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: code
      id: 17415896702830-source-17415715705150-target
      selected: false
      source: '17415896702830'
      sourceHandle: source
      target: '17415715705150'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: start
        targetType: code
      id: 1741167267535-source-1741591033838-target
      selected: false
      source: '1741167267535'
      sourceHandle: source
      target: '1741591033838'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 1741408090809-source-17415983202450-target
      selected: false
      source: '1741408090809'
      sourceHandle: source
      target: '17415983202450'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 17415983202450-source-17415983371300-target
      selected: false
      source: '17415983202450'
      sourceHandle: source
      target: '17415983371300'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: code
      id: 17415983371300-true-17414170450540-target
      selected: false
      source: '17415983371300'
      sourceHandle: 'true'
      target: '17414170450540'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: end
      id: 17415983371300-false-17414183084700-target
      selected: false
      source: '17415983371300'
      sourceHandle: 'false'
      target: '17414183084700'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: end
      id: 17414178378350-false-1741599404703-target
      selected: false
      source: '17414178378350'
      sourceHandle: 'false'
      target: '1741599404703'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 1741591033838-source-1741167811839-target
      selected: false
      source: '1741591033838'
      sourceHandle: source
      target: '1741167811839'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: tool
      id: 1741167811839-54bbf694-efea-43d1-8d1b-7f673c01a534-1741408090809-target
      selected: false
      source: '1741167811839'
      sourceHandle: 54bbf694-efea-43d1-8d1b-7f673c01a534
      target: '1741408090809'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 1741591033838-source-1741429577014-target
      selected: false
      source: '1741591033838'
      sourceHandle: source
      target: '1741429577014'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: tool
      id: 17414180343140-source-17414180504240-target
      selected: false
      source: '17414180343140'
      sourceHandle: source
      target: '17414180504240'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-1a7af28b-87c5-461a-a9f9-51c8c61ae027-17422066519480-target
      selected: false
      source: '17414178378350'
      sourceHandle: 1a7af28b-87c5-461a-a9f9-51c8c61ae027
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-c2fe5e4c-63f7-450d-a701-4e0401ce80e4-17422066519480-target
      selected: false
      source: '17414178378350'
      sourceHandle: c2fe5e4c-63f7-450d-a701-4e0401ce80e4
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-d7765d76-0c77-4a34-9605-6b8cb6d66908-17422066519480-target
      selected: false
      source: '17414178378350'
      sourceHandle: d7765d76-0c77-4a34-9605-6b8cb6d66908
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-1b551ee6-bb8a-4ddf-a3b8-0644e71fa424-17422066519480-target
      selected: false
      source: '17414178378350'
      sourceHandle: 1b551ee6-bb8a-4ddf-a3b8-0644e71fa424
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-d9052fb1-69d6-40d7-a844-06dda0866eff-17422066519480-target
      selected: false
      source: '17414178378350'
      sourceHandle: d9052fb1-69d6-40d7-a844-06dda0866eff
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-f9556c84-5247-4737-b2ce-91fff95e1034-17422066519480-target
      selected: false
      source: '17414178378350'
      sourceHandle: f9556c84-5247-4737-b2ce-91fff95e1034
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: http-request
        targetType: code
      id: 17422066519480-source-17422634016490-target
      source: '17422066519480'
      sourceHandle: source
      target: '17422634016490'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 17422634173890-source-17422634245000-target
      source: '17422634173890'
      sourceHandle: source
      target: '17422634245000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: 17422634245000-source-17422634294050-target
      source: '17422634245000'
      sourceHandle: source
      target: '17422634294050'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: http-request
      id: 17414178378350-true-17422066519480-target
      source: '17414178378350'
      sourceHandle: 'true'
      target: '17422066519480'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 17422634016490-source-1742804469564-target
      source: '17422634016490'
      sourceHandle: source
      target: '1742804469564'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: tool
      id: 1742804469564-true-17422634173890-target
      source: '1742804469564'
      sourceHandle: 'true'
      target: '17422634173890'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: end
      id: 1742804469564-false-1742804529577-target
      source: '1742804469564'
      sourceHandle: 'false'
      target: '1742804529577'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 位置信息
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: location
        - label: 位置类型
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: location_type
        - label: 价格区间
          max_length: 128
          options: []
          required: false
          type: text-input
          variable: price
        - label: 租住类型
          max_length: 10
          options: []
          required: false
          type: text-input
          variable: type
        - label: 租约类型
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: leasetype
        - label: 居室
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: bedroom
        - label: 朝向
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: face
        - label: 面积区间
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: area
        - label: 入住人数
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: checkin_people_num
        - label: 预计可入住日期
          max_length: 128
          options: []
          required: false
          type: text-input
          variable: checkin_date
        - label: 是否首次出租
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: is_first_rent
        - label: 价格排序规则
          max_length: 128
          options: []
          required: false
          type: text-input
          variable: sort
        - label: 通勤时间
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: minute
        - label: 分页
          max_length: 48
          options: []
          required: false
          type: number
          variable: page
        - label: 距离
          max_length: 48
          options: []
          required: false
          type: number
          variable: distance
        - label: 通勤方式
          max_length: 128
          options: []
          required: false
          type: text-input
          variable: transport
      height: 480
      id: '1741167267535'
      position:
        x: 30
        y: 517
      positionAbsolute:
        x: 30
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 54bbf694-efea-43d1-8d1b-7f673c01a534
          conditions:
          - comparison_operator: not empty
            id: 7520e3a3-24d2-4e04-baba-97fe18a24f5a
            value: ''
            varType: string
            variable_selector:
            - '1741591033838'
            - location
          id: 54bbf694-efea-43d1-8d1b-7f673c01a534
          logical_operator: and
        desc: ''
        selected: false
        title: 地点推荐
        type: if-else
      height: 126
      id: '1741167811839'
      position:
        x: 638
        y: 517
      positionAbsolute:
        x: 638
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: 03e6426d-414c-4c86-a42c-dfe7fe4c299c
        provider_name: 自如APP房源关键字搜索位置接口-二次封装
        provider_type: api
        selected: false
        title: 按精确location_type查找
        tool_configurations: {}
        tool_label: searchSug
        tool_name: searchSug
        tool_parameters:
          city_code:
            type: mixed
            value: '110000'
          keyword:
            type: mixed
            value: '{{#1741591033838.location#}}'
          sug_type_name:
            type: mixed
            value: '{{#1741591033838.location_type#}}'
        type: tool
      height: 54
      id: '1741408090809'
      position:
        x: 942
        y: 517
      positionAbsolute:
        x: 942
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\ndef main(type1: str) -> dict:\n    type_i = 1\n    if type1 == \"\
          整租\":\n        type_i = 2\n\n    return {\n        \"type_i\": type_i,\n\
          \    }\n"
        code_language: python3
        desc: ''
        outputs:
          type_i:
            children: null
            type: number
        selected: false
        title: 房源召回参数清洗租住类型
        type: code
        variables:
        - value_selector:
          - '1741591033838'
          - type
          variable: type1
      height: 54
      id: '17414170450540'
      position:
        x: 1854
        y: 517
      positionAbsolute:
        x: 1854
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: 85250ab7-bd92-4dc8-afdb-698d3b279f15
            value: 商圈
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: 'true'
          logical_operator: or
        - case_id: 79e83a22-6c14-4d6f-91ab-24964870b830
          conditions:
          - comparison_operator: contains
            id: 82a77adb-1532-438d-999a-30d76db0836f
            value: 地铁线
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: 79e83a22-6c14-4d6f-91ab-24964870b830
          logical_operator: and
        - case_id: ad4a6fe3-fc7a-4da9-83e5-f7663ee613b5
          conditions:
          - comparison_operator: contains
            id: fb57bf8f-7756-466a-b574-eedd53c6e73a
            value: 小区
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: ad4a6fe3-fc7a-4da9-83e5-f7663ee613b5
          logical_operator: and
        - case_id: 1a7af28b-87c5-461a-a9f9-51c8c61ae027
          conditions:
          - comparison_operator: contains
            id: b8a9cb1a-0fa5-43be-b22e-bdc3c0588a36
            value: 地铁
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: 1a7af28b-87c5-461a-a9f9-51c8c61ae027
          logical_operator: and
        - case_id: f9556c84-5247-4737-b2ce-91fff95e1034
          conditions:
          - comparison_operator: contains
            id: d49b9e23-05cb-4b35-816f-e09703c9a6f5
            value: 公司
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: f9556c84-5247-4737-b2ce-91fff95e1034
          logical_operator: and
        - case_id: 414ed828-ceb7-4a85-bc25-6f60810aa8f5
          conditions:
          - comparison_operator: contains
            id: 940660eb-d50e-443c-931f-8837023f71dd
            value: 行政区
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: 414ed828-ceb7-4a85-bc25-6f60810aa8f5
          logical_operator: and
        - case_id: 9c845e88-8600-41df-b106-a74bd8ba4318
          conditions:
          - comparison_operator: contains
            id: 8df16af5-24e8-4149-a1db-fd620e9e1ec9
            value: 自如寓
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: 9c845e88-8600-41df-b106-a74bd8ba4318
          logical_operator: and
        - case_id: d9052fb1-69d6-40d7-a844-06dda0866eff
          conditions:
          - comparison_operator: contains
            id: c3426dc5-f437-46d8-8835-adc306e5001f
            value: 学校
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: d9052fb1-69d6-40d7-a844-06dda0866eff
          logical_operator: and
        - case_id: c2fe5e4c-63f7-450d-a701-4e0401ce80e4
          conditions:
          - comparison_operator: contains
            id: 68c056cf-dd2b-4c86-8378-0642ed25956b
            value: 地点
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: c2fe5e4c-63f7-450d-a701-4e0401ce80e4
          logical_operator: and
        - case_id: 1b551ee6-bb8a-4ddf-a3b8-0644e71fa424
          conditions:
          - comparison_operator: contains
            id: afba9308-87bc-4c4e-a927-9b0908dd7bc0
            value: 写字楼
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: 1b551ee6-bb8a-4ddf-a3b8-0644e71fa424
          logical_operator: and
        - case_id: d7765d76-0c77-4a34-9605-6b8cb6d66908
          conditions:
          - comparison_operator: contains
            id: 015b4769-ce8e-4af6-b73a-1d9c1b1de5b6
            value: 商场
            varType: string
            variable_selector:
            - '17415983202450'
            - type
          id: d7765d76-0c77-4a34-9605-6b8cb6d66908
          logical_operator: and
        desc: 判断类型
        selected: false
        title: 条件分支 4
        type: if-else
      height: 634
      id: '17414178378350'
      position:
        x: 2158
        y: 517
      positionAbsolute:
        x: 2158
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: '{

              "city_code":"110000",

              "location_type":"{{#17415983202450.type#}}",

              "location_value":"{{#17415983202450.value#}}",

              "price":"{{#1741167267535.price#}}",

              "type":"{{#17414170450540.type_i#}}",

              "bedroom":"{{#1741591033838.bedroom#}}",

              "leasetype":"{{#1741591033838.leasetype#}}",

              "area":"{{#1741167267535.area#}}",

              "checkin_people_num":"{{#1741167267535.checkin_people_num#}}",

              "face":"{{#1741591033838.face#}}",

              "checkin_date":"{{#1741167267535.checkin_date#}}",

              "is_first_rent":"{{#1741167267535.is_first_rent#}}",

              "sug_type": "{{#17415983202450.tj_type#}}",

              "distance":{{#1741591033838.distance#}},

              "transport":"{{#1741167267535.transport#}}",

              "minute":"{{#1741167267535.minute#}}",

              "page":{{#1741591033838.page#}},

              "size":3

              }'
          type: raw-text
        desc: ''
        headers: Content-Type:application/json
        method: post
        params: ''
        retry_config:
          max_retries: '2'
          retry_enabled: true
          retry_interval: 100
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 查询房源
        type: http-request
        url: http://ai-finder-bff.uat.ziroom.com/rooms/
        variables: []
      height: 135
      id: '17414179333060'
      position:
        x: 2462
        y: 692
      positionAbsolute:
        x: 2462
        y: 692
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: '{

              "city_code":"110000",

              "location_type":"楼盘",

              "location_value":"{{#17415983202450.value#}}",

              "type":"{{#17414170450540.type_i#}}",

              "price":"{{#1741167267535.price#}}",

              "area":"{{#1741167267535.area#}}",

              "bedroom":"{{#1741591033838.bedroom#}}",

              "leasetype":"{{#1741591033838.leasetype#}}",

              "checkin_people_num":"{{#1741167267535.checkin_people_num#}}",

              "face":"{{#1741591033838.face#}}",

              "checkin_date":"{{#1741167267535.checkin_date#}}",

              "is_first_rent":"{{#1741167267535.is_first_rent#}}",

              "sug_type": "{{#17415983202450.tj_type#}}",

              "distance":{{#1741591033838.distance#}},

              "transport":"{{#1741167267535.transport#}}",

              "minute":"{{#1741167267535.minute#}}",

              "page":{{#1741591033838.page#}},

              "size":3

              }'
          type: raw-text
        desc: ''
        headers: Content-Type:application/json
        method: post
        params: ''
        retry_config:
          max_retries: '2'
          retry_enabled: true
          retry_interval: 100
        selected: true
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 查询小区/楼盘附近房源
        type: http-request
        url: http://ai-finder-bff.uat.ziroom.com/rooms/
        variables: []
      height: 135
      id: '17414179364500'
      position:
        x: 2462
        y: 517
      positionAbsolute:
        x: 2462
        y: 517
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\n\ndef main(arg1: str, arg2: str) -> dict:\n    if arg1:\n\
          \        resp = json.loads(arg1)\n    elif arg2:\n        resp = json.loads(arg2)\n\
          \        \n    return {\n        \"data\": json.dumps(resp[\"data\"][\"\
          rooms\"]),\n        \"tj_data\": json.dumps(resp[\"data\"][\"promotionRooms\"\
          ]),\n        \"summary\": resp[\"data\"][\"summary\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          data:
            children: null
            type: string
          summary:
            children: null
            type: string
          tj_data:
            children: null
            type: string
        selected: false
        title: 解析返回数据
        type: code
        variables:
        - value_selector:
          - '17414179364500'
          - body
          variable: arg1
        - value_selector:
          - '17414179333060'
          - body
          variable: arg2
      height: 54
      id: '17414180343140'
      position:
        x: 2766
        y: 517
      positionAbsolute:
        x: 2766
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: a496e242-15cc-4a4f-82da-cfb865830f3e
        provider_name: AI找房 | 信息总结
        provider_type: workflow
        selected: false
        title: AI找房 | 信息总结
        tool_configurations: {}
        tool_label: AI找房 | 信息总结
        tool_name: ai_room_summary
        tool_parameters:
          adjusted_search_params:
            type: mixed
            value: ''
          data:
            type: mixed
            value: '{{#17414180343140.data#}}'
          location:
            type: mixed
            value: '{{#1741591033838.location#}}'
          location_type:
            type: variable
            value:
            - '17415983202450'
            - type
          matched_rooms_total:
            type: constant
            value: '{{#17414180343140.summary#}}'
          recommend_data:
            type: mixed
            value: '{{#17414180343140.tj_data#}}'
          room_params:
            type: mixed
            value: ''
          type:
            type: mixed
            value: '{{#1741591033838.type#}}'
        type: tool
      height: 54
      id: '17414180504240'
      position:
        x: 3070
        y: 517
      positionAbsolute:
        x: 3070
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(arg1: str) -> dict:    \n    return {\n   \
          \     \"result\": json.loads(arg1)[\"output\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 转换结果
        type: code
        variables:
        - value_selector:
          - '17414180504240'
          - text
          variable: arg1
      height: 54
      id: '17414180589540'
      position:
        x: 3374
        y: 517
      positionAbsolute:
        x: 3374
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17414180589540'
          - result
          variable: text
        selected: false
        title: 结束
        type: end
      height: 90
      id: '17414180680570'
      position:
        x: 3678
        y: 517
      positionAbsolute:
        x: 3678
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 无结果
        outputs: []
        selected: false
        title: 结束
        type: end
      height: 82
      id: '17414183084700'
      position:
        x: 1854
        y: 797
      positionAbsolute:
        x: 1854
        y: 797
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 50ca5198-253f-4754-b3f8-6e7aa33af7e0
            value: ''
            varType: string
            variable_selector:
            - '1741591033838'
            - location
          - comparison_operator: empty
            id: e46f32c5-c8d1-40c0-93f3-5834d4596e69
            value: ''
            varType: string
            variable_selector:
            - '1741591033838'
            - location_type
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 热门商圈推荐
        type: if-else
      height: 152
      id: '1741429577014'
      position:
        x: 638
        y: 906
      positionAbsolute:
        x: 638
        y: 906
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: ''
          type: none
        desc: ''
        headers: ''
        method: get
        params: city_code:110000
        retry_config:
          max_retries: '2'
          retry_enabled: true
          retry_interval: 100
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 获取城市热门商圈
        type: http-request
        url: http://ai-finder-bff.uat.ziroom.com/bizcircles/
        variables: []
      height: 135
      id: '1741429590698'
      position:
        x: 942
        y: 878.5
      positionAbsolute:
        x: 942
        y: 878.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\n\ndef main(arg1: str) -> dict:\n    resp = json.loads(arg1)\n\
          \n    return {\n        \"data\": json.dumps(resp[\"data\"])\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          data:
            children: null
            type: string
        selected: false
        title: '解析返回数据 '
        type: code
        variables:
        - value_selector:
          - '1741429590698'
          - body
          variable: arg1
      height: 54
      id: '17414296980080'
      position:
        x: 1246
        y: 933
      positionAbsolute:
        x: 1246
        y: 933
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: a496e242-15cc-4a4f-82da-cfb865830f3e
        provider_name: AI找房 | 信息总结
        provider_type: workflow
        selected: false
        title: AI找房 | 信息总结
        tool_configurations: {}
        tool_label: AI找房 | 信息总结
        tool_name: ai_room_summary
        tool_parameters:
          adjusted_search_params:
            type: mixed
            value: ''
          data:
            type: mixed
            value: '{{#17414296980080.data#}}'
          location:
            type: mixed
            value: 北京
          location_type:
            type: constant
            value: 城市
          matched_rooms_total:
            type: constant
            value: '1'
          recommend_data:
            type: mixed
            value: ''
          type:
            type: mixed
            value: '{{#1741591033838.type#}}'
        type: tool
      height: 54
      id: '17414297124090'
      position:
        x: 1550
        y: 969
      positionAbsolute:
        x: 1550
        y: 969
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(arg1: str) -> dict:    \n    return {\n   \
          \     \"result\": json.loads(arg1)[\"output\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 转换结果
        type: code
        variables:
        - value_selector:
          - '17414297124090'
          - text
          variable: arg1
      height: 54
      id: '17414297241200'
      position:
        x: 1854
        y: 919
      positionAbsolute:
        x: 1854
        y: 919
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17414297241200'
          - result
          variable: text
        selected: false
        title: 结束
        type: end
      height: 90
      id: '17414297304490'
      position:
        x: 2158
        y: 1191
      positionAbsolute:
        x: 2158
        y: 1191
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: ''
          type: none
        desc: ''
        headers: ''
        method: get
        params: 'city_code:110000

          subway_line:{{#17415983202450.value#}}

          product_category:{{#17414170450540.type_i#}}'
        retry_config:
          max_retries: '2'
          retry_enabled: true
          retry_interval: 100
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 查询地铁线附近房源
        type: http-request
        url: http://ai-finder-bff.uat.ziroom.com/subway/subwayStations
        variables: []
      height: 151
      id: '17415715487970'
      position:
        x: 2462
        y: 867
      positionAbsolute:
        x: 2462
        y: 867
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\n\ndef main(arg1: str, arg2: str) -> dict:\n    if arg1:\n\
          \        resp = json.loads(arg1)\n    elif arg2:\n        resp = json.loads(arg2)\n\
          \n    return {\n        \"data\": json.dumps(resp[\"data\"])\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          data:
            children: null
            type: string
        selected: false
        title: '解析返回数据 '
        type: code
        variables:
        - value_selector:
          - '17415715487970'
          - body
          variable: arg1
        - value_selector:
          - '17415896702830'
          - body
          variable: arg2
      height: 54
      id: '17415715705150'
      position:
        x: 2766
        y: 875
      positionAbsolute:
        x: 2766
        y: 875
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: a496e242-15cc-4a4f-82da-cfb865830f3e
        provider_name: AI找房 | 信息总结
        provider_type: workflow
        selected: false
        title: AI找房 | 信息总结 ｜地铁
        tool_configurations: {}
        tool_label: AI找房 | 信息总结
        tool_name: ai_room_summary
        tool_parameters:
          adjusted_search_params:
            type: mixed
            value: ''
          data:
            type: mixed
            value: '{{#17415715705150.data#}}'
          location:
            type: mixed
            value: '{{#1741591033838.location#}}'
          location_type:
            type: variable
            value:
            - '17415983202450'
            - type
          matched_rooms_total:
            type: constant
            value: '1'
          recommend_data:
            type: mixed
            value: ''
          type:
            type: mixed
            value: '{{#1741591033838.type#}}'
        type: tool
      height: 54
      id: '17415718866720'
      position:
        x: 3070
        y: 875
      positionAbsolute:
        x: 3070
        y: 875
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(arg1: str) -> dict:    \n    return {\n   \
          \     \"result\": json.loads(arg1)[\"output\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 转换结果
        type: code
        variables:
        - value_selector:
          - '17415718866720'
          - text
          variable: arg1
      height: 54
      id: '17415718891620'
      position:
        x: 3374
        y: 875
      positionAbsolute:
        x: 3374
        y: 875
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17415718891620'
          - result
          variable: text
        selected: false
        title: '结束 '
        type: end
      height: 90
      id: '17415718915040'
      position:
        x: 3678
        y: 875
      positionAbsolute:
        x: 3678
        y: 875
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: ''
          type: none
        desc: ''
        headers: ''
        method: get
        params: 'city_code:110000

          district_code:{{#17415983202450.value#}}

          product_category:{{#17414170450540.type_i#}}'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 100
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 获取行政区附近房源
        type: http-request
        url: http://ai-finder-bff.uat.ziroom.com/bizcircles/
        variables: []
      height: 135
      id: '17415896702830'
      position:
        x: 2462
        y: 1058
      positionAbsolute:
        x: 2462
        y: 1058
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import numbers\n\ndef main(arg1: str, arg2: str, arg3: str,arg5: str,\
          \ arg6: str, arg7: str, arg9: str, arg4: numbers.Number = 1,arg8: numbers.Number\
          \ = 3) -> dict:\n    \n    arg1 = \"\" if arg1 in (None, \"null\") else\
          \ arg1\n    arg2 = \"\" if arg2 in (None, \"null\") else arg2\n    arg3\
          \ = \"\" if arg3 in (None, \"null\") else arg3\n    arg5 = \"\" if arg5\
          \ in (None, \"null\") else arg5\n    arg6 = \"\" if arg6 in (None, \"null\"\
          ) else arg6\n    arg7 = \"\" if arg7 in (None, \"null\") else arg7\n   \
          \ arg9 = \"\" if arg9 in (None, \"null\") else arg9\n\n    arg1 = arg1.split(\"\
          ,\")[0] if \",\" in arg1 else arg1\n    arg2 = arg2.split(\",\")[0] if \"\
          ,\" in arg2 else arg2\n    arg3 = arg3.split(\",\")[0] if \",\" in arg3\
          \ else arg3\n    arg5 = arg5.split(\",\")[0] if \",\" in arg5 else arg5\n\
          \    arg6 = arg6.split(\",\")[0] if \",\" in arg6 else arg6\n    arg7 =\
          \ arg7.split(\",\")[0] if \",\" in arg7 else arg7\n\n    if arg1 == \"北京\"\
          :\n        arg1 = \"\"\n    if not arg4:\n        arg4 = 1\n    if not arg8:\n\
          \        arg8 = 3\n    if arg9 and not arg3:\n        prices = list(map(int,\
          \ arg9.split(\",\")))\n        max_price = max(prices)\n        if max_price\
          \ > 7000:\n            arg3 = \"整租\"\n    \n    return {\n        \"location\"\
          : arg1,\n        \"location_type\": arg2,\n        \"type\": arg3,\n   \
          \     \"page\": arg4,\n        \"bedroom\": arg5,\n        \"face\": arg6,\n\
          \        \"leasetype\": arg7,\n        \"distance\":arg8\n    }"
        code_language: python3
        desc: ''
        outputs:
          bedroom:
            children: null
            type: string
          distance:
            children: null
            type: number
          face:
            children: null
            type: string
          leasetype:
            children: null
            type: string
          location:
            children: null
            type: string
          location_type:
            children: null
            type: string
          page:
            children: null
            type: number
          type:
            children: null
            type: string
        selected: false
        title: 参数清洗
        type: code
        variables:
        - value_selector:
          - '1741167267535'
          - location
          variable: arg1
        - value_selector:
          - '1741167267535'
          - location_type
          variable: arg2
        - value_selector:
          - '1741167267535'
          - type
          variable: arg3
        - value_selector:
          - '1741167267535'
          - page
          variable: arg4
        - value_selector:
          - '1741167267535'
          - bedroom
          variable: arg5
        - value_selector:
          - '1741167267535'
          - face
          variable: arg6
        - value_selector:
          - '1741167267535'
          - leasetype
          variable: arg7
        - value_selector:
          - '1741167267535'
          - distance
          variable: arg8
        - value_selector:
          - '1741167267535'
          - price
          variable: arg9
      height: 54
      id: '1741591033838'
      position:
        x: 334
        y: 517
      positionAbsolute:
        x: 334
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(arg1: str) -> dict:\n    result = \"1\"\n \
          \   resp = json.loads(arg1)\n    data = resp[\"data\"]\n\n    if len(data.get(\"\
          items\", [])) == 0:\n        result = \"0\"\n    else:\n        items =\
          \ data.get(\"items\")\n        item = items[0]\n    \n    return {\n   \
          \     \"type\": item[\"type_name\"] if result == \"1\" else \"\",\n    \
          \    \"value\": item[\"value\"] if result == \n        \"1\" else \"\",\n\
          \        \"tj_type\": item[\"type\"] if result == \n        \"1\" else \"\
          \",\n        \"result\": result\n    }"
        code_language: python3
        desc: py
        outputs:
          result:
            children: null
            type: string
          tj_type:
            children: null
            type: number
          type:
            children: null
            type: string
          value:
            children: null
            type: string
        selected: false
        title: 位置实体参数提取器
        type: code
        variables:
        - value_selector:
          - '1741408090809'
          - text
          variable: arg1
      height: 82
      id: '17415983202450'
      position:
        x: 1246
        y: 517
      positionAbsolute:
        x: 1246
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is
            id: c36d6653-15ca-4315-84f4-c3dd4d8e5c71
            value: '1'
            varType: string
            variable_selector:
            - '17415983202450'
            - result
          id: 'true'
          logical_operator: and
        desc: 判断关键字查询是否有结果
        selected: false
        title: 判断是否有查询结果
        type: if-else
      height: 154
      id: '17415983371300'
      position:
        x: 1550
        y: 517
      positionAbsolute:
        x: 1550
        y: 517
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 无结果
        outputs: []
        selected: false
        title: 结束 9
        type: end
      height: 82
      id: '1741599404703'
      position:
        x: 2462
        y: 1233
      positionAbsolute:
        x: 2462
        y: 1233
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: ''
          type: none
        desc: ''
        headers: ''
        method: get
        params: 'city_code:110000

          type:{{#17414170450540.type_i#}}

          location_value:{{#17415983202450.value#}}

          location_type:{{#17415983202450.type#}}

          sug_type:{{#17415983202450.tj_type#}}

          price:{{#1741167267535.price#}}'
        retry_config:
          max_retries: '2'
          retry_enabled: true
          retry_interval: 100
        selected: false
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 查询附近小区
        type: http-request
        url: http://ai-finder-bff.uat.ziroom.com/resblocks
        variables: []
      height: 135
      id: '17422066519480'
      position:
        x: 2462
        y: 1355
      positionAbsolute:
        x: 2462
        y: 1355
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\n\ndef main(arg3:str) -> dict:\n    if arg3:\n      \
          \  resp = json.loads(arg3)\n\n    return {\n        \"data\": json.dumps(resp[\"\
          data\"])\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          data:
            children: null
            type: string
        selected: false
        title: 解析返回数据
        type: code
        variables:
        - value_selector:
          - '17422066519480'
          - body
          variable: arg3
      height: 54
      id: '17422634016490'
      position:
        x: 2766
        y: 1355
      positionAbsolute:
        x: 2766
        y: 1355
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: a496e242-15cc-4a4f-82da-cfb865830f3e
        provider_name: AI找房 | 信息总结
        provider_type: workflow
        selected: false
        title: AI找房 | 信息总结 ｜小区
        tool_configurations: {}
        tool_label: AI找房 | 信息总结
        tool_name: ai_room_summary
        tool_parameters:
          adjusted_search_params:
            type: mixed
            value: ''
          data:
            type: mixed
            value: '{{#17422634016490.data#}}'
          location:
            type: mixed
            value: '{{#1741591033838.location#}}'
          location_type:
            type: variable
            value:
            - '17415983202450'
            - type
          matched_rooms_total:
            type: constant
            value: '1'
          recommend_data:
            type: mixed
            value: ''
          type:
            type: mixed
            value: '{{#1741591033838.type#}}'
        type: tool
      height: 54
      id: '17422634173890'
      position:
        x: 3374
        y: 1355
      positionAbsolute:
        x: 3374
        y: 1355
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(arg1: str) -> dict:    \n    return {\n   \
          \     \"result\": json.loads(arg1)[\"output\"]\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 转换结果
        type: code
        variables:
        - value_selector:
          - '17422634173890'
          - text
          variable: arg1
      height: 54
      id: '17422634245000'
      position:
        x: 3678
        y: 1373
      positionAbsolute:
        x: 3678
        y: 1373
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '17422634245000'
          - result
          variable: text
        selected: false
        title: '结束 '
        type: end
      height: 90
      id: '17422634294050'
      position:
        x: 3982
        y: 1355
      positionAbsolute:
        x: 3982
        y: 1355
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is not
            id: 14b64e5b-a056-4aff-bfaf-2d85d32185bb
            value: 'null'
            varType: string
            variable_selector:
            - '17422634016490'
            - data
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 5
        type: if-else
      height: 126
      id: '1742804469564'
      position:
        x: 3070
        y: 1319
      positionAbsolute:
        x: 3070
        y: 1319
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 无结果
        outputs: []
        selected: false
        title: 结束 7
        type: end
      height: 82
      id: '1742804529577'
      position:
        x: 3374
        y: 1449
      positionAbsolute:
        x: 3374
        y: 1449
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -1217.7451399503116
      y: -305.46675396807905
      zoom: 0.8544148752422248
