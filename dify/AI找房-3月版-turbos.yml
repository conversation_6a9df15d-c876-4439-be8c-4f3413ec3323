app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: AI找房-3月版-turbos
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: 通勤时长，分钟计算
    id: b62d1bcf-b48e-46d6-ab58-85403b7bb45c
    name: transport_minutes
    selector:
    - conversation
    - transport_minutes
    value: ''
    value_type: string
  - description: 交通方式：transit|ride|walk|drive
    id: 2ee04665-0f61-4b60-8824-9dee5c8b369a
    name: transport
    selector:
    - conversation
    - transport
    value: ''
    value_type: string
  - description: 公里数
    id: 92e9b94d-99c7-4456-9a5b-9ca6898bd31c
    name: distance
    selector:
    - conversation
    - distance
    value: 0
    value_type: number
  - description: 租约类型，1是短租，2是长租，租期超过1年为长租，1年内为短租
    id: 7b08e25d-ea64-4a6a-a40a-373360b5d81c
    name: leasetype
    selector:
    - conversation
    - leasetype
    value: ''
    value_type: string
  - description: 用户要推荐的是房源还是小区
    id: eeb20a15-91d0-4f9b-98aa-6bb03bd72315
    name: room_or_resblock
    selector:
    - conversation
    - room_or_resblock
    value: room
    value_type: string
  - description: 行政区，地铁线缩小范围的追问，0是没有问过，1是问过
    id: 8e8c1622-91de-4ef7-bceb-2752b3f31766
    name: region_asked
    selector:
    - conversation
    - region_asked
    value: 0
    value_type: number
  - description: 针对公司追问通勤，0是没有问过，1是问过
    id: 1d2ad5fa-82a6-4e26-947b-35aa2078cc09
    name: company_asked
    selector:
    - conversation
    - company_asked
    value: 0
    value_type: number
  - description: 当前列表页
    id: f5f714ef-6ed3-4f17-8295-d11714d9a765
    name: current_page
    selector:
    - conversation
    - current_page
    value: 1
    value_type: number
  - description: 用户检索的目标小区
    id: 7e5d6e19-3465-49eb-8408-6ff40ef5e228
    name: resblock
    selector:
    - conversation
    - resblock
    value: ''
    value_type: string
  - description: 预计入住时间
    id: 2f360c69-a024-4a2a-bc49-5668fa0f45f5
    name: checkin_date
    selector:
    - conversation
    - checkin_date
    value: 'null'
    value_type: string
  - description: '朝向: 东,南,西,北'
    id: aa8af68b-8661-453e-8e8c-77d7d3c9a2a7
    name: face
    selector:
    - conversation
    - face
    value: ''
    value_type: string
  - description: '面积区间，示例：10-15平，15平以内  ->10,15       '
    id: 00f00e12-5d7e-486b-a79a-76ffe83fa4ee
    name: area
    selector:
    - conversation
    - area
    value: 'null'
    value_type: string
  - description: 居室:1居室，2居室，3居室，4居室，5居室
    id: aa919ab5-de79-4ada-a31a-706b38531a2d
    name: bed_room
    selector:
    - conversation
    - bed_room
    value: ''
    value_type: string
  - description: 0是没有问过，1是已经问过了
    id: cbd0e396-a7e7-4bd3-a249-392d1883081f
    name: room_type_asked
    selector:
    - conversation
    - room_type_asked
    value: 0
    value_type: number
  - description: 0是没有问过，1是已经问过了
    id: 00a848ce-b5a4-43e5-9d00-50cc762a95b4
    name: budget_asked
    selector:
    - conversation
    - budget_asked
    value: 0
    value_type: number
  - description: 0是没有问过，1是已经问过了
    id: 3ef08fc1-a66c-4fb1-bdff-1493b1b6eaa2
    name: location_asked
    selector:
    - conversation
    - location_asked
    value: 0
    value_type: number
  - description: 位置类型：商圈，地铁,  小区 ，通勤，公司
    id: d2f958a6-d897-46d2-97c3-a2148413d42b
    name: location_type
    selector:
    - conversation
    - location_type
    value: ''
    value_type: string
  - description: 用户期望的房源预算
    id: ab1dce38-9f39-4a86-9943-e85b118c8521
    name: budget
    selector:
    - conversation
    - budget
    value: 'null'
    value_type: string
  - description: 用户期望的房源户型
    id: 72734147-ad72-4458-a544-b4dd10d56981
    name: room_type
    selector:
    - conversation
    - room_type
    value: 'null'
    value_type: string
  - description: 用户期望的房源位置
    id: b31956d8-3072-453d-80aa-53a5f9171f61
    name: location
    selector:
    - conversation
    - location
    value: 'null'
    value_type: string
  - description: 会话状态枚举值：idle, finding_house,finding_house_complete
    id: c3714d73-0ebf-42ab-8dc8-1d1f8d02b307
    name: session_state
    selector:
    - conversation
    - session_state
    value: idle
    value_type: string
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 欢迎使用AI找房助手，我是您的找房顾问小木，通过智能算法帮您过滤海量房源，为了更快找到理想小家，可以和我聊聊
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: true
    suggested_questions:
    - 帮我在酒仙桥附近找一个房
    - "帮我在将台地铁附近找一个房\t"
    - "我是一个即将毕业的大学生，帮我找一个房子\t"
    - 我是3口之家，帮我在朝阳找一个整租
    - 我在美团上班，帮我找一个通勤半小时内的房子
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: true
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: llm
        targetType: if-else
      id: llm-source-1741156585998-target
      selected: false
      source: llm
      sourceHandle: source
      target: '1741156585998'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: start
        targetType: if-else
      id: 1741154458212-source-1741227583067-target
      selected: false
      source: '1741154458212'
      sourceHandle: source
      target: '1741227583067'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741227583067-true-llm-target
      selected: false
      source: '1741227583067'
      sourceHandle: 'true'
      target: llm
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: code
      id: 1741156585998-true-1741227856349-target
      selected: false
      source: '1741156585998'
      sourceHandle: 'true'
      target: '1741227856349'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: assigner
      id: 1741227856349-source-1741228038769-target
      selected: false
      source: '1741227856349'
      sourceHandle: source
      target: '1741228038769'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: parameter-extractor
      id: 1741228038769-source-1741228637950-target
      selected: false
      source: '1741228038769'
      sourceHandle: source
      target: '1741228637950'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: assigner
      id: 1741229953271-source-1741230014416-target
      selected: false
      source: '1741229953271'
      sourceHandle: source
      target: '1741230014416'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: if-else
      id: 1741230014416-source-1741167074836-target
      selected: false
      source: '1741230014416'
      sourceHandle: source
      target: '1741167074836'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: parameter-extractor
      id: 1741227583067-d7ed7c70-2b78-4353-9629-1393e997e5e6-1741228637950-target
      selected: false
      source: '1741227583067'
      sourceHandle: d7ed7c70-2b78-4353-9629-1393e997e5e6
      target: '1741228637950'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741229452581-source-1741328190568-target
      selected: false
      source: '1741229452581'
      sourceHandle: source
      target: '1741328190568'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: assigner
      id: 1741331950830-true-1741332011541-target
      selected: false
      source: '1741331950830'
      sourceHandle: 'true'
      target: '1741332011541'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: assigner
      id: 1741331950830-7971f6da-a712-4b17-a02a-9535b4209003-1741332036940-target
      selected: false
      source: '1741331950830'
      sourceHandle: 7971f6da-a712-4b17-a02a-9535b4209003
      target: '1741332036940'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: assigner
      id: 1741331950830-8b57cd4b-ab92-4f1d-b10e-1a930393e30b-1741332099371-target
      selected: false
      source: '1741331950830'
      sourceHandle: 8b57cd4b-ab92-4f1d-b10e-1a930393e30b
      target: '1741332099371'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: answer
      id: 1741334005357-source-1741334108475-target
      selected: false
      source: '1741334005357'
      sourceHandle: source
      target: '1741334108475'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: answer
      id: 1741335613462-source-1741168104734-target
      selected: false
      source: '1741335613462'
      sourceHandle: source
      target: '1741168104734'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: assigner
      id: 1741404955681-source-1741252220821-target
      selected: false
      source: '1741404955681'
      sourceHandle: source
      target: '1741252220821'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: code
      id: 1741252220821-source-1741335613462-target
      selected: false
      source: '1741252220821'
      sourceHandle: source
      target: '1741335613462'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: assigner
      id: 1741404955681-fail-branch-1741334005357-target
      selected: false
      source: '1741404955681'
      sourceHandle: fail-branch
      target: '1741334005357'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: answer
      id: 1741335613462-fail-branch-1741416716197-target
      selected: false
      source: '1741335613462'
      sourceHandle: fail-branch
      target: '1741416716197'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 1741424186193-source-1741331950830-target
      selected: false
      source: '1741424186193'
      sourceHandle: source
      target: '1741331950830'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: code
      id: 1741331950830-8aec67f6-494f-406e-97bd-5729465dc5d9-1741229953271-target
      selected: false
      source: '1741331950830'
      sourceHandle: 8aec67f6-494f-406e-97bd-5729465dc5d9
      target: '1741229953271'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: llm
      id: 1741332011541-source-1741229452581-target
      selected: false
      source: '1741332011541'
      sourceHandle: source
      target: '1741229452581'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: llm
      id: 1741332036940-source-1741426845734-target
      selected: false
      source: '1741332036940'
      sourceHandle: source
      target: '1741426845734'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: llm
      id: 1741332099371-source-1741426851212-target
      selected: false
      source: '1741332099371'
      sourceHandle: source
      target: '1741426851212'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741426845734-source-1741426880245-target
      selected: false
      source: '1741426845734'
      sourceHandle: source
      target: '1741426880245'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741426851212-source-1741426915644-target
      selected: false
      source: '1741426851212'
      sourceHandle: source
      target: '1741426915644'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: parameter-extractor
        targetType: tool
      id: 1741430476606-source-1741430434882-target
      selected: false
      source: '1741430476606'
      sourceHandle: source
      target: '1741430434882'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 1741430434882-source-1741430759517-target
      selected: false
      source: '1741430434882'
      sourceHandle: source
      target: '1741430759517'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: answer
      id: 1741430759517-source-1741430443136-target
      selected: false
      source: '1741430759517'
      sourceHandle: source
      target: '1741430443136'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741156585998-5024c621-2900-43ae-aa1f-2c141f4b1158-1741601374807-target
      selected: false
      source: '1741156585998'
      sourceHandle: 5024c621-2900-43ae-aa1f-2c141f4b1158
      target: '1741601374807'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741156585998-37449484-8935-45a2-b266-dfbe36c92371-1741601374807-target
      selected: false
      source: '1741156585998'
      sourceHandle: 37449484-8935-45a2-b266-dfbe36c92371
      target: '1741601374807'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741156585998-a44ecb7b-4bc0-464d-b26b-5b596fb40a0f-1741601374807-target
      selected: false
      source: '1741156585998'
      sourceHandle: a44ecb7b-4bc0-464d-b26b-5b596fb40a0f
      target: '1741601374807'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741156585998-279661c3-f949-4f6e-8607-3c389c4fe809-1741601374807-target
      selected: false
      source: '1741156585998'
      sourceHandle: 279661c3-f949-4f6e-8607-3c389c4fe809
      target: '1741601374807'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741601374807-source-1741601549648-target
      selected: false
      source: '1741601374807'
      sourceHandle: source
      target: '1741601549648'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: parameter-extractor
      id: 1741156585998-2cedc68a-23e0-4aeb-843a-da4141ccf516-1741430476606-target
      selected: false
      source: '1741156585998'
      sourceHandle: 2cedc68a-23e0-4aeb-843a-da4141ccf516
      target: '1741430476606'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741156585998-false-1741601374807-target
      selected: false
      source: '1741156585998'
      sourceHandle: 'false'
      target: '1741601374807'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1741167074836-true-1741677322908-target
      selected: false
      source: '1741167074836'
      sourceHandle: 'true'
      target: '1741677322908'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741677322908-source-1741677692698-target
      selected: false
      source: '1741677322908'
      sourceHandle: source
      target: '1741677692698'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: answer
        targetType: tool
      id: 1741677692698-source-1741404955681-target
      selected: false
      source: '1741677692698'
      sourceHandle: source
      target: '1741404955681'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: tool
      id: 1741680284878-true-1741679821876-target
      selected: false
      source: '1741680284878'
      sourceHandle: 'true'
      target: '1741679821876'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: code
      id: 1741680284878-false-1741424186193-target
      selected: false
      source: '1741680284878'
      sourceHandle: 'false'
      target: '1741424186193'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 1741679821876-source-1741680436852-target
      selected: false
      source: '1741679821876'
      sourceHandle: source
      target: '1741680436852'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: if-else
      id: 1741680436852-source-1741680966143-target
      selected: false
      source: '1741680436852'
      sourceHandle: source
      target: '1741680966143'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: code
      id: 1741680966143-false-1741424186193-target
      selected: false
      source: '1741680966143'
      sourceHandle: 'false'
      target: '1741424186193'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741681038156-source-1741681067448-target
      selected: false
      source: '1741681038156'
      sourceHandle: source
      target: '1741681067448'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: answer
      id: 1741692846302-source-1741692872205-target
      selected: false
      source: '1741692846302'
      sourceHandle: source
      target: '1741692872205'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: assigner
      id: 1741680966143-d5cbb985-b791-4bef-a6eb-90c8c95bf3d4-1741694743815-target
      source: '1741680966143'
      sourceHandle: d5cbb985-b791-4bef-a6eb-90c8c95bf3d4
      target: '1741694743815'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: llm
      id: 1741694743815-source-1741692846302-target
      source: '1741694743815'
      sourceHandle: source
      target: '1741692846302'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: if-else
      id: 1741680966143-true-1741694866202-target
      selected: false
      source: '1741680966143'
      sourceHandle: 'true'
      target: '1741694866202'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: assigner
      id: 1741694866202-true-1741694916618-target
      source: '1741694866202'
      sourceHandle: 'true'
      target: '1741694916618'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: llm
      id: 1741694916618-source-1741681038156-target
      source: '1741694916618'
      sourceHandle: source
      target: '1741681038156'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: code
      id: 1741694866202-false-1741424186193-target
      source: '1741694866202'
      sourceHandle: 'false'
      target: '1741424186193'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: parameter-extractor
        targetType: code
      id: 1741228637950-source-1741773949397-target
      source: '1741228637950'
      sourceHandle: source
      target: '1741773949397'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: assigner
      id: 1741773949397-source-1741775165498-target
      source: '1741773949397'
      sourceHandle: source
      target: '1741775165498'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: assigner
        targetType: if-else
      id: 1741775165498-source-1741680284878-target
      source: '1741775165498'
      sourceHandle: source
      target: '1741680284878'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 用户状态：在租/历史非在租/新客
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: user_status
        - label: 用户画像：学生/家庭/单身
          max_length: 256
          options: []
          required: false
          type: text-input
          variable: user_profile
      height: 116
      id: '1741154458212'
      position:
        x: 30
        y: 383
      positionAbsolute:
        x: 30
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '当前意图： {{#conversation.session_state#}}

            {{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_config:
          jinja2_variables:
          - value_selector:
            - conversation
            - session_state
            variable: session_state
        prompt_template:
        - edition_type: basic
          id: 1842527e-a18a-425d-9d95-b2cc38df3a55
          jinja2_text: "### 角色\n\n你是一位资深的房产租赁意图识别专家，拥有丰富的行业知识和敏锐的洞察力。你的任务是准确判断用户关于房产租赁问题的意图类型。在接收到用户问题时，你需要结合用户输入、**当前会话状态**，全面、深入地理解问题的核心需求。\n\
            \n### 当前会话状态\n\n当前会话状态为：{{ session_state }}\n\n### 技能\n\n#### 技能 1：精准识别用户意图\n\
            \n依据以下意图列表，仅返回与之对应的数字序号。\n\n| 序号 | 意图 | 描述 | 示例 |\n| :--: | :---------------\
            \ | :----------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------\
            \ |\n| 1 | 找房需求 | 用户表达了明确的找房意愿 | “我想在北京找一个一居室的房子”，“有没有靠近地铁的房源？”，“我想租个房子”\
            \ |\n| 2 | 房源/小区咨询 | 用户咨询具体的房源或小区信息，包括租金、室友、优惠活动等 | “这个小区的租金大概多少？”，“有没有可以合租的房源？”，“最近有什么优惠活动吗？”\
            \ |\n| 3 | 预约看房 | 用户明确表达了要预约看房的意愿 | “我想预约明天下午看房”，“可以安排看房吗？”，“怎么预约看房？”\
            \ |\n| 4 | 自如相关业务咨询 | 用户咨询与自如相关的业务，如租务、家服、装修、买卖等 | “自如的租房流程是怎样的？”，“自如的家服服务包括哪些？”，“自如的装修服务怎么样？”，“自如也提供买房服务吗？”\
            \ |\n| 5 | 政策/信息咨询 | 用户咨询与租房相关的政策或信息 | “北京租房有什么政策？”，“租房需要注意哪些事项？”，“租房合同应该怎么签？”\
            \ |\n| 6 | 投诉相关 | 用户表达了投诉的意愿 | “我对你们的服务很不满意”，“我要投诉你们的管家”，“这个房子有问题，我要投诉”\
            \ |\n| 7 | 违禁词 | 用户输入包含敏感词或不当言论 | （不提供示例，系统自动检测） |\n| 8 | 闲聊 | 用户进行与找房无关的闲聊\
            \ | “今天天气真好”，“你叫什么名字？”，“你有什么爱好？” |\n\n### 回复格式\n\n- 仅回复意图对应的序号：1、2、3、4、5、6、7、8\n\
            \n### 示例\n\n#### 示例 1\n\n当前会话状态：idle\n当前用户输入：我想找一个离公司近的房子\n1\n\n#### 示例\
            \ 2\n\n当前会话状态：finding_house\n当前用户输入：这个房子可以合租吗？租金多少钱？\n2\n\n#### 示例 3\n\
            \n当前会话状态：idle\n当前用户输入：我想预约明天上午看房\n3\n\n#### 示例 4\n\n当前会话状态：idle\n当前用户输入：自如的保洁服务怎么样？\n\
            4\n\n#### 示例 5\n\n当前会话状态：idle\n当前用户输入：租房需要什么手续？\n5\n\n#### 示例 6\n\n当前会话状态：idle\n\
            当前用户输入：我对你们的服务非常不满意！\n6\n\n#### 示例 7\n\n当前会话状态：idle\n当前用户输入：你真垃圾\n7\n\n\
            #### 示例 8\n\n当前会话状态：idle\n当前用户输入：今天真不错\n8\n\n### 限制\n\n- 若用户输入包含多个意图，需根据输入内容输出最为贴切的一个意图序号，仅回复一个数字，无需阐述原因。\n\
            - 优先判断是否包含违禁词，如果包含，直接返回7。\n- 如果无法准确判断用户意图，统一归类到8（闲聊）。\n- {% if session_state\
            \ == \"finding_house\" %}\n  如果当前会话状态为 \"finding_house\"，则直接返回 1，除非用户明确表达了其他意图（例如投诉、咨询自如业务等）。\n\
            \  具体来说：\n  * 如果用户输入包含 \"投诉\"、\"不满意\" 等关键词，返回 6。\n  * 如果用户输入包含 \"自如\"\
            \ 等关键词，返回 4。\n  * 否则，直接返回 1。\n  {% endif %}"
          role: system
          text: "<role>您是房产租赁意图识别专家，基于用户输入判断意图类型。您的任务是返回一个数字，仅此而已。</role>\n\n<format_instructions>\n\
            只输出单个数字(1-6)，不添加任何其他文字、标点、解释或注释。\n不要在数字前后添加任何内容。\n不要提供分析、解释或建议。\n只返回一个数字。\n\
            </format_instructions>\n\n<intents>\n1: 找房需求 - 包含所有与房源、小区查找、筛选和详情咨询相关的对话\n\
            2: 预约看房 - 表达看房意愿或询问看房时间/流程\n3: 业务咨询 - 咨询租房政策、手续、合同、平台服务等信息\n4: 投诉反馈 -\
            \ 表达不满或投诉，包含违禁内容\n5: 闲聊问答 - 与租房无关的一般性对话\n6: 查询小区 - 仅查询小区环境、配套设施、位置等基本信息\n\
            </intents>\n\n<session_memory>\n当前状态: {{#conversation.session_state#}}\n\
            对话轮次: {{#sys.dialogue_count#}}\n用户已表达偏好:\n- 区域: {{#conversation.location#}}\n\
            - 价格范围: {{#conversation.budget#}}\n- 户型需求: {{#conversation.room_type#}}\n\
            - 特殊要求:  居室：{{#conversation.bed_room#}}  朝向：{{#conversation.face#}}面积：{{#conversation.area#}}\n\
            </session_memory>\n\n<contextual_instruction_memory>\n以下简短指令在找房流程中具有特定含义：\n\
            \n\"多来点\" → 在找房状态下视为继续找房(意图1)\n\"再看看\" → 在找房状态下视为继续找房(意图1)\n\"下一个\" →\
            \ 在找房状态下视为继续浏览房源(意图1)\n\"可以/好/行/不错\" → 在详情咨询后可能表示满意，准备预约(可能是意图2)\n\"算了/不要/不好\"\
            \ → 在详情咨询后表示不满意，应继续找房(意图1)\n\"多少钱/价格/费用\" → 在任何状态下询问价格，属于找房需求(意图1)\n\"\
            几点/什么时候/约\" → 通常与预约看房相关(意图2)\n\n当用户输入非常简短(少于5个字)且在找房流程中时，应优先根据上下文理解为找房相关意图。\n\
            </contextual_instruction_memory>\n\n<intent_refinement>\n1: 找房需求 - 包含三个子阶段：\n\
            \   a. 需求探索：用户表达租房意愿、讨论区域、价格、户型等宏观条件\n      ▶ 触发词：找房、推荐、有什么房源、有什么小区、预算、户型、区域名称\n\
            \   b. 房源浏览：用户在多个推荐选项中进行筛选比较\n      ▶ 触发词：还有其他、更便宜的、更大的、比较一下\n   c. 房源详询：针对特定房源询问细节\n\
            \      ▶ 触发词：这套房子、那个房源、多少钱、什么朝向、几楼、有无电梯\n\n6: 查询小区 - 严格限定于具体小区整体信息查询，用户会提供准确的小区名称\n\
            \   ▶ 触发词：小区环境、物业、绿化、交通、配套\n   ▶ 区分规则：若询问包含\"小区的房子/房源\"则归类为找房需求(1)\n</intent_refinement>\n\
            \n<state_transition_rules>\n- 基础原则：优先保持当前状态，除非有明确状态转换信号\n- 当会话主题从小区整体情况转向小区内房源时，意图从6转为1\n\
            - 当从具体房源咨询转向预约流程时，意图从1转为2\n- 当从找房需求转向平台政策或收费标准时，意图从1转为3\n</state_transition_rules>\n\
            \n<multi_turn_examples>\n对话1 - 需求探索到房源详询:\n用户: \"北京租房哪里便宜点\"\n输出: 1\n\
            用户: \"通州怎么样\"\n输出: 1\n用户: \"通州北苑有什么小区\"\n输出: 1\n用户: \"这个小区房子大概多少钱\"\n\
            输出: 1\n用户: \"有两居室吗\"\n输出: 1\n用户: \"那个精装修的两居室是什么样的\"\n输出: 1\n用户: \"有阳台吗\"\
            \n输出: 1\n用户: \"能约时间看看房吗\"\n输出: 2\n\n对话2 - 小区查询到找房:\n用户: \"天通苑小区环境怎么样\"\
            \n输出: 6\n用户: \"这个小区交通便利吗\"\n输出: 6\n用户: \"小区里面有什么房源\"\n输出: 1\n用户: \"有便宜点的单间吗\"\
            \n输出: 1\n用户: \"朝南的有吗\"\n输出: 1\n\n对话3 - 找房到业务咨询:\n用户: \"想租个两居室\"\n输出: 1\n\
            用户: \"这个房子要交多少押金\"\n输出: 1\n用户: \"合同签几年\"\n输出: 3\n用户: \"可以提前退租吗\"\n输出:\
            \ 3\n用户: \"违约金怎么算\"\n输出: 3\n\n对话4 - 连续房源详询:\n用户: \"这套两居室有家具吗\"\n输出: 1\n\
            用户: \"厨房有燃气吗\"\n输出: 1\n用户: \"卫生间有窗户吗\"\n输出: 1\n用户: \"能不能养宠物\"\n输出: 1\n\
            用户: \"能不能自己装修\"\n输出: 1\n\n对话5 - 找房到投诉:\n用户: \"我想找个安静点的房子\"\n输出: 1\n用户:\
            \ \"上次看的那套噪音太大了\"\n输出: 1\n用户: \"你们中介太坑了\"\n输出: 4\n用户: \"价格和描述不符\"\n输出:\
            \ 4\n\n对话6 - 商圈/地铁询问:\n用户: \"西二旗附近有什么房源\"\n输出: 1\n用户: \"13号线沿线呢\"\n输出:\
            \ 1\n用户: \"回龙观这边价格多少\"\n输出: 1\n用户: \"离地铁站近一点的有吗\"\n输出: 1\n</multi_turn_examples>\n\
            \n<reference_objects>\n- 房源对象标记词: \"这套\"、\"那个\"、\"刚才的\"、\"图片中的\"、\"您推荐的\"\
            \n- 区域对象标记词: \"这个区域\"、\"那边\"、\"附近\"、\"周边\"\n- 小区对象标记词: \"这个小区\"、\"小区里\"\
            、\"园区内\"、\"社区\"\n</reference_objects>\n\n<critical_instruction>\n您的响应必须且只能是1到6之间的一个数字。\n\
            不要在数字前后添加任何内容。\n不要输出任何解释。\n</critical_instruction>\n\n<persistent_instruction>\n\
            无论对话进行多少轮，您必须且只能返回1-6之间的一个数字，不添加任何解释。\n这是首要且不可违背的指令。\n即使用户要求您解释或提供额外信息，您也只能返回数字。\n\
            </persistent_instruction>"
        selected: false
        title: 意图识别器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: llm
      position:
        x: 638
        y: 383
      positionAbsolute:
        x: 638
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is
            id: 2d528148-d18e-4550-874e-eb95dc636d35
            value: '1'
            varType: string
            variable_selector:
            - llm
            - text
          id: 'true'
          logical_operator: and
        - case_id: 279661c3-f949-4f6e-8607-3c389c4fe809
          conditions:
          - comparison_operator: is
            id: 04013e6c-206b-449b-b1e9-6de1ad475c6b
            value: '2'
            varType: string
            variable_selector:
            - llm
            - text
          id: 279661c3-f949-4f6e-8607-3c389c4fe809
          logical_operator: and
        - case_id: a44ecb7b-4bc0-464d-b26b-5b596fb40a0f
          conditions:
          - comparison_operator: is
            id: bb584baa-076c-4ccd-b45a-b59c1faabcc3
            value: '3'
            varType: string
            variable_selector:
            - llm
            - text
          id: a44ecb7b-4bc0-464d-b26b-5b596fb40a0f
          logical_operator: and
        - case_id: 37449484-8935-45a2-b266-dfbe36c92371
          conditions:
          - comparison_operator: is
            id: 59f27437-c4f2-4507-8dba-9ad1e149f59e
            value: '4'
            varType: string
            variable_selector:
            - llm
            - text
          id: 37449484-8935-45a2-b266-dfbe36c92371
          logical_operator: and
        - case_id: 5024c621-2900-43ae-aa1f-2c141f4b1158
          conditions:
          - comparison_operator: is
            id: 3bcd39c3-d4e9-4091-9dfb-defc3868dcca
            value: '5'
            varType: string
            variable_selector:
            - llm
            - text
          id: 5024c621-2900-43ae-aa1f-2c141f4b1158
          logical_operator: and
        - case_id: 2cedc68a-23e0-4aeb-843a-da4141ccf516
          conditions:
          - comparison_operator: is
            id: 16733f65-374c-45e9-bd35-edf3c1aeb5bf
            value: '6'
            varType: string
            variable_selector:
            - llm
            - text
          id: 2cedc68a-23e0-4aeb-843a-da4141ccf516
          logical_operator: and
        desc: ''
        selected: false
        title: 意图选择器
        type: if-else
      height: 366
      id: '1741156585998'
      position:
        x: 942
        y: 383
      positionAbsolute:
        x: 942
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}

            用户意图理解：

            <intents>

            1: 找房需求 - 包含所有与房源查找、筛选和详情咨询相关的对话

            2: 预约看房 - 表达看房意愿或询问看房时间/流程

            3: 业务咨询 - 咨询租房政策、手续、合同、平台服务等信息

            4: 投诉反馈 - 表达不满或投诉，包含违禁内容

            5: 闲聊问答 - 与租房无关的一般性对话

            6: 查询小区 - 仅查询小区环境、配套设施、位置等基本信息

            </intents>

            当前用户意图： {{#llm.text#}}

            当前会话轮次： {{#sys.dialogue_count#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 50
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_template:
        - id: 9cb1091c-c22c-46fb-ba60-e85ffe47bc85
          role: system
          text: "<rental_assistant>\n  <intents>\n    <intent id=\"1\" name=\"找房需求\"\
            >\n      <description>包含所有与房源、小区查找、筛选和详情咨询相关的对话，如区域选择、价格范围、户型需求、特殊设施要求等</description>\n\
            \      <examples>\n        <example>我想找一个两室一厅的房子</example>\n        <example>有没有3000元以下的单间</example>\n\
            \        <example>我需要一个带电梯的公寓</example>\n        <example>朝阳有哪些小区推荐?</example>\n\
            \        <example>我在美团工作，步行1公里内有哪些小区？</example>\n      </examples>\n \
            \   </intent>\n    \n    <intent id=\"2\" name=\"预约看房\">\n      <description>表达看房意愿或询问看房时间/流程、预约方式、带看人员等</description>\n\
            \      <examples>\n        <example>我想去看看这套房子</example>\n        <example>周末有空可以带我去看房吗</example>\n\
            \        <example>预约看房需要什么手续</example>\n      </examples>\n    </intent>\n\
            \    \n    <intent id=\"3\" name=\"业务咨询\">\n      <description>咨询租房政策、手续、合同、押金、租期、平台服务费等信息</description>\n\
            \      <examples>\n        <example>签合同需要什么证件</example>\n        <example>租金一般怎么支付</example>\n\
            \        <example>中介费是多少</example>\n      </examples>\n    </intent>\n\
            \    \n    <intent id=\"4\" name=\"投诉反馈\">\n      <description>表达不满或投诉，包含对房东、中介、平台服务的不满或建议</description>\n\
            \      <examples>\n        <example>房东不退押金</example>\n        <example>中介描述与实际情况不符</example>\n\
            \        <example>预约了但是没人来带看</example>\n      </examples>\n    </intent>\n\
            \    \n    <intent id=\"5\" name=\"闲聊问答\">\n      <description>与租房无关的一般性对话，包括问候、感谢等</description>\n\
            \      <examples>\n        <example>你好，今天天气怎么样</example>\n        <example>谢谢你的帮助</example>\n\
            \        <example>你是人工客服吗</example>\n      </examples>\n    </intent>\n\
            \    \n    <intent id=\"6\" name=\"查询小区\">\n      <description>查询特定小区的环境、配套设施、位置、交通、安全等基本信息</description>\n\
            \      <examples>\n        <example>这个小区的物业怎么样</example>\n        <example>小区周边有什么公共交通</example>\n\
            \        <example>小区里有健身房吗</example>\n      </examples>\n    </intent>\n\
            \  </intents>\n  \n  <conversation_strategy>\n    <round number=\"1\"\
            >\n      <strategy>强引导策略 - 无论用户输入什么，都主动引导至租房相关话题</strategy>\n      <action>优先识别租房意图，提出引导性问题收集租房需求</action>\n\
            \    </round>\n    \n    <rounds range=\"2-3\">\n      <strategy>自然交流策略\
            \ - 允许一定程度的开放对话</strategy>\n      <action>根据用户实际意图自然回应，适当跟随用户话题，但在回答结尾处轻微引导</action>\n\
            \    </rounds>\n    \n    <rounds range=\"4+\">\n      <strategy>重新引导策略\
            \ - 再次强化租房主题</strategy>\n      <action>礼貌而坚定地将对话引回租房相关话题，提供具体租房问题</action>\n\
            \    </rounds>\n  </conversation_strategy>\n  \n  <instructions>\n   \
            \ <step>1. 识别当前是对话的第几轮</step>\n    <step>2. 分析用户输入的完整内容和上下文</step>\n \
            \   <step>3. 确定用户当前意图，匹配到最相关的意图类别</step>\n    <step>4. 根据当前对话轮次和用户意图，选择适当的回应策略：</step>\n\
            \    <step>   - 第1轮：无论用户意图如何，都强引导至租房话题</step>\n    <step>   - 第2-3轮：自然回应用户实际意图，但在回答末尾轻微引导回租房</step>\n\
            \    <step>   - 第4轮及以后：再次强化租房主题引导</step>\n    <step>5. 提取用户提供的租房相关参数(如区域、价格、户型等)并记录</step>\n\
            \    <step>6. 确保回复自然、专业，不包含任何XML标签</step>\n  </instructions>\n  \n  <examples>\n\
            \    <!-- 第1轮对话示例 -->\n    <example>\n      <round>1</round>\n      <input>你好，今天天气真不错</input>\n\
            \      <analyzed_intent>闲聊问答</analyzed_intent>\n      <output>\n     \
            \   您好！很高兴为您服务。是的，好天气总是令人心情愉快。我是您的租房助手，可以帮您找到理想的住所。\n        \n      \
            \  1. 您目前在寻找什么区域的房源呢？\n        2. 您对房子的户型有什么偏好？比如一居、两居或三居？\n        3.\
            \ 您的租房预算大概是多少？\n      </output>\n    </example>\n    \n    <!-- 第2-3轮对话示例\
            \ -->\n    <example>\n      <round>2</round>\n      <input>我最近买了一辆新车，停车很麻烦</input>\n\
            \      <analyzed_intent>闲聊问答</analyzed_intent>\n      <output>\n     \
            \   恭喜您有了新车！确实，在城市里找停车位常常是件头疼事。很多小区的停车位资源都比较紧张。\n        \n        说起停车，您在找房时是否需要考虑带车位的房源呢？或者您可能更关心小区的停车情况？\n\
            \      </output>\n    </example>\n    \n    <example>\n      <round>3</round>\n\
            \      <input>最近工作很忙，经常加班到很晚</input>\n      <analyzed_intent>闲聊问答</analyzed_intent>\n\
            \      <output>\n        工作忙碌确实很辛苦，希望您能注意休息。对于经常加班的朋友来说，住所的位置就显得尤为重要了。\n\
            \        \n        您可能会考虑离公司近一些的房源，这样可以节省通勤时间。您的工作区域在哪里呢？也许我可以推荐附近的住宅区。\n\
            \      </output>\n    </example>\n    \n    <!-- 第4轮及以后对话示例 -->\n    <example>\n\
            \      <round>4</round>\n      <input>你喜欢什么电影？</input>\n      <analyzed_intent>闲聊问答</analyzed_intent>\n\
            \      <output>\n        关于电影话题我们可以改天再聊。现在，让我们回到找房的话题上来，这样我才能更好地帮助您：\n\
            \        \n        1. 您希望租房的区域是哪里呢？\n        2. 您的预算范围大概是多少？\n       \
            \ 3. 您对房屋的户型和面积有什么要求？\n      </output>\n    </example>\n    \n    <!--\
            \ 租房需求示例（任意轮次） -->\n    <example>\n      <round>2</round>\n      <input>我想在朝阳区租一套两居室的房子，预算6000左右</input>\n\
            \      <analyzed_intent>找房需求</analyzed_intent>\n      <extracted_params>\n\
            \        <area>朝阳区</area>\n        <layout>两居室</layout>\n        <budget>6000元左右</budget>\n\
            \      </extracted_params>\n      <output>\n        了解您需要朝阳区6000元左右的两居室房源。这个预算在朝阳区部分区域是可以找到不错的两居的。\n\
            \        \n        1. 您对朝阳区内的具体位置有偏好吗？比如靠近某个地铁站或商圈？\n        2. 您对房屋的装修要求是精装、简装还是毛坯都可以考虑？\n\
            \        3. 您计划什么时候入住呢？\n      </output>\n    </example>\n    \n    <!--\
            \ 多轮对话连续示例 -->\n    <example>\n      <conversation>\n        <turn>\n\
            \          <round>1</round>\n          <user>你好啊</user>\n          <assistant>\n\
            \            您好！很高兴为您服务。我是您的租房助手，可以帮您找到合适的住所。\n            \n        \
            \    1. 您想在哪个区域租房呢？\n            2. 您的预算大概是多少？\n            3. 您对房型有什么要求？\n\
            \          </assistant>\n        </turn>\n        \n        <turn>\n \
            \         <round>2</round>\n          <user>我喜欢打篮球，附近有篮球场的小区就好</user>\n\
            \          <assistant>\n            您喜欢篮球啊，健康的爱好！确实，有运动设施的小区对生活质量提升很大。\n\
            \            \n            我可以帮您找带篮球场的小区。您大致想在哪个城区找房呢？预算方面有什么考虑吗？\n  \
            \        </assistant>\n        </turn>\n        \n        <turn>\n   \
            \       <round>3</round>\n          <user>我还喜欢做饭，厨房要宽敞一些</user>\n    \
            \      <assistant>\n            了解，喜欢烹饪的话，一个宽敞的厨房确实很重要！现在很多租客也都很看重厨房的使用体验。\n\
            \            \n            我会注意筛选厨房空间较大的房源。同时，您能告诉我您期望的区域和大致预算吗？这样我能更精准地为您推荐。\n\
            \          </assistant>\n        </turn>\n        \n        <turn>\n \
            \         <round>4</round>\n          <user>最近看了一部新电影挺好看的</user>\n   \
            \       <assistant>\n            我理解您想分享生活中的乐趣。不过现在让我们回到租房话题，这样才能尽快帮您找到合适的房源：\n\
            \            \n            1. 您想租房的城市和区域是哪里？\n            2. 您的月租预算大概是多少？\n\
            \            3. 您需要什么样的户型？是合租还是整租？\n            \n            有了这些信息，我才能开始为您筛选带篮球场且厨房宽敞的合适房源。\n\
            \          </assistant>\n        </turn>\n      </conversation>\n    </example>\n\
            \  </examples>\n  \n  <validation_rules>\n    <rule>系统必须正确识别并记录当前对话轮次</rule>\n\
            \    <rule>第一轮必须强引导至租房主题</rule>\n    <rule>第二、三轮可自然跟随用户话题，但应在回答末尾轻微引导</rule>\n\
            \    <rule>第四轮及以后重新强化租房引导</rule>\n    <rule>当用户明确表达租房需求时，无论轮次都应专注于此需求</rule>\n\
            \    <rule>所有回复必须不含XML标签和技术指令</rule>\n    <rule>系统应记录用户提供的所有租房参数用于后续推荐</rule>\n\
            \  </validation_rules>\n</rental_assistant>"
        selected: false
        title: 意图追问
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741601374807'
      position:
        x: 1246
        y: 637
      positionAbsolute:
        x: 1246
        y: 637
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: '<parameter_extraction>

          <!-- 参数定义和提取规则 -->

          <parameter name="location">

          <rules>

          <rule>提取核心区域名称，去除"附近"、"周边"、"沿线"等修饰词</rule>

          <rule>保留行政区划和地标名称</rule>

          <rule>若未提及位置，输出"null"</rule>

          </rules>

          </parameter>


          <parameter name="location_type">

          <rules>

          <rule>根据位置判断其类型：商圈、地铁线、地铁、小区、公司、行政区、自如寓、学校</rule>

          <rule>若位置为"null"，位置类型也输出"null"</rule>

          <rule>若无法明确判断位置类型，输出"null"</rule>

          </rules>

          </parameter>


          <parameter name="budget">

          <rules>

          <rule>统一格式为"最小值,最大值"</rule>

          <rule>转换单位：k=千，w=万（如5k=5000, 3w=30000）</rule>

          <rule>处理范围表述（如"3-4k"→"3000,4000"，"2000-5000"→"2000,5000"）</rule>

          <rule>单一值转为上限区间（如5000→"0,5000"）</rule>

          <rule>提取预算如"X左右", 则在X上下浮动1000元，（如"5000左右"→"4000,6000"）</rule>

          <rule>如提供了准确数字, 如"4000", 则转化为不多于4000，（如 "4000"->"0,4000）</rule>

          <rule>处理模糊表述（如"最多8k"→"0,8000"）</rule>

          <rule>若未提及预算，输出"null"</rule>

          <rule>仅当明确针对预算使用"价格不限"、"预算不限"、"钱不是问题"、"花多少都行"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对预算，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="room_type">

          <rules>

          <rule>区分"整租"与"合租"</rule>

          <rule>若无法正确识别房型意图，或用户未明确表达，默认为"null"</rule>

          <rule>提取具体房型如"一居"、"两室一厅"等</rule>

          <rule>若未提及房型，输出"null"</rule>

          </rules>

          </parameter>


          <parameter name="area">

          <rules>

          <rule>统一格式为"最小值,最大值"</rule>

          <rule>处理范围表示（如"10-15平方米"→"10,15"）</rule>

          <rule>处理上限表示（如"50平以内"、"小于50平"→"0,50"）</rule>

          <rule>处理下限表示（如"40平以上"→"40,120"，"大于40平"→"40,120"）</rule>

          <rule>处理具体数值（如"50平米"→"50,50"）</rule>

          <rule>处理模糊表述（如"50平左右"→"45,55"）</rule>

          <rule>若未提及面积，输出"null"</rule>

          <rule>仅当明确针对面积使用"面积不限"、"大小无所谓"、"多大都行"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对面积，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="bed_room">

          <rules>

          <rule>提取居室数量，统一输出为数字：1, 2, 3, 4, 5</rule>

          <rule>转换中文数字（如"一居"→"1"，"两居"→"2"，"三居"→"3"）</rule>

          <rule>从"x室y厅"格式中提取x的值（如"两室一厅"→"2"）</rule>

          <rule>识别"x居室"、"x居"格式（如"3居室"→"3"）</rule>

          <rule>若未提及居室数量，输出"null"</rule>

          <rule>仅当明确针对居室使用"居室不限"、"几室都行"、"不限几居"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对居室，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="face">

          <rules>

          <rule>识别标准朝向表述：东、南、西、北</rule>

          <rule>将朝向转换为对应数字编码：东 1, 南 2, 西 3, 北 4</rule>

          <rule>处理组合朝向，用逗号分隔（如"东南朝向"→"1,2"）</rule>

          <rule>识别含有"朝向"、"向"、"朝"、"面向"等关键词的表达</rule>

          <rule>若未提及朝向，输出"null"</rule>

          <rule>仅当明确针对朝向使用"朝向不重要"、"朝向无所谓"、"朝哪都行"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对朝向，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="room_or_resblock">

          <rules>

          <rule>判断用户意向寻找的是具体房源，还是小区</rule>

          <rule>若判断用户表达的是小区，则为"resblock"</rule>

          <rule>若没有特别提及小区相关，则为"room"</rule>

          <rule>该字段不支持"any"值，始终输出具体类型</rule>

          </rules>

          </parameter>


          <parameter name="leasetype">

          <rules>

          <rule>租期超过1年为长租，用数字2表示</rule>

          <rule>租期在1年及1年以内为短租，用数字1表示</rule>

          <rule>若明确提到"短租"、"短期"、"临时"等词，输出"1"</rule>

          <rule>若明确提到"长租"、"长期"、"常住"等词，输出"2"</rule>

          <rule>若提到具体月数或天数，如"6个月"、"300天"，根据是否超过365天判断</rule>

          <rule>若未提及租期，输出"null"</rule>

          <rule>仅当明确针对租期使用"租期不限"、"长租短租都行"、"不限制租期"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对租期，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="distance">

          <rules>

          <rule>提取用户提到的距离数值，如"5公里内"、"3km以内"</rule>

          <rule>统一格式为数字，不带单位，如"5"、"3"</rule>

          <rule>若表述为"X公里内"、"X公里以内"、"小于X公里"，则提取X值</rule>

          <rule>将"千米"、"km"、"公里"等单位统一转换处理</rule>

          <rule>若表述为"X分钟车程"、"X分钟路程"，根据交通工具估算距离（步行约4km/h，驾车约30km/h）</rule>

          <rule>若未提及距离，输出"null"</rule>

          <rule>仅当明确针对距离使用"距离不限"、"多远都行"、"远近无所谓"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对距离，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="transport">

          <rules>

          <rule>提取用户提到的通勤方式</rule>

          <rule>将"地铁"、"公交"、"公共交通"、"公交车"、"轨道交通"等映射为"transit"</rule>

          <rule>将"步行"、"走路"、"走"、"徒步"等映射为"walk"</rule>

          <rule>将"开车"、"驾车"、"自驾"、"汽车"等映射为"drive"</rule>

          <rule>将"骑车"、"自行车"、"单车"、"电动车"、"共享单车"等映射为"ride"</rule>

          <rule>若未提及通勤方式，输出"null"</rule>

          <rule>仅当明确针对通勤方式使用"交通方式不限"、"怎么去都行"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对通勤方式，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="transport_minutes">

          <rules>

          <rule>提取用户提到的通勤时长，单位为分钟</rule>

          <rule>统一格式为数字，不带单位，如"30"</rule>

          <rule>若表述为"X分钟"、"X分钟内"、"X分钟以内"，则提取X值</rule>

          <rule>若表述为"X小时"，则转换为分钟，如"1小时"→"60"</rule>

          <rule>若表述为"X小时Y分钟"，则转换为总分钟数，如"1小时30分钟"→"90"</rule>

          <rule>处理上限表示（如"半小时内"→"30"，"1小时内"→"60"）</rule>

          <rule>处理下限表示（如"至少20分钟"→"20"）</rule>

          <rule>若未提及通勤时长，输出"null"</rule>

          <rule>仅当明确针对通勤时长使用"时间不限"、"多久都行"、"不在乎时间"等表达时，才输出"any"</rule>

          <rule>若用户表达中的"随便"、"不限"等词不是明确针对通勤时长，则不要输出"any"</rule>

          </rules>

          </parameter>


          <parameter name="current_page">

          <rules>

          <rule>简化处理：只返回1或2</rule>

          <rule>若用户提及"继续"、"再来"、"下一页"、"更多"等表达，则返回"2"</rule>

          <rule>其他情况均返回"1"</rule>

          </rules>

          </parameter>

          </extraction_parameters>

          <!-- 统一输出格式 -->

          <output_format>

          <json_template>

          {

          "location": "提取的位置",

          "location_type": "位置类型",

          "budget": "最小值,最大值",

          "room_type": "提取的房型",

          "area": "最小值,最大值",

          "bed_room": "提取的居室数量",

          "face": "提取的朝向编码",

          "room_or_resblock": "提取的查询对象类型",

          "current_page": "当前页码",

          "leasetype": "租约类型",

          "distance": "提取的距离值",

          "transport": "提取的通勤方式",

          "transport_minutes": "提取的通勤时长"

          }

          </json_template>

          </output_format>

          <!-- 处理说明 -->

          <processing_instructions>

          <instruction>1. 仔细分析用户输入，准确提取所有参数信息</instruction>

          <instruction>2. 特别注意提取distance距离参数，统一为数值格式</instruction>

          <instruction>3. 对current_page参数简化处理，仅返回1或2</instruction>

          <instruction>4. 关键："any"值只在用户明确针对特定参数表示不限时才使用，不要混淆不同参数的"不限"表达</instruction>

          <instruction>5. 用户表达"不限"、"随便"等模糊词时，必须结合上下文判断是针对哪个具体参数，避免过度提取</instruction>

          <instruction>6. 当用户表达"其他条件都不限"时，只将用户未明确提及的参数保持为"null"，而不是全部改为"any"</instruction>

          <instruction>7. 正确识别通勤方式，将"地铁/公交"映射为"transit"，"步行"映射为"walk"，"开车"映射为"drive"，"骑车"映射为"ride"</instruction>

          <instruction>8. 通勤时长统一转换为分钟数字，如"1小时"转换为"60"</instruction>

          <instruction>9. 将提取的所有参数组织为JSON格式输出</instruction>

          </processing_instructions>

          </parameter_extraction>'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        parameters:
        - description: 位置信息，区域，通勤，地铁，公司，没有提取出来默认值为null
          name: location
          required: true
          type: string
        - description: '预算，示例:   3-8k, 提取成 3000,8000,4k以内，提取成0,4000, 没有提取出来默认值为null'
          name: budget
          required: true
          type: string
        - description: 房源户型，合租， 整租,  没有提取出默认值为null
          name: room_type
          required: true
          type: string
        - description: 位置类型：商圈，地铁线，地铁，小区，公司，行政区，自如寓，学校
          name: location_type
          required: false
          type: string
        - description: 居室：1 ，2，3，4，5
          name: bed_room
          required: false
          type: string
        - description: 面积，示例：50平以内， 10-15,40以上
          name: area
          required: false
          type: string
        - description: "朝向\n东 1 \n南 2\n西 3\n北 4"
          name: face
          required: false
          type: string
        - description: 当前页数
          name: current_page
          required: false
          type: number
        - description: '用户查询的目标对象类型。用户可能表达出两种意向：

            1. 直接寻找具体的房源

            2. 在指定的区域内先寻找小区'
          name: room_or_resblock
          required: false
          type: string
        - description: 租约类型，1是短租，2是长租，租期超过1年为长租，1年内为短租
          name: leasetype
          required: false
          type: string
        - description: 距离数，km,公里
          name: distance
          required: false
          type: number
        - description: 通勤方式，transit|ride|walk|drive
          name: transport
          required: false
          type: string
        - description: 通勤时长，分钟维度
          name: transport_minutes
          required: false
          type: string
        query:
        - sys
        - query
        reasoning_mode: prompt
        selected: false
        title: 提取查询房源参数
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741228637950'
      position:
        x: 1854
        y: 479.5
      positionAbsolute:
        x: 1854
        y: 479.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is
            id: f3b1b534-26a2-460d-99dd-78f8e7098edd
            value: finding_house_complete
            varType: string
            variable_selector:
            - conversation
            - session_state
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 找房信息收集完成
        type: if-else
      height: 126
      id: '1741167074836'
      position:
        x: 5502
        y: 670.5
      positionAbsolute:
        x: 5502
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741335613462.result#}}'
        desc: ''
        selected: false
        title: 直接回复 3
        type: answer
        variables: []
      height: 103
      id: '1741168104734'
      position:
        x: 7326
        y: 854.5
      positionAbsolute:
        x: 7326
        y: 854.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: is
            id: 07ab2e27-b55d-40cc-ad44-70b94ce4c69a
            value: idle
            varType: string
            variable_selector:
            - conversation
            - session_state
          id: 'true'
          logical_operator: and
        - case_id: d7ed7c70-2b78-4353-9629-1393e997e5e6
          conditions:
          - comparison_operator: is
            id: d68afe89-3722-4aa0-beb7-68ea74192dd9
            value: finding_house
            varType: string
            variable_selector:
            - conversation
            - session_state
          id: d7ed7c70-2b78-4353-9629-1393e997e5e6
          logical_operator: and
        desc: ''
        selected: false
        title: 判断是否为找房状态
        type: if-else
      height: 174
      id: '1741227583067'
      position:
        x: 334
        y: 383
      positionAbsolute:
        x: 334
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\ndef main() -> dict:\n    return {\n        \"result\": \"finding_house\"\
          ,\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 更新找房状态
        type: code
        variables: []
      height: 54
      id: '1741227856349'
      position:
        x: 1246
        y: 523.5
      positionAbsolute:
        x: 1246
        y: 523.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1741227856349'
          - result
          variable_selector:
          - conversation
          - session_state
          write_mode: over-write
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 88
      id: '1741228038769'
      position:
        x: 1550
        y: 484.5
      positionAbsolute:
        x: 1550
        y: 484.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_template:
        - id: da488d71-047e-4a2c-888c-fff15ed5b4cf
          role: system
          text: '<instructions>

            根据用户当前的位置信息和提问状态，生成自然友好的位置询问提示。


            输入信息：

            - 用户当前位置信息 (location)

            - 位置提问状态 (location_asked)


            处理逻辑：

            如果当前提问意图明确为"ask_location"或(location为空)，则生成位置提问语句。

            提问应当自然友好，可针对不同场景使用不同变体。


            位置提问变体：

            1. 基础提问："请问您想在哪个区域找房呢？"

            2. 提供选择："您是想在市中心还是郊区找房呢？"

            3. 引导详细："能告诉我您心仪的位置或附近的地标吗？"

            4. 工作通勤："您是想找离工作地点近的房源吗？"

            5. 交通导向："您是想找地铁沿线的房源，还是特定区域的呢？"


            针对不同城市可以增加城市特色提问。


            注意：

            1. 只生成一句话的提问

            2. 使用自然、亲切的语气

            3. 不要包含技术词汇如"null"或"undefined"

            4. 不要输出变量名称

            </instructions>


            <examples>

            <example>

            <input>{"当前提问意图": "ask_location", "当前用户意向": {"位置": "", "预算": 5000, "户型":
            "合租"}, "当前提问状态": {"位置": 0, "预算": 1, "房源类型": 1}}</input>

            <output>请问您想在哪个区域找房呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "", "当前用户意向": {"位置": null, "预算": 8000, "户型": "整租"},
            "当前提问状态": {"位置": 0, "预算": 1, "房源类型": 1}}</input>

            <output>您期望在哪个位置或者附近有什么地标性建筑呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "ask_location", "当前用户意向": {"位置": null, "预算": 10000,
            "户型": "整租"}, "当前提问状态": {"位置": 0, "预算": 1, "房源类型": 1}}</input>

            <output>请问您想租房的位置在哪个区域？是否需要靠近地铁站？</output>

            </example>


            <example>

            <input>{"当前提问意图": "ask_location", "当前用户意向": {"位置": "", "预算": 6000, "户型":
            "合租"}, "当前提问状态": {"位置": 0, "预算": 1, "房源类型": 1}}</input>

            <output>方便告诉我您想找房的区域或者附近地标吗？</output>

            </example>

            </examples>'
        - id: 3fe0d11e-4b1a-4ca7-8f1d-1cc90622730d
          role: user
          text: '用户当前意向：

            位置：{{#conversation.location#}}

            位置类型： {{#conversation.location_type#}}  如果为null的话 默认有商圈，地铁,  小区 ，通勤，公司

            当前提问状态：

            位置: {{#conversation.location_asked#}}  0代表未提问，1代表已经提问'
        selected: false
        title: 聊天(位置分支)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741229452581'
      position:
        x: 5198
        y: 816.3369917993924
      positionAbsolute:
        x: 5198
        y: 816.3369917993924
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\ndef main() -> dict:\n    return {\n        \"result\": \"finding_house_complete\"\
          ,\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 更新找房信息状态为完成
        type: code
        variables: []
      height: 54
      id: '1741229953271'
      position:
        x: 4894
        y: 670.5
      positionAbsolute:
        x: 4894
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1741229953271'
          - result
          variable_selector:
          - conversation
          - session_state
          write_mode: over-write
        selected: false
        title: 更新找房信息收集状态变量
        type: assigner
        version: '2'
      height: 88
      id: '1741230014416'
      position:
        x: 5198
        y: 670.5
      positionAbsolute:
        x: 5198
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: idle
          variable_selector:
          - conversation
          - session_state
          write_mode: over-write
        - input_type: constant
          operation: set
          value: 'null'
          variable_selector:
          - conversation
          - transport_minutes
          write_mode: over-write
        - input_type: constant
          operation: set
          value: 'null'
          variable_selector:
          - conversation
          - transport
          write_mode: over-write
        selected: false
        title: 变量赋值 15
        type: assigner
        version: '2'
      height: 144
      id: '1741252220821'
      position:
        x: 6718
        y: 854.5
      positionAbsolute:
        x: 6718
        y: 854.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741229452581.text#}}'
        desc: ''
        selected: false
        title: 直接回复 4
        type: answer
        variables: []
      height: 103
      id: '1741328190568'
      position:
        x: 5502
        y: 836.5
      positionAbsolute:
        x: 5502
        y: 836.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(location, budget, room_type, location_asked, budget_asked,\
          \ room_type_asked):\n    \"\"\"\n    判断是否可以直接调用房源搜索接口\n    \n    返回:\n \
          \   dict: {\"result\": \"search\"} 表示可以直接搜索\n          {\"result\": \"ask_xxx\"\
          } 表示需要询问某个字段\n    \"\"\"\n    def is_empty(value):\n        return value\
          \ is None or value == \"\" or value == \"null\"\n    \n    # 检查位置：如果为空且未询问，则需询问\n\
          \    if is_empty(location) and location_asked == 0:\n        return {\"\
          result\": \"ask_location\"}\n    \n    # 检查预算：如果为空且未询问，则需询问\n    if is_empty(budget)\
          \ and budget_asked == 0:\n        return {\"result\": \"ask_budget\"}\n\
          \    \n    # 检查房型：如果为空且未询问，则需询问\n    if is_empty(room_type) and room_type_asked\
          \ == 0:\n        return {\"result\": \"ask_room_type\"}\n    \n    # 所有字段要么有值，要么已询问过，可以直接搜索\n\
          \    return {\"result\": \"search\"}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 判断提问意图
        type: code
        variables:
        - value_selector:
          - conversation
          - location
          variable: location
        - value_selector:
          - conversation
          - budget
          variable: budget
        - value_selector:
          - conversation
          - room_type
          variable: room_type
        - value_selector:
          - conversation
          - location_asked
          variable: location_asked
        - value_selector:
          - conversation
          - budget_asked
          variable: budget_asked
        - value_selector:
          - conversation
          - room_type_asked
          variable: room_type_asked
      height: 54
      id: '1741424186193'
      position:
        x: 4286
        y: 670.5
      positionAbsolute:
        x: 4286
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: ca9fbf71-4ec3-4c66-9a2c-6cfb774d6e48
            value: ask_location
            varType: string
            variable_selector:
            - '1741424186193'
            - result
          - comparison_operator: '='
            id: b3412ccd-0ce1-48f9-9202-e712660d91d5
            value: '0'
            varType: number
            variable_selector:
            - conversation
            - location_asked
          id: 'true'
          logical_operator: and
        - case_id: 7971f6da-a712-4b17-a02a-9535b4209003
          conditions:
          - comparison_operator: contains
            id: 68155e6e-8e3c-4832-aee7-5d7ba76565cd
            value: ask_budget
            varType: string
            variable_selector:
            - '1741424186193'
            - result
          - comparison_operator: '='
            id: 911e54e8-770f-4678-9339-b745be5e6ab2
            value: '0'
            varType: number
            variable_selector:
            - conversation
            - budget_asked
          id: 7971f6da-a712-4b17-a02a-9535b4209003
          logical_operator: and
        - case_id: 8b57cd4b-ab92-4f1d-b10e-1a930393e30b
          conditions:
          - comparison_operator: contains
            id: c9a464c1-b226-4f8a-b63a-103d7bfd857d
            value: ask_room_type
            varType: string
            variable_selector:
            - '1741424186193'
            - result
          - comparison_operator: '='
            id: 76127d79-08ed-42f1-b8d2-64b15ff8af58
            value: '0'
            varType: number
            variable_selector:
            - conversation
            - room_type_asked
          id: 8b57cd4b-ab92-4f1d-b10e-1a930393e30b
          logical_operator: and
        - case_id: 8aec67f6-494f-406e-97bd-5729465dc5d9
          conditions:
          - comparison_operator: contains
            id: f677be17-8779-46f4-a838-8a4d321bcb3a
            value: search
            varType: string
            variable_selector:
            - '1741424186193'
            - result
          id: 8aec67f6-494f-406e-97bd-5729465dc5d9
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 8
        type: if-else
      height: 348
      id: '1741331950830'
      position:
        x: 4590
        y: 670.5
      positionAbsolute:
        x: 4590
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: 1
          variable_selector:
          - conversation
          - location_asked
          write_mode: over-write
        selected: false
        title: 已经位置提问
        type: assigner
        version: '2'
      height: 88
      id: '1741332011541'
      position:
        x: 4894
        y: 808
      positionAbsolute:
        x: 4894
        y: 808
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: 1
          variable_selector:
          - conversation
          - budget_asked
          write_mode: over-write
        selected: false
        title: 已经提问预算
        type: assigner
        version: '2'
      height: 88
      id: '1741332036940'
      position:
        x: 4894
        y: 951
      positionAbsolute:
        x: 4894
        y: 951
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: 1
          variable_selector:
          - conversation
          - room_type_asked
          write_mode: over-write
        selected: false
        title: 已经提问房源类型
        type: assigner
        version: '2'
      height: 88
      id: '1741332099371'
      position:
        x: 4894
        y: 1094
      positionAbsolute:
        x: 4894
        y: 1094
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: idle
          variable_selector:
          - conversation
          - session_state
          write_mode: over-write
        - input_type: constant
          operation: set
          value: 'null'
          variable_selector:
          - conversation
          - transport_minutes
          write_mode: over-write
        - input_type: constant
          operation: set
          value: 'null'
          variable_selector:
          - conversation
          - transport
          write_mode: over-write
        selected: false
        title: 变量赋值 10
        type: assigner
        version: '2'
      height: 144
      id: '1741334005357'
      position:
        x: 6718
        y: 670.5
      positionAbsolute:
        x: 6718
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: ' 当前执行遇到点问题，请联系工作人员 :  {{#1741404955681.error_message#}}'
        desc: ''
        selected: false
        title: 直接回复 4
        type: answer
        variables: []
      height: 119
      id: '1741334108475'
      position:
        x: 7022
        y: 670.5
      positionAbsolute:
        x: 7022
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(arg1: str) -> dict:\n    o = json.loads(arg1)\n\
          \    return {\n        \"result\": o[\"text\"]\n    }\n"
        code_language: python3
        desc: ''
        error_strategy: fail-branch
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码执行 3
        type: code
        variables:
        - value_selector:
          - '1741404955681'
          - text
          variable: arg1
      height: 90
      id: '1741335613462'
      position:
        x: 7022
        y: 869
      positionAbsolute:
        x: 7022
        y: 869
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_strategy: fail-branch
        provider_id: cfaec52c-e681-44f0-ae16-5d685388e271
        provider_name: AI找房｜房源搜索
        provider_type: workflow
        selected: false
        title: AI找房｜房源搜索
        tool_configurations: {}
        tool_label: AI找房｜房源搜索
        tool_name: house_search
        tool_parameters:
          area:
            type: mixed
            value: '{{#conversation.area#}}'
          bedroom:
            type: mixed
            value: '{{#conversation.bed_room#}}'
          checkin_people_num:
            type: mixed
            value: ''
          distance:
            type: variable
            value:
            - conversation
            - distance
          face:
            type: mixed
            value: '{{#conversation.face#}}'
          leasetype:
            type: mixed
            value: '{{#conversation.leasetype#}}'
          location:
            type: mixed
            value: '{{#conversation.location#}}'
          location_type:
            type: mixed
            value: ''
          minute:
            type: mixed
            value: '{{#conversation.transport_minutes#}}'
          page:
            type: variable
            value:
            - conversation
            - current_page
          price:
            type: mixed
            value: '{{#conversation.budget#}}'
          transport:
            type: mixed
            value: '{{#conversation.transport#}}'
          type:
            type: mixed
            value: '{{#conversation.room_type#}}'
        type: tool
      height: 90
      id: '1741404955681'
      position:
        x: 6417.873452289822
        y: 670.5
      positionAbsolute:
        x: 6417.873452289822
        y: 670.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: 没有找到合适的房源，请换个条件试试呢~
        desc: ''
        selected: false
        title: 直接回复 6
        type: answer
        variables: []
      height: 116
      id: '1741416716197'
      position:
        x: 7326
        y: 997.5
      positionAbsolute:
        x: 7326
        y: 997.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_template:
        - id: b9e3443a-3299-4e9e-83ec-8b744ea36829
          role: system
          text: '<instructions>

            根据用户当前的预算信息和提问状态，生成自然友好的预算询问提示。


            输入信息：

            - 用户当前预算信息 (budget)

            - 预算提问状态 (budget_asked)

            - 其他用户意向信息 (如位置、房型)


            处理逻辑：

            如果当前提问意图明确为"ask_budget"或(budget为空)，则生成预算提问语句。

            提问应当自然友好，可根据用户已提供的其他信息进行个性化提问。


            预算提问变体：

            1. 基础提问："请问您的租房预算大概是多少呢？"

            2. 范围引导："您的月租预算是在什么价位？3000以下、3000-5000还是5000以上？"

            3. 关联房型："对于{户型}，您的预算范围是？"

            4. 关联位置："在{位置}租{户型}，您的预算大概是多少呢？"

            5. 开放式："您对房租有什么要求或限制吗？"


            注意：

            1. 只生成一句话的提问

            2. 使用自然、亲切的语气

            3. 不要包含技术词汇如"null"或"undefined"

            4. 不要输出变量名称

            5. 个性化提问时，只使用已有的有效信息

            </instructions>


            <examples>

            <example>

            <input>{"当前提问意图": "ask_budget", "当前用户意向": {"位置": "朝阳区", "预算": null, "户型":
            "整租"}, "当前提问状态": {"位置": 1, "预算": 0, "房源类型": 1}}</input>

            <output>请问您在朝阳区整租的预算大概是多少呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "", "当前用户意向": {"位置": "海淀区", "预算": "", "户型": ""}, "当前提问状态":
            {"位置": 1, "预算": 0, "房源类型": 0}}</input>

            <output>您在海淀区的租房预算大概是多少呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "ask_budget", "当前用户意向": {"位置": "望京", "预算": null, "户型":
            "合租"}, "当前提问状态": {"位置": 1, "预算": 0, "房源类型": 1}}</input>

            <output>在望京合租，您的月租预算是什么范围呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "ask_budget", "当前用户意向": {"位置": "天通苑", "预算": "", "户型":
            ""}, "当前提问状态": {"位置": 1, "预算": 0, "房源类型": 0}}</input>

            <output>天通苑的房源价格区间较广，您的租金预算大概是多少呢？</output>

            </example>

            </examples>'
        - id: 89068814-d947-42a6-a803-5b7ff5047e1e
          role: user
          text: '用户当前意向：

            预算：{{#conversation.budget#}}


            当前提问状态：

            预算： {{#conversation.budget_asked#}} 0代表未提问，1代表已经提问'
        selected: false
        title: 聊天(预算分支)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741426845734'
      position:
        x: 5198
        y: 963
      positionAbsolute:
        x: 5198
        y: 963
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_template:
        - id: 8a991264-f92d-42b2-a970-f6b9f0011cea
          role: system
          text: '<instructions>

            根据用户当前的房型信息和提问状态，生成自然友好的房型询问提示。


            输入信息：

            - 用户当前房型信息 (room_type)

            - 房型提问状态 (room_type_asked)

            - 其他用户意向信息 (如位置、预算)


            处理逻辑：

            如果当前提问意图明确为"ask_room_type"或(room_type为空且位置和预算信息已获取)，则生成房型提问语句。

            提问应当自然友好，可根据用户已提供的其他信息进行个性化提问。


            房型提问变体：

            1. 基础提问："请问您需要合租还是整租呢？"

            2. 居室详询："您需要几居室的房子？是一居、两居还是更大的户型？"

            3. 预算关联："以您{预算}的预算，您是考虑合租还是整租呢？"

            4. 位置关联："在{位置}，您是需要合租的单间，还是整租一套房子呢？"

            5. 组合提问："您是想要整租还是合租？需要几居室的房子呢？"

            6. 生活习惯："您是独自居住还是和他人合住？"


            注意：

            1. 只生成一句话的提问

            2. 使用自然、亲切的语气

            3. 不要包含技术词汇如"null"或"undefined"

            4. 不要输出变量名称

            5. 个性化提问时，只使用已有的有效信息

            </instructions>


            <examples>

            <example>

            <input>{"当前提问意图": "ask_room_type", "当前用户意向": {"位置": "海淀区", "预算": 6000,
            "户型": ""}, "当前提问状态": {"位置": 1, "预算": 1, "房源类型": 0}}</input>

            <output>请问您需要合租还是整租呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "", "当前用户意向": {"位置": "望京", "预算": "5000,8000", "户型":
            null}, "当前提问状态": {"位置": 1, "预算": 1, "房源类型": 0}}</input>

            <output>在望京5000-8000的预算下，您是考虑合租还是整租呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "ask_room_type", "当前用户意向": {"位置": "百度", "预算": "0,6000",
            "户型": ""}, "当前提问状态": {"位置": 1, "预算": 1, "房源类型": 0}}</input>

            <output>百度附近6000以内的房源，您是需要合租单间还是整租一套房子呢？</output>

            </example>


            <example>

            <input>{"当前提问意图": "ask_room_type", "当前用户意向": {"位置": "天通苑", "预算": "3000,5000",
            "户型": null}, "当前提问状态": {"位置": 1, "预算": 1, "房源类型": 0}}</input>

            <output>天通苑3000-5000的价位，您更倾向于合租还是整租？需要几居室的房源呢？</output>

            </example>

            </examples>'
        - id: bf6d0d79-8daf-41f4-a393-249ae777f50e
          role: user
          text: '用户当前意向：

            房源类型：{{#conversation.room_type#}}

            当前提问状态：

            房源类型： {{#conversation.room_type_asked#}}  0代表未提问，1代表已经提问'
        selected: false
        title: 聊天(房源类型分支)
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741426851212'
      position:
        x: 5198
        y: 1104.1684958996962
      positionAbsolute:
        x: 5198
        y: 1104.1684958996962
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741426845734.text#}}'
        desc: ''
        selected: false
        title: 直接回复 7
        type: answer
        variables: []
      height: 103
      id: '1741426880245'
      position:
        x: 5502
        y: 977.6659551351727
      positionAbsolute:
        x: 5502
        y: 977.6659551351727
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741426851212.text#}}'
        desc: ''
        selected: false
        title: 直接回复 8
        type: answer
        variables: []
      height: 103
      id: '1741426915644'
      position:
        x: 5502
        y: 1122.5
      positionAbsolute:
        x: 5502
        y: 1122.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: 89d137b4-7aa4-4670-a95d-375ad2e2ea53
        provider_name: AI 找房 | 小区问答
        provider_type: workflow
        selected: false
        title: AI 找房 | 小区问答
        tool_configurations: {}
        tool_label: AI 找房 | 小区问答
        tool_name: resblock_qa
        tool_parameters:
          query:
            type: mixed
            value: '{{#sys.query#}}'
          reblockName:
            type: mixed
            value: '{{#1741430476606.reblockName#}}'
        type: tool
      height: 54
      id: '1741430434882'
      position:
        x: 1550
        y: 383
      positionAbsolute:
        x: 1550
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741430759517.result#}}'
        desc: ''
        selected: false
        title: 直接回复 9
        type: answer
        variables: []
      height: 103
      id: '1741430443136'
      position:
        x: 2158
        y: 383
      positionAbsolute:
        x: 2158
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: '根据用户输入，解析出小区名称。提取的参数不要包含 "小区" 等字样。

          <example>

          <input>潘家园小区</input>

          <output>潘家园</output>

          </example>'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        parameters:
        - description: 小区(楼盘)名称，需要精确。与resblockId必须二选一填入。
          name: reblockName
          options: []
          required: false
          type: string
        query:
        - sys
        - query
        reasoning_mode: prompt
        selected: false
        title: 参数提取器 2
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741430476606'
      position:
        x: 1246
        y: 383
      positionAbsolute:
        x: 1246
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\ndef main(arg1: str) -> dict:\n    data = json.loads(arg1)\n\
          \    return {\n        \"result\": data.get(\"text\")\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 小区回复格式化
        type: code
        variables:
        - value_selector:
          - '1741430434882'
          - text
          variable: arg1
      height: 54
      id: '1741430759517'
      position:
        x: 1854
        y: 383
      positionAbsolute:
        x: 1854
        y: 383
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741601374807.text#}}'
        desc: ''
        selected: false
        title: 直接回复 8
        type: answer
        variables: []
      height: 103
      id: '1741601549648'
      position:
        x: 1550
        y: 612.5
      positionAbsolute:
        x: 1550
        y: 612.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_config:
          jinja2_variables:
          - value_selector:
            - conversation
            - location
            variable: location
          - value_selector:
            - conversation
            - budget
            variable: budget
          - value_selector:
            - conversation
            - room_type
            variable: room_type
          - value_selector:
            - conversation
            - area
            variable: area
          - value_selector:
            - conversation
            - face
            variable: face
          - value_selector:
            - conversation
            - bed_room
            variable: bed_room
          - value_selector:
            - conversation
            - leasetype
            variable: leasetype
          - value_selector:
            - conversation
            - distance
            variable: distance
          - value_selector:
            - conversation
            - transport_minutes
            variable: transport_minutes
          - value_selector:
            - conversation
            - transport
            variable: transport
        prompt_template:
        - edition_type: jinja2
          id: 24156066-9ee6-40af-a2b4-e20c1af25133
          jinja2_text: "根据用户的查询参数生成一句话的看房单摘要，使用Markdown粗体和标签式展示。严格按照以下规则：\n\n1. 忽略值为\"\
            null\"或\"any\"的字段，仅包含有效信息\n2. 将有效信息按照重要性顺序组合成一句话\n3. 每个条件格式为\"值\"，使用\"\
            \ - \"分隔不同类型的条件\n4. 禁止输出任何额外注释、说明或提示性文字\n5. 输出内容仅限一句话，末尾添加换行符\n\n\n按照以下顺序和格式处理各字段：\n\
            \n位置(location): 具体位置，如\"回龙观\"\n房型(room_type): 房型，如\"整租\"、\"合租\"\n居室(bed_room):\
            \ X居室，如\"2居室\"\n预算(budget): \U0001F4B0价格区间，如\"\U0001F4B04000-9000元\"\n\
            面积(area): 面积区间，如\"60-80平米\"\n朝向(face): 朝向描述，根据编码转换：\n - 1: \"东向\"\n -\
            \ 2: \"南向\"\n - 3: \"西向\"\n - 4: \"北向\"\n租期(leasetype): 租期描述，精确对应：\n -\
            \ 1: \"短租\"\n - 2: \"长租\"\n 输出 \"短租\" 或者 \"长租\"\n距离(distance): 距离描述， {{\
            \ distance }}km内\n通勤方式(transport): 通勤方式描述，精确对应：\n - transit: \"公交\"\n\
            \ - ride: \"骑行\"\n - walk: \"步行\"\n - drive: \"驾车\"\n通勤时间(transport_minutes):\
            \ 通勤时间描述，如\"30分钟\"\n\n\n最终输出格式(仅一句话)：\n\"根据您的条件 [有效条件用标签式展示并用' - '连接]\
            \ ，正在极速查找房源中。\"\n\n严禁输出任何其他额外文字、注释或说明。\n{% raw %}\n\n\n\n\n\n\n{% endraw\
            \ %}\n"
          role: system
          text: ''
        - edition_type: jinja2
          id: 2ef77520-cf77-4bc1-aff1-5310bd58a45c
          jinja2_text: '看房单:



            位置：{{location}}

            预算： {{budget}}

            房源类型： {{room_type}}

            居室： {{bed_room}}

            面积： {{area}}

            朝向： {{face}}

            租期： {{leasetype}}

            距离： {{distance}}

            通勤方式： {{transport}}

            通勤时间： {{transport_minutes}}'
          role: user
          text: '看房单:



            位置：{{location}}

            预算： {{budget}}

            房源类型： {{room_type}}

            居室： {{bed_room}}

            面积： {{area}}

            朝向： {{face}}

            租期： {{leasetype}}

            距离： {{distance}}

            通勤方式： {{transport}}

            通勤时间： {{transport_minutes}}'
        selected: false
        title: 找房单输出
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741677322908'
      position:
        x: 5807.834044864827
        y: 670.5
      positionAbsolute:
        x: 5807.834044864827
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741677322908.text#}}'
        desc: ''
        selected: false
        title: 直接回复 9
        type: answer
        variables: []
      height: 103
      id: '1741677692698'
      position:
        x: 6110
        y: 670.5
      positionAbsolute:
        x: 6110
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: 303d0744-db06-4649-ab8e-1b1813f94d71
        provider_name: AI找房｜地理类型分析
        provider_type: workflow
        selected: false
        title: AI找房｜地理类型分析
        tool_configurations: {}
        tool_label: AI找房｜地理类型分析
        tool_name: getLocationInfoList
        tool_parameters:
          location:
            type: mixed
            value: '{{#conversation.location#}}'
        type: tool
      height: 54
      id: '1741679821876'
      position:
        x: 3070
        y: 670.5
      positionAbsolute:
        x: 3070
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: not contains
            id: 5b944df6-812b-4c55-a811-fb308d6a36d4
            value: 'null'
            varType: string
            variable_selector:
            - conversation
            - location
          - comparison_operator: not empty
            id: 23234a4f-13b8-4388-ad2f-b8418f34a74f
            value: ''
            varType: string
            variable_selector:
            - conversation
            - location
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 判断能否提取位置
        type: if-else
      height: 152
      id: '1741680284878'
      position:
        x: 2766
        y: 501.5
      positionAbsolute:
        x: 2766
        y: 501.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(location_type_list: list) -> dict:\n    # 检查列表是否为空\n    if\
          \ not location_type_list or len(location_type_list) == 0:\n        return\
          \ {\n            \"result\": \"\"\n        }\n    \n    # 获取第一个元素（字典）\n\
          \    first_dict = location_type_list[0]\n    \n    # 从字典中获取result键对应的列表\n\
          \    location_types = first_dict.get(\"result\", [])\n    \n    # 检查result列表是否为空\n\
          \    if not location_types or len(location_types) == 0:\n        return\
          \ {\n            \"result\": \"\"\n        }\n    \n    # 获取result列表的第一个元素\n\
          \    first_item = location_types[0]\n    \n    return {\n        \"result\"\
          : first_item\n    }"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 解析地理位置
        type: code
        variables:
        - value_selector:
          - '1741679821876'
          - json
          variable: location_type_list
      height: 54
      id: '1741680436852'
      position:
        x: 3377.850684140845
        y: 670.5
      positionAbsolute:
        x: 3377.850684140845
        y: 670.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: e98439cf-a22b-429d-a17e-3f926df15502
            value: ''
            varType: string
            variable_selector:
            - '1741680436852'
            - result
          - comparison_operator: contains
            id: 2bca1b81-9578-468b-9404-339ea1d6ea00
            value: 行政区
            varType: string
            variable_selector:
            - '1741680436852'
            - result
          - comparison_operator: contains
            id: 76f8ac06-f185-4007-bcd7-794b99682ac2
            value: 地铁线
            varType: string
            variable_selector:
            - '1741680436852'
            - result
          id: 'true'
          logical_operator: or
        - case_id: d5cbb985-b791-4bef-a6eb-90c8c95bf3d4
          conditions:
          - comparison_operator: contains
            id: 8e7232a8-00ae-40c2-af00-7782c1a9bcf9
            value: 公司
            varType: string
            variable_selector:
            - '1741680436852'
            - result
          - comparison_operator: '='
            id: a7e3c2f5-db10-4f99-be86-a265b5cc9908
            value: '0'
            varType: number
            variable_selector:
            - conversation
            - company_asked
          - comparison_operator: empty
            id: 1b98d526-ae18-4264-83ba-ba9e070f2813
            value: ''
            varType: string
            variable_selector:
            - conversation
            - transport
          id: d5cbb985-b791-4bef-a6eb-90c8c95bf3d4
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 14
        type: if-else
      height: 278
      id: '1741680966143'
      position:
        x: 4006.591713352107
        y: 515.1890729859181
      positionAbsolute:
        x: 4006.591713352107
        y: 515.1890729859181
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_template:
        - id: 9c496677-f336-4683-ae53-5601fffd6870
          role: system
          text: '当前位置：{{#conversation.location#}}

            当前识别位置类型： {{#conversation.location_type#}}

            如果识别到位置类型是行政区，地铁线的话，进行追问，{{#conversation.location#}}挺大的，您有意向区域吗？

            如果识别到的位置类型为"" 并且位置是"城市"，进行追问：{{#conversation.location#}}挺大的，您有意向区域吗？

            不要输出无关内容。

            示例：

            北京挺大的，您有意向区域吗？

            14号线范围挺广的，您有意向区域吗？

            朝阳挺大的，您有意向区域吗？'
        selected: false
        title: 缩小范围追问
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741681038156'
      position:
        x: 4590
        y: 1371.5
      positionAbsolute:
        x: 4590
        y: 1371.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741681038156.text#}}'
        desc: ''
        selected: false
        title: 直接回复 10
        type: answer
        variables: []
      height: 103
      id: '1741681067448'
      position:
        x: 4894
        y: 1222
      positionAbsolute:
        x: 4894
        y: 1222
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: hunyuan-turbos-latest
          provider: openai_api_compatible
        prompt_template:
        - id: 6f3b1663-3de3-4b9d-9b49-a0a479a73795
          role: system
          text: '当前位置:{{#conversation.location#}}

            追问用户：请问您是那种通勤方式呢（公交/地铁，步行，骑行，驾车），以及对通勤时长的要求'
        selected: false
        title: 通勤找房追问
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1741692846302'
      position:
        x: 4286
        y: 1363.1659551351727
      positionAbsolute:
        x: 4286
        y: 1363.1659551351727
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1741692846302.text#}}'
        desc: ''
        selected: false
        title: 直接回复 11
        type: answer
        variables: []
      height: 103
      id: '1741692872205'
      position:
        x: 4590
        y: 1509.5
      positionAbsolute:
        x: 4590
        y: 1509.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: 1
          variable_selector:
          - conversation
          - company_asked
          write_mode: over-write
        selected: false
        title: 更新公司提问
        type: assigner
        version: '2'
      height: 88
      id: '1741694743815'
      position:
        x: 3982
        y: 1406
      positionAbsolute:
        x: 3982
        y: 1406
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: '='
            id: 71d3d12a-4640-484a-b57d-0df24ba4af1c
            value: '0'
            varType: number
            variable_selector:
            - conversation
            - region_asked
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 判断地域
        type: if-else
      height: 126
      id: '1741694866202'
      position:
        x: 3982
        y: 1226.5
      positionAbsolute:
        x: 3982
        y: 1226.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: constant
          operation: set
          value: 1
          variable_selector:
          - conversation
          - region_asked
          write_mode: over-write
        selected: false
        title: 赋值位置缩小范围追问
        type: assigner
        version: '2'
      height: 88
      id: '1741694916618'
      position:
        x: 4286
        y: 1229.5
      positionAbsolute:
        x: 4286
        y: 1229.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(\n    # 提取的参数\n    location: str = \"null\",\n    location_type:\
          \ str = \"null\",\n    budget: str = \"null\",\n    room_type: str = \"\
          null\",\n    area: str = \"null\",\n    bed_room: str = \"null\",\n    face:\
          \ str = \"null\",\n    room_or_resblock: str = \"null\",\n    leasetype:\
          \ int = None,  # 修改为int类型，默认值为None\n    distance: float = None,  # 修改为float类型，默认值为None\n\
          \    transport: str = \"null\",\n    transport_minutes: str = \"null\",\n\
          \    current_page: int = 1,  # 已经是int类型\n    # 全局参数\n    global_location:\
          \ str = \"null\",\n    global_location_type: str = \"null\",\n    global_budget:\
          \ str = \"null\",\n    global_room_type: str = \"null\",\n    global_area:\
          \ str = \"null\",\n    global_bed_room: str = \"null\",\n    global_face:\
          \ str = \"null\",\n    global_room_or_resblock: str = \"null\",\n    global_leasetype:\
          \ int = None,  # 修改为int类型\n    global_distance: float = None,  # 修改为float类型\n\
          \    global_transport: str = \"null\",\n    global_transport_minutes: str\
          \ = \"null\",\n    global_current_page: int = 1  # 修改为int，默认值1\n) -> dict:\n\
          \    \"\"\"\n    根据特定规则更新会话参数\n    \n    Args:\n        各个提取的参数和全局参数\n \
          \       \n    Returns:\n        dict: 包含更新后参数的字典\n    \"\"\"\n    result\
          \ = {}\n    \n    # 1. 特殊处理location、budget和room_type\n    # location特殊处理\n\
          \    if location != \"null\" and (global_location == \"null\" or\n     \
          \                        (location != \"null\" and global_location != \"\
          null\")):\n        result[\"location\"] = location\n    else:\n        result[\"\
          location\"] = global_location\n        \n    # budget特殊处理\n    if budget\
          \ != \"null\" and (global_budget == \"null\" or\n                      \
          \     (budget != \"null\" and global_budget != \"null\")):\n        if budget\
          \ == \"any\" or \"unlimited\" in str(budget).lower():\n            result[\"\
          budget\"] = \"null\"\n        else:\n            result[\"budget\"] = budget\n\
          \    else:\n        result[\"budget\"] = global_budget\n        \n    #\
          \ room_type特殊处理\n    if room_type != \"null\" and (global_room_type == \"\
          null\" or\n                              (room_type != \"null\" and global_room_type\
          \ != \"null\")):\n        result[\"room_type\"] = room_type\n    else:\n\
          \        result[\"room_type\"] = global_room_type\n    \n    # 2. 特殊处理current_page\
          \ - 保持为整数类型\n    try:\n        if current_page > 1:\n            # 增加全局页码\n\
          \            global_page = global_current_page if isinstance(global_current_page,\
          \ int) else 1\n            result[\"current_page\"] = global_page + 1\n\
          \        else:\n            result[\"current_page\"] = current_page if current_page\
          \ != None else global_current_page\n    except:\n        result[\"current_page\"\
          ] = global_current_page\n    \n    # 3. 处理其他普通字符串参数\n    param_pairs = [\n\
          \        (\"location_type\", location_type, global_location_type),\n   \
          \     (\"area\", area, global_area),\n        (\"bed_room\", bed_room, global_bed_room),\n\
          \        (\"face\", face, global_face),\n        (\"room_or_resblock\",\
          \ room_or_resblock, global_room_or_resblock),\n        (\"transport\", transport,\
          \ global_transport),\n        (\"transport_minutes\", transport_minutes,\
          \ global_transport_minutes)\n    ]\n    \n    for param_name, param_value,\
          \ global_value in param_pairs:\n        if param_value != \"null\":\n  \
          \          result[param_name] = param_value\n        else:\n           \
          \ result[param_name] = global_value\n    \n    # 4. 单独处理数值类型参数\n    # leasetype处理\n\
          \    if leasetype is not None:\n        result[\"leasetype\"] = leasetype\n\
          \    else:\n        result[\"leasetype\"] = global_leasetype\n        \n\
          \    # distance处理\n    if distance is not None:\n        result[\"distance\"\
          ] = distance\n    else:\n        result[\"distance\"] = global_distance\n\
          \        \n    return result"
        code_language: python3
        desc: ''
        outputs:
          area:
            children: null
            type: string
          bed_room:
            children: null
            type: string
          budget:
            children: null
            type: string
          current_page:
            children: null
            type: number
          distance:
            children: null
            type: number
          face:
            children: null
            type: string
          leasetype:
            children: null
            type: string
          location:
            children: null
            type: string
          location_type:
            children: null
            type: string
          room_or_resblock:
            children: null
            type: string
          room_type:
            children: null
            type: string
          transport:
            children: null
            type: string
          transport_minutes:
            children: null
            type: string
        selected: false
        title: 参数变更
        type: code
        variables:
        - value_selector:
          - '1741228637950'
          - location
          variable: location
        - value_selector:
          - '1741228637950'
          - budget
          variable: budget
        - value_selector:
          - '1741228637950'
          - room_type
          variable: room_type
        - value_selector:
          - '1741228637950'
          - location_type
          variable: location_type
        - value_selector:
          - '1741228637950'
          - bed_room
          variable: bed_room
        - value_selector:
          - '1741228637950'
          - area
          variable: area
        - value_selector:
          - '1741228637950'
          - face
          variable: face
        - value_selector:
          - '1741228637950'
          - current_page
          variable: current_page
        - value_selector:
          - '1741228637950'
          - room_or_resblock
          variable: room_or_resblock
        - value_selector:
          - '1741228637950'
          - leasetype
          variable: leasetype
        - value_selector:
          - '1741228637950'
          - distance
          variable: distance
        - value_selector:
          - '1741228637950'
          - transport
          variable: transport
        - value_selector:
          - '1741228637950'
          - transport_minutes
          variable: transport_minutes
        - value_selector:
          - conversation
          - transport_minutes
          variable: global_transport_minutes
        - value_selector:
          - conversation
          - transport
          variable: global_transport
        - value_selector:
          - conversation
          - distance
          variable: global_distance
        - value_selector:
          - conversation
          - leasetype
          variable: global_leasetype
        - value_selector:
          - conversation
          - room_or_resblock
          variable: global_room_or_resblock
        - value_selector:
          - conversation
          - current_page
          variable: global_current_page
        - value_selector:
          - conversation
          - face
          variable: global_face
        - value_selector:
          - conversation
          - area
          variable: global_area
        - value_selector:
          - conversation
          - bed_room
          variable: global_bed_room
        - value_selector:
          - conversation
          - location_type
          variable: global_location_type
        - value_selector:
          - conversation
          - budget
          variable: global_budget
        - value_selector:
          - conversation
          - room_type
          variable: global_room_type
        - value_selector:
          - conversation
          - location
          variable: global_location
      height: 54
      id: '1741773949397'
      position:
        x: 2158
        y: 526
      positionAbsolute:
        x: 2158
        y: 526
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - transport_minutes
          variable_selector:
          - conversation
          - transport_minutes
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - transport
          variable_selector:
          - conversation
          - transport
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - distance
          variable_selector:
          - conversation
          - distance
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - leasetype
          variable_selector:
          - conversation
          - leasetype
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - room_or_resblock
          variable_selector:
          - conversation
          - room_or_resblock
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - current_page
          variable_selector:
          - conversation
          - current_page
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - face
          variable_selector:
          - conversation
          - face
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - area
          variable_selector:
          - conversation
          - area
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - bed_room
          variable_selector:
          - conversation
          - bed_room
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - location_type
          variable_selector:
          - conversation
          - location_type
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - budget
          variable_selector:
          - conversation
          - budget
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - room_type
          variable_selector:
          - conversation
          - room_type
          write_mode: over-write
        - input_type: variable
          operation: over-write
          value:
          - '1741773949397'
          - location
          variable_selector:
          - conversation
          - location
          write_mode: over-write
        selected: false
        title: 变量赋值 10
        type: assigner
        version: '2'
      height: 424
      id: '1741775165498'
      position:
        x: 2462
        y: 501.5
      positionAbsolute:
        x: 2462
        y: 501.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -3683.181045686615
      y: -275.33992373987746
      zoom: 0.738692194951157
