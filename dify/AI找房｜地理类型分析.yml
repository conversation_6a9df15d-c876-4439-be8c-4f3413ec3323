app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: AI找房｜地理类型分析
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: tool
      id: 1741421126106-source-1741421183286-target
      source: '1741421126106'
      sourceHandle: source
      target: '1741421183286'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: 1741421315456-source-1741421515923-target
      source: '1741421315456'
      sourceHandle: source
      target: '1741421515923'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: tool
        targetType: code
      id: 1741421183286-source-1741421315456-target
      source: '1741421183286'
      sourceHandle: source
      target: '1741421315456'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 地点信息
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: location
      height: 90
      id: '1741421126106'
      position:
        x: 30
        y: 245
      positionAbsolute:
        x: 30
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        provider_id: 03e6426d-414c-4c86-a42c-dfe7fe4c299c
        provider_name: 自如APP房源关键字搜索位置接口-二次封装
        provider_type: api
        selected: false
        title: searchSug
        tool_configurations: {}
        tool_label: searchSug
        tool_name: searchSug
        tool_parameters:
          city_code:
            type: mixed
            value: '110000'
          keyword:
            type: mixed
            value: '{{#1741421126106.location#}}'
          sug_type_name:
            type: mixed
            value: ''
        type: tool
      height: 54
      id: '1741421183286'
      position:
        x: 334
        y: 245
      positionAbsolute:
        x: 334
        y: 245
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\ndef main(arg1: str) -> dict:\n    inner_resp = json.loads(arg1)\n\
          \n    items = inner_resp[\"data\"][\"items\"]\n\n    type_names = list(dict.fromkeys([item[\"\
          type_name\"] for item in items]))\n    \n    return {\n        \"type_names\"\
          : type_names\n    }\n"
        code_language: python3
        desc: ''
        outputs:
          type_names:
            children: null
            type: array[string]
        selected: false
        title: 代码执行 2
        type: code
        variables:
        - value_selector:
          - '1741421183286'
          - text
          variable: arg1
      height: 54
      id: '1741421315456'
      position:
        x: 635.1428571428571
        y: 245
      positionAbsolute:
        x: 635.1428571428571
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1741421315456'
          - type_names
          variable: result
        selected: false
        title: 结束
        type: end
      height: 90
      id: '1741421515923'
      position:
        x: 944.8571428571429
        y: 245
      positionAbsolute:
        x: 944.8571428571429
        y: 245
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 293
      y: 122.00000000000006
      zoom: 0.7
