# AI找房系统

一个基于人工智能的对话式找房系统，帮助用户通过自然语言交互来寻找合适的房源。

## 功能特点

- 自然语言交互：用户可以用自然语言描述找房需求
- 意图识别：系统能够理解用户的意图（找房、询问细节等）
- 参数提取：从用户输入中提取关键找房参数（地段、价格、户型等）
- 会话管理：保持对话上下文，记住用户的偏好
- 流式响应：支持类似OpenAI的流式SSE响应
- 提示模板版本控制：支持不同版本的提示模板

## 技术栈

- **后端**：Python + FastAPI
- **数据库**：SQLite
- **AI集成**：OpenAI API (GPT-3.5/GPT-4)
- **前端**：HTML + CSS + JavaScript

## 项目结构

```
ai_house_search/
├── app/                           # 主应用目录
│   ├── api/                       # API层
│   │   ├── chat.py                # 聊天相关API
│   │   ├── session.py             # 会话管理API
│   │   └── house.py               # 房源相关API
│   ├── core/                      # 核心配置
│   │   ├── config.py              # 应用配置
│   │   └── logging.py             # 日志配置
│   ├── db/                        # 数据库层
│   │   ├── base.py                # 数据库基础设置
│   │   ├── models/                # 数据模型
│   │   │   ├── session.py         # 会话模型
│   │   │   └── message.py         # 消息模型
│   │   └── repositories/          # 数据访问层
│   │       ├── session_repo.py    # 会话数据访问
│   │       └── message_repo.py    # 消息数据访问
│   ├── llm/                       # LLM集成层
│   │   ├── client.py              # OpenAI客户端
│   │   ├── intent/                # 意图识别模块
│   │   │   ├── __init__.py
│   │   │   └── recognizer.py      # 意图识别器
│   │   ├── extraction/            # 参数提取模块
│   │   │   ├── __init__.py
│   │   │   └── extractor.py       # 参数提取器
│   │   ├── prompts/               # 提示模板(支持版本控制)
│   │   │   ├── intent/            # 意图识别提示
│   │   │   │   ├── __init__.py
│   │   │   │   └── v1.py
│   │   │   ├── extraction/        # 参数提取提示
│   │   │   │   ├── __init__.py
│   │   │   │   └── v1.py
│   │   │   └── response/          # 回复生成提示
│   │   │       ├── __init__.py
│   │   │       └── v1.py
│   │   └── response.py            # 回复生成
│   ├── services/                  # 业务逻辑层
│   │   ├── chat_service.py        # 聊天服务
│   │   ├── session_service.py     # 会话管理服务
│   │   ├── house_service.py       # 房源服务
│   │   └── context_service.py     # 上下文管理服务
│   ├── utils/                     # 工具函数
│   │   ├── validators.py          # 数据验证
│   │   └── helpers.py             # 辅助函数
│   └── main.py                    # 应用入口
├── static/                        # 静态文件
├── templates/                     # 模板文件
├── .env                           # 环境变量
├── requirements.txt               # 依赖管理
└── README.md                      # 项目说明
```

## 核心流程

1. 用户通过API发送消息
2. API层调用ChatService处理消息
3. ChatService使用IntentRecognizer识别意图
4. ChatService使用ParameterExtractor提取参数
5. ChatService更新会话上下文
6. ChatService生成回复
7. API层返回响应

## 快速开始

### 环境准备

1. 确保已安装Python 3.8+
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 配置环境变量：
   - 复制`.env.example`为`.env`
   - 填入OpenAI API密钥和其他配置

   ```
   # OpenAI API配置
   OPENAI_API_KEY=your_openai_api_key  # 必填
   OPENAI_MODEL=gpt-3.5-turbo          # 可选，默认为gpt-3.5-turbo
   OPENAI_API_BASE=https://api.openai.com/v1  # 可选，自定义API端点
   OPENAI_API_VERSION=2023-05-15       # 可选，API版本

   # 自如找房API配置
   HOUSE_API_BASE_URL=http://ai-finder-bff.uat.ziroom.com  # 自如找房API基础URL
   ```

### 激活虚拟环境

在运行应用或测试前，请先激活虚拟环境：

```bash
# 激活虚拟环境
source .venv/bin/activate

# 在Windows上使用
# .venv\Scripts\activate
```

### 运行应用

```bash
# 确保已激活虚拟环境
uvicorn app.main:app --reload
```

访问 http://localhost:8000 即可使用应用。

### API文档

访问 http://localhost:8000/api/docs 查看API文档。

### 运行测试

```bash
# 确保已激活虚拟环境

# 运行所有测试
python -m tests.run_tests

# 运行单个测试
python -m tests.run_tests test_api
python -m tests.run_tests test_flow
python -m tests.run_tests test_chat_flow
python -m tests.run_tests test_llm
python -m tests.run_tests test_config

# 或者直接运行单个测试模块
python -m tests.test_api
python -m tests.test_flow
python -m tests.test_chat_flow
python -m tests.test_llm
python -m tests.test_config
```

## 提示模板版本控制

系统支持不同版本的提示模板，可以通过配置文件指定使用的版本：

```python
# app/core/config.py
INTENT_PROMPT_VERSION: str = "v1"
EXTRACTION_PROMPT_VERSION: str = "v1"
RESPONSE_PROMPT_VERSION: str = "v1"
```

要添加新版本的提示模板，只需在相应目录下创建新的版本文件，并在`__init__.py`中注册：

```python
# app/llm/prompts/intent/__init__.py
INTENT_PROMPTS = {
    IntentPromptVersion.V1: V1_INTENT_PROMPT,
    IntentPromptVersion.V2: V2_INTENT_PROMPT,  # 新版本
}
```

## 上下文管理

系统支持灵活的上下文管理，可以配置哪些变量需要在对话开始时清除：

```python
# app/services/context_service.py
CLEAR_ON_NEW_DIALOG = {
    "location_asked", "budget_asked", "room_type_asked",
    "company_asked", "region_asked", "current_page"
}
```

## 流式响应

系统支持类似OpenAI的流式SSE响应，可以通过`/api/chat/stream`接口使用：

```javascript
const eventSource = new EventSource('/api/chat/stream');
eventSource.onmessage = (event) => {
    if (event.data === '[DONE]') {
        eventSource.close();
    } else {
        // 处理响应块
        console.log(event.data);
    }
};
```