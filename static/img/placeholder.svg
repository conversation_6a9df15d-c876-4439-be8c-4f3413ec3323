<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="200" fill="#f1f5f9"/>
  <text x="150" y="100" font-family="Arial" font-size="14" text-anchor="middle" fill="#94a3b8">加载中...</text>
  <g fill="#cbd5e1">
    <circle cx="130" cy="100" r="4">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="150" cy="100" r="4">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite" begin="0.2s"/>
    </circle>
    <circle cx="170" cy="100" r="4">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite" begin="0.4s"/>
    </circle>
  </g>
</svg>
