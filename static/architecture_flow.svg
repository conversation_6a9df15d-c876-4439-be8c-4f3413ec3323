<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .api-box { fill: #e1f5fe; }
      .service-box { fill: #e8f5e9; }
      .workflow-box { fill: #fff8e1; }
      .repository-box { fill: #f3e5f5; }
      .db-box { fill: #ede7f6; }
      .external-box { fill: #ffebee; }
      .arrow {
        stroke: #333;
        stroke-width: 2;
        marker-end: url(#arrowhead);
      }
      .label {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 20px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="400" y="30" class="title">AI House Search System Architecture</text>
  <text x="400" y="50" class="subtitle">Flow from API to Service to Repository</text>

  <!-- API Layer -->
  <rect x="50" y="80" width="700" height="80" class="box api-box" />
  <text x="400" y="110" class="label">API Layer</text>
  <text x="400" y="130" class="subtitle">app/api/chat.py, app/api/session.py, app/api/house.py</text>
  <rect x="100" y="100" width="120" height="40" class="box" />
  <text x="160" y="125" class="label">Chat API</text>
  <rect x="340" y="100" width="120" height="40" class="box" />
  <text x="400" y="125" class="label">Session API</text>
  <rect x="580" y="100" width="120" height="40" class="box" />
  <text x="640" y="125" class="label">House API</text>

  <!-- Service Layer -->
  <rect x="50" y="200" width="700" height="80" class="box service-box" />
  <text x="400" y="230" class="label">Service Layer</text>
  <text x="400" y="250" class="subtitle">app/services/chat_service.py, app/services/session_service.py, app/services/house_service.py</text>
  <rect x="100" y="220" width="120" height="40" class="box" />
  <text x="160" y="245" class="label">ChatService</text>
  <rect x="340" y="220" width="120" height="40" class="box" />
  <text x="400" y="245" class="label">SessionService</text>
  <rect x="580" y="220" width="120" height="40" class="box" />
  <text x="640" y="245" class="label">HouseService</text>

  <!-- Workflow Layer -->
  <rect x="50" y="320" width="700" height="80" class="box workflow-box" />
  <text x="400" y="350" class="label">Workflow Layer</text>
  <text x="400" y="370" class="subtitle">app/workflows/chat_workflow.py, app/workflows/factory.py, app/workflows/steps/</text>
  <rect x="100" y="340" width="120" height="40" class="box" />
  <text x="160" y="365" class="label">ChatWorkflow</text>
  <rect x="340" y="340" width="120" height="40" class="box" />
  <text x="400" y="365" class="label">WorkflowFactory</text>
  <rect x="580" y="340" width="120" height="40" class="box" />
  <text x="640" y="365" class="label">WorkflowSteps</text>

  <!-- Repository Layer -->
  <rect x="50" y="440" width="700" height="80" class="box repository-box" />
  <text x="400" y="470" class="label">Repository Layer</text>
  <text x="400" y="490" class="subtitle">app/db/repositories/session_repo.py, app/db/repositories/message_repo.py</text>
  <rect x="100" y="460" width="120" height="40" class="box" />
  <text x="160" y="485" class="label">SessionRepo</text>
  <rect x="340" y="460" width="120" height="40" class="box" />
  <text x="400" y="485" class="label">MessageRepo</text>
  <rect x="580" y="460" width="120" height="40" class="box" />
  <text x="640" y="485" class="label">HouseSearcher</text>

  <!-- Database & External API Layer -->
  <rect x="50" y="560" width="340" height="60" class="box db-box" />
  <text x="220" y="590" class="label">SQLite Database</text>
  <rect x="410" y="560" width="340" height="60" class="box external-box" />
  <text x="580" y="590" class="label">External House Search API</text>

  <!-- Arrows -->
  <!-- API to Service -->
  <line x1="160" y1="140" x2="160" y2="220" class="arrow" />
  <line x1="400" y1="140" x2="400" y2="220" class="arrow" />
  <line x1="640" y1="140" x2="640" y2="220" class="arrow" />

  <!-- Service to Workflow -->
  <line x1="160" y1="260" x2="160" y2="340" class="arrow" />
  <line x1="400" y1="260" x2="400" y2="340" class="arrow" />
  <line x1="640" y1="260" x2="640" y2="340" class="arrow" />

  <!-- Workflow to Repository -->
  <line x1="160" y1="380" x2="160" y2="460" class="arrow" />
  <line x1="400" y1="380" x2="400" y2="460" class="arrow" />
  <line x1="640" y1="380" x2="640" y2="460" class="arrow" />

  <!-- Repository to Database/External -->
  <line x1="160" y1="500" x2="160" y2="560" class="arrow" />
  <line x1="400" y1="500" x2="400" y2="560" class="arrow" />
  <line x1="640" y1="500" x2="640" y2="560" class="arrow" />

  <!-- Cross connections -->
  <line x1="220" y1="240" x2="340" y2="240" class="arrow" />
  <line x1="460" y1="240" x2="580" y2="240" class="arrow" />
  <line x1="220" y1="360" x2="340" y2="360" class="arrow" />
  <line x1="460" y1="360" x2="580" y2="360" class="arrow" />
</svg>
