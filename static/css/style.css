/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: 100vh;
    margin: 0;
    overflow: hidden;
    background-color: #f8f9fa;
    color: #333;
}

.container-fluid {
    height: 100vh;
    padding: 0;
}

.row {
    height: 100%;
    margin: 0;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 左侧边栏样式 */
.sidebar {
    background-color: #1E293B;
    color: white;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #2D3748;
    background-color: #0F172A;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.brand {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    width: 32px;
    height: 32px;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
}

#new-chat-btn {
    background-color: #3B82F6;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-weight: 500;
    transition: all 0.2s;
}

#new-chat-btn:hover {
    background-color: #2563EB;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#load-sessions-btn {
    background-color: #475569;
    border: none;
    border-radius: 6px;
    padding: 8px;
    transition: all 0.2s;
    color: white;
}

#load-sessions-btn:hover {
    background-color: #334155;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.loading-session {
    text-align: center;
    padding: 10px;
    color: #94A3B8;
    font-size: 0.9rem;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #2D3748;
    background-color: #1E293B;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #3B82F6;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    color: white;
    font-size: 1.2rem;
}

.user-name {
    font-weight: 500;
    color: #E2E8F0;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#switch-user-btn {
    background: none;
    border: none;
    color: #94A3B8;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

#switch-user-btn:hover {
    background-color: #2D3748;
    color: white;
}

.session-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
}

.session-item {
    padding: 12px 15px;
    margin-bottom: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    border-left: 3px solid transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.session-item:hover {
    background-color: #2D3748;
    border-left-color: #3B82F6;
}

.session-item.active {
    background-color: #3B82F6;
    border-left-color: white;
}

.session-item.deleting {
    opacity: 0.5;
    pointer-events: none;
}

.session-content {
    flex: 1;
    min-width: 0; /* 确保flex项可以缩小到比内容更小 */
}

.session-item .session-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.session-item .session-time {
    font-size: 0.8rem;
    color: #CBD5E1;
}

.session-actions {
    display: flex;
    align-items: center;
    margin-left: 8px;
    opacity: 0;
    transition: opacity 0.2s;
}

.session-item:hover .session-actions {
    opacity: 1;
}

.delete-session-btn {
    background: none;
    border: none;
    color: #CBD5E1;
    font-size: 0.9rem;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.delete-session-btn:hover {
    color: #EF4444;
    background-color: rgba(239, 68, 68, 0.1);
}

.session-item.active .delete-session-btn {
    color: white;
}

.session-item.active .delete-session-btn:hover {
    color: #FCA5A5;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-footer {
    padding: 15px;
    text-align: center;
    font-size: 0.8rem;
    color: #CBD5E1;
    border-top: 1px solid #2D3748;
    background-color: #0F172A;
}

.user-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.user-name {
    font-weight: 500;
}

/* 聊天区域样式 */
.chat-area {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0;
    background-color: #F8FAFC;
}

.chat-header {
    padding: 15px 20px;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E2E8F0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.chat-header h4 {
    margin: 0;
    font-weight: 600;
    color: #1E293B;
}

#session-state-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 12px;
}

.chat-actions button {
    margin-left: 8px;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.chat-actions button:hover {
    transform: translateY(-2px);
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #FFFFFF;
}

.message {
    margin-bottom: 24px;
    max-width: 85%;
    position: relative;
}

.user-message {
    margin-left: auto;
}

.assistant-message {
    margin-right: auto;
}

.system-message {
    margin: 0 auto 24px;
    max-width: 90%;
}

.message-content {
    padding: 14px 18px;
    border-radius: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    line-height: 1.5;
}

.user-message .message-content {
    background-color: #3B82F6;
    color: white;
    border-bottom-right-radius: 4px;
}

.assistant-message .message-content {
    background-color: #F1F5F9;
    color: #1E293B;
    border-bottom-left-radius: 4px;
}

/* 思考中状态 */
.assistant-message.thinking .message-content {
    background-color: #E0F2FE;
    border-left: 3px solid #0EA5E9;
}

/* 浏览中状态 */
.assistant-message.browsing .message-content {
    background-color: #F0FDF4;
    border-left: 3px solid #10B981;
}

.system-message .message-content {
    background-color: #F8FAFC;
    color: #64748B;
    text-align: center;
    border: 1px solid #E2E8F0;
}

/* Markdown 样式 */
.message-content p {
    margin: 0 0 12px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul, .message-content ol {
    margin-bottom: 12px;
    padding-left: 20px;
}

.message-content ul:last-child, .message-content ol:last-child {
    margin-bottom: 0;
}

.message-content li {
    margin-bottom: 6px;
}

.message-content li:last-child {
    margin-bottom: 0;
}

.message-content h1, .message-content h2, .message-content h3,
.message-content h4, .message-content h5, .message-content h6 {
    margin-top: 16px;
    margin-bottom: 12px;
    font-weight: 600;
    line-height: 1.3;
}

.message-content h1 {
    font-size: 1.5rem;
}

.message-content h2 {
    font-size: 1.3rem;
}

.message-content h3 {
    font-size: 1.2rem;
}

.message-content h4 {
    font-size: 1.1rem;
}

.message-content h5, .message-content h6 {
    font-size: 1rem;
}

.message-content code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
}

.message-content pre {
    background-color: #F1F5F9;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 12px 0;
}

.message-content pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    font-size: 0.9em;
    color: #334155;
}

.message-content blockquote {
    border-left: 4px solid #CBD5E1;
    padding-left: 12px;
    color: #64748B;
    margin: 12px 0;
}

.message-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
}

.message-content table th, .message-content table td {
    border: 1px solid #E2E8F0;
    padding: 8px;
    text-align: left;
}

.message-content table th {
    background-color: #F1F5F9;
    font-weight: 600;
}

.message-content a {
    color: #3B82F6;
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.message-content img {
    max-width: 100%;
    border-radius: 6px;
    margin: 12px 0;
    transition: opacity 0.3s ease;
}

/* 房源容器样式 */
.house-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.house-container h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1E293B;
    border-bottom: 2px solid #3B82F6;
    padding-bottom: 8px;
}

.house-container h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 10px;
    color: #1E293B;
}

.house-container img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
    max-height: 200px;
    object-fit: cover;
}

.house-container p {
    margin: 8px 0;
}

.house-container a {
    display: inline-block;
    background-color: #3B82F6;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    margin-top: 10px;
    transition: all 0.2s;
}

.house-container a:hover {
    background-color: #2563EB;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.house-container hr {
    border: 0;
    height: 1px;
    background-color: #E2E8F0;
    margin: 15px 0;
}

/* 错误消息样式 */
.error-message {
    background-color: #FEE2E2;
    color: #B91C1C;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
    border-left: 4px solid #EF4444;
    font-size: 0.9rem;
}

/* 调试数据样式 */
.debug-data {
    background-color: #F3F4F6;
    color: #1F2937;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 0.8rem;
    max-height: 300px;
    overflow-y: auto;
}

/* 原始数据样式 */
.raw-data {
    background-color: #F3F4F6;
    color: #1F2937;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
    font-family: monospace;
    white-space: pre-wrap;
    font-size: 0.8rem;
    max-height: 300px;
    overflow-y: auto;
}

/* 懒加载图片样式 */
img.lazy-loading {
    opacity: 0.7;
    filter: blur(5px);
    transition: opacity 0.3s ease, filter 0.3s ease;
}

img.lazy-loaded {
    opacity: 1;
    filter: blur(0);
}

/* 打字机效果样式 */
.typing-container {
    display: inline;
    word-break: break-word;
}

.typing-container span {
    display: inline;
}

/* 思考中动画 */
.thinking-indicator {
    display: inline-flex;
    align-items: center;
    margin-top: 5px;
    font-size: 0.9rem;
    color: #0EA5E9;
}

.thinking-dots {
    display: inline-flex;
    margin-left: 5px;
}

.thinking-dots span {
    width: 6px;
    height: 6px;
    margin: 0 2px;
    background-color: #0EA5E9;
    border-radius: 50%;
    display: inline-block;
    animation: thinking 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.thinking-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes thinking {
    0%, 80%, 100% {
        transform: scale(0);
    } 40% {
        transform: scale(1.0);
    }
}

/* 浏览中动画 */
.browsing-indicator {
    display: inline-flex;
    align-items: center;
    margin-top: 5px;
    font-size: 0.9rem;
    color: #10B981;
}

.browsing-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 5px;
    border: 2px solid rgba(16, 185, 129, 0.3);
    border-radius: 50%;
    border-top-color: #10B981;
    animation: browsing 1s infinite linear;
}

@keyframes browsing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

img.lazy-error {
    opacity: 0.8;
}

.message-actions {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
}

.message-actions button {
    font-size: 0.8rem;
    padding: 4px 8px;
}

.chat-input-area {
    padding: 15px;
    background-color: #FFFFFF;
    border-top: 1px solid #E2E8F0;
}

.chat-input-area .input-group {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #E2E8F0;
}

#message-input {
    resize: none;
    border: none;
    padding: 14px;
    font-size: 1rem;
    background-color: #FFFFFF;
}

#message-input:focus {
    outline: none;
    box-shadow: none;
}

#send-btn {
    border-radius: 0 8px 8px 0;
    padding: 0 20px;
    background-color: #3B82F6;
    border: none;
    transition: all 0.2s;
}

#send-btn:hover {
    background-color: #2563EB;
}

.input-hints {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
    padding: 0 5px;
}

.input-hints span {
    color: #64748B;
    font-size: 0.9rem;
    margin-right: 8px;
    line-height: 30px;
}

.hint-btn {
    background-color: #F1F5F9;
    border: 1px solid #E2E8F0;
    color: #64748B;
    border-radius: 15px;
    padding: 5px 12px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s;
}

.hint-btn:hover {
    background-color: #E2E8F0;
    color: #334155;
}

/* 参数面板样式 */
.parameters-panel {
    position: fixed;
    top: 70px;
    right: 20px;
    width: 320px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #E2E8F0;
}

.parameters-header {
    padding: 12px 16px;
    background-color: #F8FAFC;
    border-bottom: 1px solid #E2E8F0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.parameters-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1E293B;
    display: flex;
    align-items: center;
    gap: 8px;
}

.parameters-header h5 i {
    color: #3B82F6;
}

.parameters-content {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
}

.parameter-item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #E2E8F0;
}

.parameter-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.parameter-name {
    font-weight: 600;
    margin-bottom: 6px;
    color: #334155;
    font-size: 0.9rem;
}

.parameter-value {
    color: #3B82F6;
    background-color: #F1F5F9;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.9rem;
    display: inline-block;
}

/* 调试面板样式 */
.debug-panel {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 450px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #E2E8F0;
}

.debug-header {
    padding: 12px 16px;
    background-color: #F8FAFC;
    border-bottom: 1px solid #E2E8F0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1E293B;
    display: flex;
    align-items: center;
    gap: 8px;
}

.debug-header h5 i {
    color: #3B82F6;
}

.debug-content {
    padding: 16px;
    max-height: 350px;
    overflow-y: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    background-color: #F8FAFC;
    color: #334155;
    border-radius: 6px;
    margin: 10px;
}

.debug-section {
    margin-bottom: 16px;
}

.debug-section h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #64748B;
    padding-bottom: 6px;
    border-bottom: 1px solid #E2E8F0;
}

.debug-section pre {
    margin: 0;
    background-color: #F1F5F9;
    padding: 10px;
    border-radius: 6px;
    overflow-x: auto;
    color: #334155;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .chat-area {
        width: 100%;
    }

    .parameters-panel, .debug-panel {
        width: 90%;
        left: 5%;
        right: 5%;
    }

    .message {
        max-width: 90%;
    }

    .input-hints {
        display: none;
    }
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background-color: #94A3B8;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
    animation: typing 1s infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
    margin-right: 0;
}

@keyframes typing {
    0% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0); }
}

/* 房源卡片样式 */
.house-card {
    border: 1px solid #E2E8F0;
    border-radius: 10px;
    margin-bottom: 20px;
    padding: 0;
    background-color: white;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.house-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.house-card-image {
    width: 100%;
    height: 160px;
    background-color: #F1F5F9;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.house-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.house-card-content {
    padding: 16px;
}

.house-card .house-title {
    font-weight: 600;
    margin-bottom: 12px;
    color: #1E293B;
    font-size: 1.1rem;
    line-height: 1.4;
}

.house-card .house-price {
    color: #EF4444;
    font-weight: 700;
    font-size: 1.3rem;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.house-card .price-unit {
    font-size: 0.9rem;
    color: #64748B;
    margin-left: 4px;
    font-weight: 400;
}

.house-card .house-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    color: #64748B;
    font-size: 0.9rem;
}

.house-card .house-info-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.house-card .house-info-item i {
    color: #3B82F6;
}

.house-card .house-location {
    color: #64748B;
    font-size: 0.9rem;
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
    gap: 5px;
}

.house-card .house-location i {
    color: #3B82F6;
    margin-top: 3px;
}

.house-card .house-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 12px;
}

.house-card .house-tag {
    background-color: #F1F5F9;
    color: #3B82F6;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.house-card .house-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #E2E8F0;
}

.house-card .house-action-btn {
    padding: 6px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
    transition: all 0.2s;
}

.house-card .house-action-btn:hover {
    transform: translateY(-2px);
}

/* 房源详情模态框样式 */
#houseDetailModal .modal-content {
    border-radius: 12px;
    overflow: hidden;
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

#houseDetailModal .modal-header {
    background-color: #F8FAFC;
    border-bottom: 1px solid #E2E8F0;
    padding: 16px 20px;
}

#houseDetailModal .modal-title {
    font-weight: 600;
    color: #1E293B;
}

#houseDetailModal .modal-body {
    padding: 20px;
}

#houseDetailModal .modal-footer {
    background-color: #F8FAFC;
    border-top: 1px solid #E2E8F0;
    padding: 16px 20px;
}

#houseDetailContent {
    max-height: 70vh;
    overflow-y: auto;
}

.house-detail-section {
    margin-bottom: 20px;
}

.house-detail-section h5 {
    font-weight: 600;
    color: #1E293B;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #E2E8F0;
}

.house-detail-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.house-detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.house-detail-item .label {
    color: #64748B;
    font-size: 0.9rem;
}

.house-detail-item .value {
    font-weight: 500;
    color: #334155;
}

.house-detail-description {
    line-height: 1.6;
    color: #334155;
}

.house-detail-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 16px;
}

.house-detail-gallery img {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.house-detail-gallery img:hover {
    transform: scale(1.05);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}
