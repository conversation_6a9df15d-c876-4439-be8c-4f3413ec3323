/**
 * 图片加载处理工具
 * 
 * 提供图片懒加载、占位符和加载失败处理功能
 */

// 图片懒加载处理器
class ImageLoader {
    constructor() {
        // 默认占位符图片（小尺寸的灰色图片）
        this.placeholderImage = '/static/img/placeholder.svg';
        
        // 图片加载失败时的替代图片
        this.fallbackImage = '/static/img/image-error.svg';
        
        // 观察器配置
        this.observerOptions = {
            root: null, // 使用视口作为根
            rootMargin: '0px 0px 200px 0px', // 底部预加载区域
            threshold: 0.1 // 当图片有10%进入视口时触发
        };
        
        // 创建观察器
        this.observer = new IntersectionObserver(this.onIntersect.bind(this), this.observerOptions);
        
        // 初始化
        this.init();
    }
    
    // 初始化
    init() {
        // 监听DOM变化，处理新添加的图片
        this.mutationObserver = new MutationObserver(this.onDomChange.bind(this));
        this.mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 处理已有的图片
        this.processExistingImages();
    }
    
    // 处理已有的图片
    processExistingImages() {
        // 获取所有图片
        const images = document.querySelectorAll('img:not([data-lazy-loaded])');
        
        // 处理每个图片
        images.forEach(img => this.prepareImage(img));
    }
    
    // 准备图片进行懒加载
    prepareImage(img) {
        // 如果已经处理过，跳过
        if (img.hasAttribute('data-lazy-loaded')) {
            return;
        }
        
        // 保存原始图片URL
        const originalSrc = img.getAttribute('src');
        
        // 如果没有原始URL，跳过
        if (!originalSrc || originalSrc === this.placeholderImage || originalSrc === this.fallbackImage) {
            return;
        }
        
        // 标记为已处理
        img.setAttribute('data-lazy-loaded', 'false');
        
        // 保存原始URL
        img.setAttribute('data-original-src', originalSrc);
        
        // 设置占位符
        img.setAttribute('src', this.placeholderImage);
        
        // 添加加载中样式
        img.classList.add('lazy-loading');
        
        // 添加加载事件监听器
        img.addEventListener('load', () => this.onImageLoaded(img));
        img.addEventListener('error', () => this.onImageError(img));
        
        // 开始观察图片
        this.observer.observe(img);
    }
    
    // 当图片进入视口时
    onIntersect(entries, observer) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                
                // 如果图片尚未加载
                if (img.getAttribute('data-lazy-loaded') === 'false') {
                    // 获取原始URL
                    const originalSrc = img.getAttribute('data-original-src');
                    
                    // 设置原始URL
                    img.setAttribute('src', originalSrc);
                    
                    // 标记为正在加载
                    img.setAttribute('data-lazy-loaded', 'loading');
                }
                
                // 停止观察该图片
                observer.unobserve(img);
            }
        });
    }
    
    // 当图片加载完成时
    onImageLoaded(img) {
        // 如果是占位符图片加载完成，不做处理
        if (img.getAttribute('src') === this.placeholderImage) {
            return;
        }
        
        // 移除加载中样式
        img.classList.remove('lazy-loading');
        
        // 添加已加载样式
        img.classList.add('lazy-loaded');
        
        // 标记为已加载
        img.setAttribute('data-lazy-loaded', 'true');
    }
    
    // 当图片加载失败时
    onImageError(img) {
        // 如果是占位符图片加载失败，不做处理
        if (img.getAttribute('src') === this.placeholderImage || 
            img.getAttribute('src') === this.fallbackImage) {
            return;
        }
        
        // 设置为失败图片
        img.setAttribute('src', this.fallbackImage);
        
        // 移除加载中样式
        img.classList.remove('lazy-loading');
        
        // 添加加载失败样式
        img.classList.add('lazy-error');
        
        // 标记为加载失败
        img.setAttribute('data-lazy-loaded', 'error');
    }
    
    // 当DOM变化时
    onDomChange(mutations) {
        // 处理新添加的节点
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                // 处理新添加的节点
                mutation.addedNodes.forEach(node => {
                    // 如果是元素节点
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 如果是图片
                        if (node.tagName === 'IMG') {
                            this.prepareImage(node);
                        }
                        // 如果包含图片
                        else {
                            const images = node.querySelectorAll('img:not([data-lazy-loaded])');
                            images.forEach(img => this.prepareImage(img));
                        }
                    }
                });
            }
        });
    }
}

// 创建图片加载器实例
const imageLoader = new ImageLoader();
