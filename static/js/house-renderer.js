/**
 * 房源数据渲染器
 * 用于将房源数据渲染为Markdown格式
 */

// 在加载时输出日志
console.log('房源渲染器已加载');

// 确保全局对象存在
if (typeof window !== 'undefined') {
    // 创建全局对象
    window.HouseRenderer = window.HouseRenderer || {};
    console.log('初始化HouseRenderer全局对象');
}

// 渲染房源列表
function renderHouseList(title, houses) {
    let markdown = `## ${title}\n\n`;

    // 添加房源总数
    markdown += `找到 ${houses.length} 个符合条件的房源\n\n`;

    // 添加房源卡片
    houses.forEach(house => {
        markdown += renderHouseCard(house);
    });

    return markdown;
}

// 渲染单个房源卡片
function renderHouseCard(house) {
    console.log('渲染房源卡片:', house);

    try {
        // 提取房源信息 - 适配不同的数据格式
        let name, price, image, id, roomInfo, tags, location;

        // 检查是否是小区数据格式
        if (house.resblock_name) {
            name = house.resblock_name || '未知小区';

            // 提取价格信息 - 检查多种可能的价格字段
            if (house.min_price) {
                price = `¥${house.min_price}`;
                if (house.max_price && house.max_price !== house.min_price) {
                    price += ` - ¥${house.max_price}`;
                }
            } else if (house.price) {
                price = `¥${house.price}${house.price_unit || '/月'}`;
            } else {
                price = '价格待定';
            }

            // 提取图片信息 - 检查多种可能的图片字段
            image = house.pic_info || house.photo || house.photo_webp || '';

            id = house.resblock_id || '';
            roomInfo = house.house_count ? `可租套数: ${house.house_count}套` : '';

            // 提取位置信息
            const locationInfo = [];
            if (house.district_name) locationInfo.push(house.district_name);
            if (house.bizcircle_name) locationInfo.push(house.bizcircle_name);
            location = locationInfo.join(' · ');
        }
        // 检查是否是房间数据格式
        else if (house.name || house.name_v2) {
            name = house.name_v2 || house.name || '未知房源';
            price = house.price ? `¥${house.price}${house.price_unit || '/月'}` : '价格待定';
            image = house.photo || house.photo_webp || '';
            id = house.house_id || house.id || '';

            // 房间信息
            const roomDetails = [];
            if (house.area) roomDetails.push(`${house.area}㎡`);
            if (house.floor && house.floor_total) roomDetails.push(`${house.floor}/${house.floor_total}层`);
            if (house.face) roomDetails.push(`朝${house.face}`);
            roomInfo = roomDetails.join(' | ');

            // 标签
            if (house.tags && Array.isArray(house.tags)) {
                tags = house.tags.map(tag => tag.title || tag).join('、');
            }

            // 位置信息
            const locationInfo = [];
            if (house.district_name) locationInfo.push(house.district_name);
            if (house.bizcircle_name) locationInfo.push(house.bizcircle_name);
            if (house.resblock_name) locationInfo.push(house.resblock_name);
            location = locationInfo.join(' · ');

            // 地铁信息
            if (house.subway_station_info) {
                location += `\n${house.subway_station_info}`;
            }
        }
        // 其他未知格式
        else {
            name = '未知房源';
            price = '价格待定';
            image = '';
            id = '';
            roomInfo = '';
        }

        console.log('提取的房源信息:', { name, price, image, id, roomInfo, tags, location });

        // 生成Markdown格式的房源卡片
        let markdown = `### ${name}\n\n`;

        // 添加位置信息
        if (location) {
            markdown += `**位置**: ${location}\n\n`;
        }

        // 添加图片
        if (image) {
            markdown += `![${name}](${image})\n\n`;
        }

        // 添加价格和房间信息
        markdown += `**价格**: ${price}`;
        if (roomInfo) {
            markdown += ` | **房间**: ${roomInfo}`;
        }
        markdown += '\n\n';

        // 添加标签
        if (tags) {
            markdown += `**标签**: ${tags}\n\n`;
        }

        // 添加查看详情按钮
        if (id) {
            let detailUrl;
            if (house.resblock_name) {
                // 小区详情页
                detailUrl = `https://www.ziroom.com/z/vr/index.html?id=${id}`;
            } else {
                // 房源详情页
                detailUrl = `https://www.ziroom.com/z/vr/${id}.html`;
            }
            markdown += `[查看详情](${detailUrl})\n\n`;
        }

        // 添加分隔线
        markdown += `---\n\n`;

        console.log('生成的Markdown:', markdown);
        return markdown;
    } catch (error) {
        console.error('渲染房源卡片失败:', error);
        return `### 渲染房源卡片失败\n\n${error.message}\n\n---\n\n`;
    }
}

// 处理房源数据
function processHouseData(houseData) {
    console.log('处理房源数据:', houseData);

    try {
        // 检查是否是直接的房源数据对象
        if (houseData && typeof houseData === 'object' && !Array.isArray(houseData)) {
            // 检查是否有content字段（SSE格式）
            if (houseData.content && typeof houseData.content === 'object') {
                console.log('检测到SSE格式数据，提取content');
                houseData = houseData.content;
            }

            // 检查是否是小区数据或房间数据
            if (houseData.resblock_id || houseData.house_id || houseData.id || houseData.name || houseData.name_v2) {
                console.log('渲染房源卡片');
                return renderHouseCard(houseData);
            }
        }

        // 如果是房源列表
        if (Array.isArray(houseData)) {
            console.log('渲染房源列表');
            return renderHouseList("房源列表", houseData);
        }

        // 默认返回格式化的数据
        console.log('未识别的数据格式，返回格式化数据');
        let markdown = "### 房源信息\n\n";

        // 尝试提取关键信息
        if (typeof houseData === 'object') {
            const keys = Object.keys(houseData);
            for (const key of keys) {
                const value = houseData[key];
                if (typeof value !== 'object') {
                    markdown += `**${key}**: ${value}\n\n`;
                }
            }
        }

        markdown += "```json\n" + JSON.stringify(houseData, null, 2) + "\n```\n\n";
        return markdown;
    } catch (error) {
        console.error('处理房源数据失败:', error);
        return `### 处理房源数据失败\n\n${error.message}\n\n---\n\n`;
    }
}

// 导出函数
if (typeof window !== 'undefined') {
    window.HouseRenderer = window.HouseRenderer || {};
    window.HouseRenderer.processHouseData = processHouseData;
    window.HouseRenderer.renderHouseList = renderHouseList;
    window.HouseRenderer.renderHouseCard = renderHouseCard;

    console.log('房源渲染器方法已导出:', Object.keys(window.HouseRenderer));

    // 添加一个测试方法
    window.HouseRenderer.test = function() {
        console.log('房源渲染器测试方法被调用');
        return '测试成功';
    };
}
