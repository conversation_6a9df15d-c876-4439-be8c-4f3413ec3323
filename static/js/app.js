// 全局变量
let currentSessionId = null;
let sessions = [];
let isStreaming = false;
let eventSource = null;
let lastStoredSessionId = null;
let currentUserId = null;

// 通用API响应处理函数
function handleApiResponse(response) {
    // 检查是否是新的标准格式
    if (response && typeof response === 'object' && 'code' in response) {
        // 新格式：{code, message, data}
        if (response.code !== 200) {
            throw new Error(response.message || 'API请求失败');
        }
        return response.data;
    }

    // 旧格式：直接返回数据
    return response;
}

// DOM元素
const messageInput = document.getElementById('message-input');
const sendBtn = document.getElementById('send-btn');
const chatMessages = document.getElementById('chat-messages');
const sessionList = document.getElementById('session-list');
const newChatBtn = document.getElementById('new-chat-btn');
const clearChatBtn = document.getElementById('clear-chat-btn');
const currentSessionTitle = document.getElementById('current-session-title');
const parametersPanel = document.getElementById('parameters-panel');
const parametersContent = document.getElementById('parameters-content');
const closeParametersBtn = document.getElementById('close-parameters-btn');
const debugPanel = document.getElementById('debug-panel');
const debugContent = document.getElementById('debug-content');
const closeDebugBtn = document.getElementById('close-debug-btn');
const toggleDebugBtn = document.getElementById('toggle-debug-btn');
const showParamsBtn = document.getElementById('show-params-btn');
const sessionStateBadge = document.getElementById('session-state-badge');

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 配置 marked.js
    configureMarked();

    // 检查用户ID
    currentUserId = localStorage.getItem('userId');

    // 如果没有用户ID，显示用户名输入模态框
    if (!currentUserId) {
        showUserNameModal();
    } else {
        // 更新用户名显示
        updateUserDisplay(currentUserId);

        // 尝试恢复上次会话
        await initializeSession();
    }

    // 事件监听
    sendBtn.addEventListener('click', sendMessage);
    messageInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    newChatBtn.addEventListener('click', createNewSession);
    clearChatBtn.addEventListener('click', clearChat);
    closeParametersBtn.addEventListener('click', () => {
        parametersPanel.style.display = 'none';
    });
    closeDebugBtn.addEventListener('click', () => {
        debugPanel.style.display = 'none';
    });
    toggleDebugBtn.addEventListener('click', toggleDebugFormat);
    showParamsBtn.addEventListener('click', () => {
        if (parametersPanel.style.display === 'block') {
            parametersPanel.style.display = 'none';
        } else {
            getSessionContext();
        }
    });

    // 加载所有会话按钮
    const loadSessionsBtn = document.getElementById('load-sessions-btn');
    if (loadSessionsBtn) {
        loadSessionsBtn.addEventListener('click', () => {
            loadSessions();
        });
    }

    // 添加快捷提问按钮事件
    document.querySelectorAll('.hint-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            // 设置输入框的值
            messageInput.value = btn.textContent;

            // 直接发送消息
            sendMessage();
        });
    });

    // 用户名保存按钮
    const saveUserNameBtn = document.getElementById('saveUserNameBtn');
    if (saveUserNameBtn) {
        saveUserNameBtn.addEventListener('click', saveUserName);
    }

    // 用户名输入框回车事件
    const userNameInput = document.getElementById('userName');
    if (userNameInput) {
        userNameInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                saveUserName();
            }
        });
    }

    // 切换用户按钮
    const switchUserBtn = document.getElementById('switch-user-btn');
    if (switchUserBtn) {
        switchUserBtn.addEventListener('click', showUserNameModal);
    }
});

// 显示用户名输入模态框
function showUserNameModal() {
    // 如果有当前用户，在输入框中显示当前用户名
    const userNameInput = document.getElementById('userName');
    if (userNameInput && currentUserId) {
        userNameInput.value = currentUserId;
    }

    // 根据是否有当前用户，显示不同的标题、提示和按钮文本
    const modalTitle = document.getElementById('userNameModalLabel');
    const modalPrompt = document.querySelector('#userNameModal .modal-body p');
    const saveButton = document.getElementById('saveUserNameBtn');

    if (currentUserId) {
        // 切换用户模式
        if (modalTitle) {
            modalTitle.textContent = '切换用户';
        }
        if (modalPrompt) {
            modalPrompt.textContent = '请输入新的用户名，切换后将加载该用户的会话历史。';
        }
        if (saveButton) {
            saveButton.textContent = '切换用户';
        }
    } else {
        // 首次登录模式
        if (modalTitle) {
            modalTitle.textContent = '欢迎使用AI找房助手';
        }
        if (modalPrompt) {
            modalPrompt.textContent = '请输入您的用户名，以便我们为您提供个性化的服务。';
        }
        if (saveButton) {
            saveButton.textContent = '开始使用';
        }
    }

    const userNameModal = new bootstrap.Modal(document.getElementById('userNameModal'));
    userNameModal.show();
}

// 保存用户名
async function saveUserName() {
    const userNameInput = document.getElementById('userName');
    let userName = userNameInput.value.trim();

    // 如果用户名为空，使用默认值
    if (!userName) {
        userName = '访客_' + Math.floor(Math.random() * 10000);
    }

    // 检查是否是切换用户
    const isUserSwitch = currentUserId && currentUserId !== userName;

    // 保存用户ID到本地存储
    localStorage.setItem('userId', userName);
    currentUserId = userName;

    // 更新用户名显示
    updateUserDisplay(userName);

    // 关闭模态框
    const userNameModal = bootstrap.Modal.getInstance(document.getElementById('userNameModal'));
    userNameModal.hide();

    // 如果是切换用户，清空当前会话ID
    if (isUserSwitch) {
        localStorage.removeItem('currentSessionId');
        currentSessionId = null;
        sessions = [];
    }

    // 初始化会话
    await initializeSession();

    // 如果是切换用户，显示提示消息
    if (isUserSwitch) {
        addSystemMessage(`
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> 您已切换到用户 <strong>${userName}</strong>
            </div>
        `);
    }
}

// 更新用户名显示
function updateUserDisplay(userName) {
    const userNameElement = document.querySelector('.user-name');
    if (userNameElement) {
        userNameElement.textContent = userName;
    }
}

// 初始化会话
async function initializeSession() {
    // 尝试恢复上次会话
    lastStoredSessionId = localStorage.getItem('currentSessionId');

    if (lastStoredSessionId) {
        try {
            // 尝试加载上次会话
            await loadLastSession(lastStoredSessionId);
        } catch (error) {
            console.error('恢复上次会话失败:', error);
            // 如果恢复失败，创建新会话
            await createNewSession();
        }
    } else {
        // 没有存储的会话ID，创建新会话
        await createNewSession();
    }

    // 无论是恢复会话还是创建新会话，都加载会话列表
    await loadSessions();
}

// 配置 marked.js
function configureMarked() {
    if (typeof marked !== 'undefined') {
        marked.setOptions({
            renderer: new marked.Renderer(),
            highlight: function (code, lang) {
                if (typeof hljs !== 'undefined') {
                    const language = hljs.getLanguage(lang) ? lang : 'plaintext';
                    return hljs.highlight(code, { language }).value;
                }
                return code;
            },
            langPrefix: 'hljs language-',
            pedantic: false,
            gfm: true,
            breaks: true,
            sanitize: false,
            smartypants: false,
            xhtml: false
        });
    }
}

// 加载上次会话
async function loadLastSession(sessionId) {
    try {
        // 使用POST请求获取会话
        const response = await fetch('/api/session/info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: sessionId
            })
        });

        if (!response.ok) {
            throw new Error('获取会话失败');
        }

        const apiResponse = await response.json();
        const data = handleApiResponse(apiResponse);
        currentSessionId = sessionId;

        // 更新会话列表
        sessions = [{
            session_id: sessionId,
            title: data.title || '恢复的对话',
            time: new Date().toLocaleTimeString()
        }];

        renderSessionList();

        // 加载对话历史
        await loadConversation(sessionId);

        // 更新标题
        currentSessionTitle.textContent = data.title || '恢复的对话';

        // 更新状态
        updateSessionState('空闲');

        return true;
    } catch (error) {
        console.error('加载上次会话失败:', error);
        throw error;
    }
}

// 简化的处理房源数据函数
function handleHouseData(data, messageElement) {
    console.log('处理房源数据:', data);

    // 如果没有消息元素，创建一个新的
    if (!messageElement) {
        messageElement = addAssistantMessage('');
    }

    // 获取内容元素
    const contentElement = messageElement.querySelector('.message-content');
    if (!contentElement) {
        console.error('未找到消息内容元素');
        return;
    }

    // 创建一个卡片元素
    const cardDiv = document.createElement('div');
    cardDiv.style.border = '1px solid #ddd';
    cardDiv.style.borderRadius = '8px';
    cardDiv.style.margin = '10px 0';
    cardDiv.style.padding = '15px';
    cardDiv.style.backgroundColor = '#fff';
    cardDiv.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';

    // 显示原始数据（用于调试）
    const debugDiv = document.createElement('pre');
    debugDiv.style.fontSize = '12px';
    debugDiv.style.backgroundColor = '#f5f5f5';
    debugDiv.style.padding = '10px';
    debugDiv.style.borderRadius = '4px';
    debugDiv.style.overflow = 'auto';
    debugDiv.style.maxHeight = '200px';
    debugDiv.textContent = JSON.stringify(data, null, 2);

    cardDiv.appendChild(debugDiv);
    contentElement.appendChild(cardDiv);
}

// 更新会话状态
function updateSessionState(state) {
    sessionStateBadge.textContent = state;

    // 根据状态设置不同的颜色
    sessionStateBadge.className = 'badge ms-2';

    switch (state.toLowerCase()) {
        case '处理中':
            sessionStateBadge.classList.add('bg-warning');
            break;
        case '思考中':
            sessionStateBadge.classList.add('bg-info');
            break;
        case '浏览中':
            sessionStateBadge.classList.add('bg-primary');
            break;
        case '空闲':
            sessionStateBadge.classList.add('bg-success');
            break;
        case '错误':
            sessionStateBadge.classList.add('bg-danger');
            break;
        default:
            sessionStateBadge.classList.add('bg-secondary');
    }
}

// 创建新会话
async function createNewSession() {
    try {
        // 更新状态
        updateSessionState('处理中');

        const response = await fetch('/api/session/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: currentUserId || ('访客_' + Math.floor(Math.random() * 10000))
            })
        });

        if (!response.ok) {
            throw new Error('创建会话失败');
        }

        const apiResponse = await response.json();
        const data = handleApiResponse(apiResponse);
        currentSessionId = data.session_id;

        // 保存会话ID到本地存储
        localStorage.setItem('currentSessionId', currentSessionId);

        // 更新会话列表
        await loadSessions();

        // 清空聊天区域
        chatMessages.innerHTML = '';

        // 添加欢迎消息
        addSystemMessage(`
            <h3>👋 ${currentUserId ? `${currentUserId}，` : ''}欢迎使用AI找房助手</h3>
            <p>我是您的智能找房顾问，可以根据您的需求快速匹配合适的房源。请告诉我您的找房需求，例如：</p>
            <ul>
                <li>🏙️ <strong>位置需求</strong>：我想在朝阳区找房、离地铁5号线近一点、望京SOHO附近</li>
                <li>💰 <strong>预算范围</strong>：月租5000-8000元、预算7000左右</li>
                <li>🏠 <strong>房型要求</strong>：两居室、一室一厅、主卧带独卫</li>
                <li>🌟 <strong>特殊需求</strong>：要有电梯、南北通透、精装修、可以养宠物</li>
                <li>🚇 <strong>通勤要求</strong>：到国贸上班通勤30分钟内、骑车到中关村不超过20分钟</li>
            </ul>
            <p>您可以直接输入您的需求，我会为您推荐最合适的房源！</p>
        `);

        // 更新标题
        currentSessionTitle.textContent = '新对话';

        // 关闭面板
        parametersPanel.style.display = 'none';
        debugPanel.style.display = 'none';

        // 更新状态
        updateSessionState('空闲');

        // 聚焦输入框
        messageInput.focus();

    } catch (error) {
        console.error('创建会话失败:', error);
        addSystemMessage('创建会话失败，请刷新页面重试。');
        updateSessionState('错误');
    }
}

// 发送消息
async function sendMessage() {
    const message = messageInput.value.trim();

    if (!message || !currentSessionId || isStreaming) return;

    // 清空输入框
    messageInput.value = '';

    // 添加用户消息
    addUserMessage(message);

    // 更新状态
    updateSessionState('处理中');

    // 添加加载指示器
    const loadingIndicator = addLoadingIndicator();

    try {
        // 使用流式响应
        isStreaming = true;

        // 关闭之前的事件源
        if (eventSource) {
            eventSource.close();
        }

        // 创建新的事件源 - 使用POST请求

        // 创建POST请求
        const fetchController = new AbortController();
        const fetchSignal = fetchController.signal;

        // 使用fetch发起POST请求，然后手动处理SSE
        fetch('/api/chat/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: currentSessionId,
                message: message,
                stream: true
            }),
            signal: fetchSignal
        }).then(response => {
            // 创建一个新的Reader
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            // 标记流是否已关闭
            let isStreamClosed = false;

            function readStream() {
                // 如果流已关闭，不再继续读取
                if (isStreamClosed) {
                    return;
                }

                reader.read().then(({ done, value }) => {
                    // 如果流已关闭，不再处理数据
                    if (isStreamClosed) {
                        return;
                    }

                    if (done) {
                        // 流结束，处理最后的数据
                        if (buffer.includes('data: [DONE]')) {
                            // 手动触发DONE事件
                            const doneEvent = new MessageEvent('message', {
                                data: '[DONE]'
                            });
                            eventSource.onmessage(doneEvent);
                        }
                        return;
                    }

                    // 解码数据并添加到缓冲区
                    buffer += decoder.decode(value, { stream: true });

                    // 处理缓冲区中的每一行
                    const lines = buffer.split('\n\n');
                    buffer = lines.pop() || ''; // 保留最后一个不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.substring(6); // 移除 'data: ' 前缀

                            // 如果是[DONE]事件，标记流已关闭
                            if (data === '[DONE]') {
                                isStreamClosed = true;
                            }

                            // 创建一个新的MessageEvent
                            const messageEvent = new MessageEvent('message', {
                                data: data
                            });

                            // 手动调用onmessage处理函数
                            eventSource.onmessage(messageEvent);

                            // 如果是[DONE]事件，不再继续处理
                            if (data === '[DONE]') {
                                return;
                            }
                        }
                    }

                    // 如果流未关闭，继续读取
                    if (!isStreamClosed) {
                        readStream();
                    }
                }).catch(error => {
                    // 如果流已关闭，忽略错误
                    if (isStreamClosed) {
                        return;
                    }

                    console.error('读取流失败:', error);
                    // 触发错误处理
                    eventSource.onerror(error);
                });
            }

            // 开始读取流
            readStream();
        }).catch(error => {
            console.error('发起POST请求失败:', error);
            // 触发错误处理
            eventSource.onerror(error);
        });

        // 创建一个模拟的EventSource对象
        eventSource = {
            onmessage: null,
            onerror: null,
            close: function () {
                fetchController.abort();
            }
        };

        let assistantMessage = '';
        let messageElement = null;

        // 创建调试数据对象
        const debugData = {
            extractedParams: null,
            apiResponse: null
        };

        eventSource.onmessage = (event) => {
            // 移除加载指示器
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            if (event.data === '[DONE]') {
                // 流式响应结束
                isStreaming = false;

                // 标记流已关闭，然后关闭事件源
                isStreamClosed = true;
                eventSource.close();

                // 更新状态
                updateSessionState('空闲');

                // 使用累积的消息内容更新，确保格式化正确
                if (messageElement) {
                    updateAssistantMessage(messageElement, assistantMessage);
                }

                // 更新会话列表
                updateSessionTitle(message);

                // 获取完整响应
                fetch(`/api/session/${currentSessionId}/last_response`)
                    .then(response => response.json())
                    .then(apiResponse => {
                        const data = handleApiResponse(apiResponse);
                        if (data && data.content) {
                            // 更新消息内容
                            assistantMessage = data.content;
                            if (messageElement) {
                                updateAssistantMessage(messageElement, assistantMessage);

                                // 移除思考中和浏览中的状态
                                messageElement.classList.remove('thinking', 'browsing');

                                // 移除指示器
                                const indicator = messageElement.querySelector('.thinking-indicator, .browsing-indicator');
                                if (indicator) {
                                    indicator.remove();
                                }
                            } else {
                                messageElement = addAssistantMessage(assistantMessage);
                            }

                            // 更新调试数据
                            debugData.apiResponse = data;
                        }

                        // 获取会话上下文和调试数据
                        return getSessionContext();
                    })
                    .then(data => {
                        if (data) {
                            // 更新调试数据
                            debugData.extractedParams = data.context;

                            // 更新消息元素的调试数据
                            if (messageElement) {
                                messageElement.dataset.debugData = JSON.stringify(debugData);

                                // 添加调试按钮
                                const messageActions = document.createElement('div');
                                messageActions.className = 'message-actions';
                                messageActions.innerHTML = `
                                    <button class="debug-btn btn btn-sm btn-outline-secondary" data-message-id="${messageElement.id}">
                                        <i class="bi bi-info-circle"></i> 查看详情
                                    </button>
                                `;
                                messageElement.appendChild(messageActions);

                                // 为调试按钮添加事件监听器
                                const debugBtn = messageElement.querySelector('.debug-btn');
                                if (debugBtn) {
                                    debugBtn.addEventListener('click', function () {
                                        showDebugPanel(debugData);
                                    });
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('获取完整响应失败:', error);
                    });

                return;
            }

            try {
                // 尝试解析JSON数据
                const jsonData = JSON.parse(event.data);

                // 检查是否是新的SSE格式
                if (jsonData && jsonData.id && jsonData.parent_type && jsonData.sub_type) {
                    // 获取内容
                    const content = jsonData.content;
                    const parentType = jsonData.parent_type;
                    const subType = jsonData.sub_type;

                    // 根据parent_type更新会话状态
                    if (parentType === 'requirement_communication') {
                        updateSessionState('思考中');
                    } else if (parentType === 'bizcircle' ||
                        parentType === 'subway_line' ||
                        parentType === 'subway_station' ||
                        parentType === 'resblock' ||
                        parentType === 'district' ||
                        parentType.startsWith('poi_')) {
                        updateSessionState('浏览中');
                    }

                    // 处理不同的sub_type
                    if (subType === 'text') {
                        // 文本类型 - 逐字符显示
                        // 实现打字机效果
                        if (!messageElement) {
                            // 第一次收到消息，创建消息元素
                            assistantMessage = content;
                            messageElement = addAssistantMessage(assistantMessage);

                            // 创建一个内部容器用于打字机效果
                            const contentElement = messageElement.querySelector('.message-content');
                            if (contentElement) {
                                // 清空内容，准备逐字添加
                                contentElement.innerHTML = '';

                                // 创建打字机容器
                                const typingContainer = document.createElement('div');
                                typingContainer.className = 'typing-container';
                                contentElement.appendChild(typingContainer);
                            }
                        } else {
                            // 已有消息元素，追加内容
                            const contentElement = messageElement.querySelector('.message-content');
                            const typingContainer = contentElement.querySelector('.typing-container');

                            if (typingContainer) {
                                // 追加新字符
                                const charSpan = document.createElement('span');
                                charSpan.textContent = content;
                                typingContainer.appendChild(charSpan);

                                // 累积消息内容
                                assistantMessage += content;
                            }
                        }
                    } else if (subType.endsWith('_list')) {
                        // 直接从jsonData中获取数据并显示
                        console.log(`收到${subType}数据:`, jsonData);

                        // 获取数据
                        const data = jsonData.json[subType];
                        console.log(`解析后的${subType}数据:`, data);

                        // 创建一个新的消息元素
                        const newMessageDiv = document.createElement('div');
                        newMessageDiv.className = 'message assistant-message';
                        newMessageDiv.style.padding = '15px';
                        newMessageDiv.style.margin = '10px 0';
                        newMessageDiv.style.border = '1px solid #ddd';
                        newMessageDiv.style.borderRadius = '8px';
                        newMessageDiv.style.backgroundColor = '#fff';

                        // 根据不同的数据类型显示不同的内容
                        if (subType === 'subway_line_list' && data && data.resblock_id) {
                            // 地铁线小区数据
                            const imageUrl = data.pic_info || 'https://via.placeholder.com/150';

                            newMessageDiv.innerHTML = `
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 0 0 100px; margin-right: 10px;">
                                        <img src="${imageUrl}" alt="${data.resblock_name || '小区图片'}" style="width: 100%; height: auto; border-radius: 4px;">
                                    </div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">${data.resblock_name || '未知小区'}</div>
                                        <div style="font-size: 16px; color: #f5222d; margin-bottom: 5px;">${data.min_price || '价格待定'} - ${data.max_price || '价格待定'}<span>元/月</span></div>
                                        <div style="margin-bottom: 5px;">共${data.house_count || '0'}套房源</div>
                                    </div>
                                </div>
                            `;
                        } else if (subType === 'resblock_list' && data) {
                            // 房源数据
                            const imageUrl = data.photo || 'https://via.placeholder.com/150';

                            // 处理标签
                            let tagsHtml = '';
                            if (data.tags && data.tags.length > 0) {
                                tagsHtml = data.tags.map(tag => {
                                    const tagText = typeof tag === 'object' ? tag.title : tag;
                                    return `<span style="display: inline-block; background-color: #f0f0f0; padding: 2px 6px; border-radius: 4px; margin-right: 5px; font-size: 12px;">${tagText}</span>`;
                                }).join('');
                            }

                            newMessageDiv.innerHTML = `
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="flex: 0 0 100px; margin-right: 10px;">
                                        <img src="${imageUrl}" alt="${data.name || '房源图片'}" style="width: 100%; height: auto; border-radius: 4px;">
                                    </div>
                                    <div style="flex: 1;">
                                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 5px;">${data.name || '未知房源'}</div>
                                        <div style="font-size: 16px; color: #f5222d; margin-bottom: 5px;">${data.price || '价格待定'}<span>${data.price_unit || '元/月'}</span></div>
                                        <div style="margin-bottom: 5px;">${data.room_intro || ''}</div>
                                        <div style="font-size: 14px; color: #666; margin-bottom: 5px;">${data.subway_station_info || ''}</div>
                                        <div style="margin-top: 5px;">${tagsHtml}</div>
                                    </div>
                                </div>
                            `;
                        } else {
                            // 如果没有数据或不是已知的数据类型，显示原始JSON
                            newMessageDiv.innerHTML = `
                                <pre style="background-color: #f5f5f5; padding: 10px; overflow: auto; max-height: 300px;">
${JSON.stringify(data, null, 2)}
                                </pre>
                            `;
                        }

                        // 直接添加到聊天消息容器
                        document.querySelector('.chat-messages').appendChild(newMessageDiv);

                        // 滚动到底部
                        document.querySelector('.chat-messages').scrollTop = document.querySelector('.chat-messages').scrollHeight;
                    } else if (subType === 'json') {
                        // JSON类型 - 直接显示完整内容
                        console.log('收到JSON数据:', content);
                        console.log('JSON数据类型:', typeof content);
                        console.log('JSON数据结构:', Object.keys(content));

                        // 检查window.HouseRenderer是否存在
                        if (!window.HouseRenderer) {
                            console.error('房源渲染器未加载！');
                            // 尝试重新加载房源渲染器
                            const script = document.createElement('script');
                            script.src = '/static/js/house-renderer.js';
                            script.onload = function () {
                                console.log('房源渲染器重新加载成功');
                            };
                            script.onerror = function () {
                                console.error('房源渲染器加载失败');
                            };
                            document.head.appendChild(script);

                            // 添加错误消息
                            if (!messageElement) {
                                messageElement = addAssistantMessage('房源渲染器加载失败，请刷新页面重试。');
                            }
                            return;
                        }

                        // 根据parent_type处理不同类型的JSON数据
                        if (parentType === 'listing') {
                            console.log('处理房源数据');

                            // 处理房源数据
                            // 如果没有消息元素，创建一个新的
                            if (!messageElement) {
                                console.log('创建新的消息元素');
                                messageElement = addAssistantMessage('');

                                // 创建一个内部容器用于显示房源数据
                                const contentElement = messageElement.querySelector('.message-content');
                                if (contentElement) {
                                    console.log('创建房源容器');
                                    // 清空内容，准备添加房源数据
                                    contentElement.innerHTML = '';

                                    // 创建房源容器
                                    const houseContainer = document.createElement('div');
                                    houseContainer.className = 'house-container';
                                    contentElement.appendChild(houseContainer);
                                }
                            }

                            // 获取房源容器
                            const contentElement = messageElement.querySelector('.message-content');
                            if (!contentElement) {
                                console.error('未找到消息内容元素');
                                return;
                            }

                            let houseContainer = contentElement.querySelector('.house-container');
                            if (!houseContainer) {
                                console.log('未找到房源容器，创建新的');
                                houseContainer = document.createElement('div');
                                houseContainer.className = 'house-container';
                                contentElement.appendChild(houseContainer);
                            }

                            if (houseContainer) {
                                try {
                                    console.log('调用房源渲染器处理数据');

                                    // 仅在开发模式下显示原始数据（用于调试）
                                    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                                        const debugDiv = document.createElement('div');
                                        debugDiv.className = 'debug-data';
                                        debugDiv.style.display = 'none';
                                        debugDiv.textContent = JSON.stringify(content, null, 2);
                                        houseContainer.appendChild(debugDiv);
                                    }

                                    // 使用房源渲染器处理数据
                                    console.log('HouseRenderer方法:', Object.keys(window.HouseRenderer));

                                    // 确保content是对象而不是字符串
                                    let processedContent = content;
                                    if (typeof content === 'string') {
                                        try {
                                            processedContent = JSON.parse(content);
                                            console.log('将字符串解析为JSON对象:', processedContent);
                                        } catch (e) {
                                            console.error('解析JSON字符串失败:', e);
                                        }
                                    }

                                    const markdownContent = window.HouseRenderer.processHouseData(processedContent);
                                    console.log('生成的Markdown内容:', markdownContent);

                                    // 检查marked和DOMPurify是否存在
                                    if (!window.marked) {
                                        console.error('marked未加载');
                                        throw new Error('marked未加载');
                                    }

                                    if (!window.DOMPurify) {
                                        console.error('DOMPurify未加载');
                                        throw new Error('DOMPurify未加载');
                                    }

                                    // 将Markdown转换为HTML
                                    const htmlContent = DOMPurify.sanitize(marked.parse(markdownContent));
                                    console.log('生成的HTML内容:', htmlContent.substring(0, 100) + '...');

                                    // 追加到房源容器
                                    const tempDiv = document.createElement('div');
                                    tempDiv.innerHTML = htmlContent;

                                    // 将tempDiv中的所有子节点添加到houseContainer
                                    console.log('添加HTML内容到房源容器');
                                    let childCount = 0;
                                    while (tempDiv.firstChild) {
                                        houseContainer.appendChild(tempDiv.firstChild);
                                        childCount++;
                                    }
                                    console.log(`添加了${childCount}个子节点`);

                                    // 累积消息内容
                                    assistantMessage += markdownContent;

                                    // 仅在开发模式下添加调试按钮
                                    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                                        const debugButton = document.createElement('button');
                                        debugButton.className = 'btn btn-sm btn-secondary mt-2';
                                        debugButton.textContent = '显示调试信息';
                                        debugButton.onclick = function () {
                                            const debugData = this.parentNode.querySelector('.debug-data');
                                            if (debugData && debugData.style.display === 'none') {
                                                debugData.style.display = 'block';
                                                this.textContent = '隐藏调试信息';
                                            } else if (debugData) {
                                                debugData.style.display = 'none';
                                                this.textContent = '显示调试信息';
                                            }
                                        };
                                        houseContainer.appendChild(debugButton);
                                    }

                                } catch (error) {
                                    console.error('处理房源数据失败:', error);
                                    // 添加错误消息
                                    const errorDiv = document.createElement('div');
                                    errorDiv.className = 'error-message';
                                    errorDiv.textContent = '处理房源数据失败: ' + error.message;
                                    houseContainer.appendChild(errorDiv);

                                    // 仅在开发模式下显示原始数据
                                    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                                        const rawDataDiv = document.createElement('pre');
                                        rawDataDiv.className = 'raw-data';
                                        rawDataDiv.textContent = JSON.stringify(content, null, 2);
                                        houseContainer.appendChild(rawDataDiv);
                                    }
                                }
                            }
                        } else if (parentType === 'community') {
                            // 处理小区信息
                            // 类似处理...
                        }
                    }

                    // 滚动到底部
                    scrollToBottom();
                } else {
                    // 旧格式的数据处理
                    console.warn('收到旧格式的SSE数据:', event.data);
                    const decodedData = event.data.replace(/\\n/g, "\n");
                    assistantMessage = decodedData;

                    if (!messageElement) {
                        messageElement = addAssistantMessage(assistantMessage);
                    } else {
                        updateAssistantMessage(messageElement, assistantMessage);
                    }
                }
            } catch (error) {
                // 如果解析JSON失败，按旧格式处理
                console.warn('解析SSE数据失败:', error);
                const decodedData = event.data.replace(/\\n/g, "\n");
                assistantMessage = decodedData;

                if (!messageElement) {
                    messageElement = addAssistantMessage(assistantMessage);
                } else {
                    updateAssistantMessage(messageElement, assistantMessage);
                }
            }
        };

        eventSource.onerror = (error) => {
            console.error('流式响应错误:', error);

            // 移除加载指示器
            if (loadingIndicator) {
                loadingIndicator.remove();
            }

            // 关闭事件源
            eventSource.close();
            isStreaming = false;

            // 更新状态
            updateSessionState('错误');

            // 添加错误消息
            if (!messageElement) {
                addSystemMessage('获取回复失败，请重试。');
            }
        };

    } catch (error) {
        console.error('发送消息失败:', error);

        // 移除加载指示器
        if (loadingIndicator) {
            loadingIndicator.remove();
        }

        // 添加错误消息
        addSystemMessage('发送消息失败，请重试。');

        // 更新状态
        updateSessionState('错误');

        isStreaming = false;
    }
}

// 加载会话列表
async function loadSessions() {
    try {
        // 如果没有用户ID，显示提示信息
        if (!currentUserId) {
            console.log('未登录用户，不加载会话列表');
            sessionList.innerHTML = '<div class="loading-session">请先登录</div>';
            return;
        }

        // 显示加载中状态
        sessionList.innerHTML = '<div class="loading-session">加载中...</div>';

        // 调用API获取会话列表，只获取当前用户的会话
        const response = await fetch(`/api/session/list?user_id=${encodeURIComponent(currentUserId)}`);

        if (!response.ok) {
            throw new Error('获取会话列表失败');
        }

        const apiResponse = await response.json();
        const data = handleApiResponse(apiResponse);

        // 清空当前会话列表
        sessions = [];

        // 处理返回的会话数据
        if (data.sessions && data.sessions.length > 0) {
            data.sessions.forEach(session => {
                // 获取最后一条消息作为标题
                let title = '新对话';
                if (session.last_message) {
                    title = session.last_message.content;
                    if (title.length > 20) {
                        title = title.substring(0, 20) + '...';
                    }
                }

                // 格式化时间
                const time = new Date(session.updated_at).toLocaleString();

                // 添加到会话列表
                sessions.push({
                    session_id: session.session_id,
                    title: title,
                    time: time
                });
            });
        }

        // 确保当前会话在列表中
        if (currentSessionId && !sessions.find(s => s.session_id === currentSessionId)) {
            sessions.unshift({
                session_id: currentSessionId,
                title: '当前对话',
                time: new Date().toLocaleTimeString()
            });
        }

        // 更新会话列表UI
        renderSessionList();

        console.log(`已加载 ${sessions.length} 个会话`);

    } catch (error) {
        console.error('加载会话列表失败:', error);
        // 出错时显示当前会话
        sessions = [];
        if (currentSessionId) {
            sessions.push({
                session_id: currentSessionId,
                title: '当前对话',
                time: new Date().toLocaleTimeString()
            });
        }
        renderSessionList();
    }
}

// 渲染会话列表
function renderSessionList() {
    sessionList.innerHTML = '';

    sessions.forEach(session => {
        const sessionItem = document.createElement('div');
        sessionItem.className = `session-item ${session.session_id === currentSessionId ? 'active' : ''}`;
        sessionItem.dataset.sessionId = session.session_id;

        sessionItem.innerHTML = `
            <div class="session-content" data-session-id="${session.session_id}">
                <div class="session-title">${session.title}</div>
                <div class="session-time">${session.time}</div>
            </div>
            <div class="session-actions">
                <button class="delete-session-btn" title="删除会话" data-session-id="${session.session_id}">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        // 为会话内容添加点击事件（切换会话）
        const sessionContent = sessionItem.querySelector('.session-content');
        sessionContent.addEventListener('click', () => {
            switchSession(session.session_id);
        });

        // 为删除按钮添加点击事件
        const deleteBtn = sessionItem.querySelector('.delete-session-btn');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡，避免触发会话切换
            confirmDeleteSession(session.session_id);
        });

        sessionList.appendChild(sessionItem);
    });
}

// 切换会话
async function switchSession(sessionId) {
    if (sessionId === currentSessionId) return;

    try {
        // 使用POST请求获取会话详情
        const response = await fetch('/api/session/info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: sessionId
            })
        });

        if (!response.ok) {
            throw new Error('获取会话失败');
        }

        const apiResponse = await response.json();
        const data = handleApiResponse(apiResponse);

        // 保存会话ID到本地存储
        localStorage.setItem('currentSessionId', sessionId);

        // 设置当前会话ID
        currentSessionId = sessionId;

        // 更新会话列表
        renderSessionList();

        // 加载对话历史
        await loadConversation(sessionId);

        // 更新标题
        currentSessionTitle.textContent = sessions.find(s => s.session_id === sessionId)?.title || '对话';

    } catch (error) {
        console.error('切换会话失败:', error);
        addSystemMessage('切换会话失败，请重试。');
    }
}

// 加载对话历史
async function loadConversation(sessionId) {
    try {
        const response = await fetch(`/api/session/${sessionId}/conversation`);

        if (!response.ok) {
            throw new Error('获取对话历史失败');
        }

        const apiResponse = await response.json();
        const data = handleApiResponse(apiResponse);

        // 清空聊天区域
        chatMessages.innerHTML = '';

        // 添加历史消息
        data.messages.forEach(message => {
            if (message.role === 'user') {
                addUserMessage(message.content);
            } else if (message.role === 'assistant') {
                addAssistantMessage(message.content);
            }
        });

        // 如果没有消息，添加欢迎消息
        if (data.messages.length === 0) {
            addSystemMessage(`
                <p>欢迎使用AI找房系统！我可以帮您找到合适的房源。请告诉我您的需求，例如：</p>
                <ul>
                    <li>"我想在朝阳区找一个两居室的房子"</li>
                    <li>"预算在8000左右，要有电梯"</li>
                    <li>"最好是南向的，离地铁近一点"</li>
                </ul>
            `);
        }

        // 显示上下文参数
        showParameters(data.context);

    } catch (error) {
        console.error('加载对话历史失败:', error);
        addSystemMessage('加载对话历史失败，请重试。');
    }
}

// 获取会话上下文
async function getSessionContext() {
    try {
        // 使用POST请求获取会话上下文
        const response = await fetch('/api/session/info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: currentSessionId
            })
        });

        if (!response.ok) {
            throw new Error('获取会话上下文失败');
        }

        const apiResponse = await response.json();
        const data = handleApiResponse(apiResponse);

        // 显示上下文参数
        showParameters(data.context);

        // 显示调试信息
        showDebugInfo(data);

        // 返回数据
        return data;

    } catch (error) {
        console.error('获取会话上下文失败:', error);
        return null;
    }
}

// 更新会话标题
function updateSessionTitle(message) {
    // 使用消息的前10个字符作为标题
    const title = message.length > 20 ? message.substring(0, 20) + '...' : message;

    // 更新当前会话的标题
    const session = sessions.find(s => s.session_id === currentSessionId);
    if (session) {
        session.title = title;
        session.time = new Date().toLocaleTimeString();
    }

    // 更新会话列表
    renderSessionList();

    // 更新标题
    currentSessionTitle.textContent = title;
}

// 清空聊天
async function clearChat() {
    if (!currentSessionId) return;

    try {
        // 调用清除会话API
        const response = await fetch(`/api/chat/clear?session_id=${currentSessionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('清除会话失败');
        }

        // 清空聊天区域
        chatMessages.innerHTML = '';

        // 添加欢迎消息
        addSystemMessage(`
            <p>欢迎使用AI找房系统！我可以帮您找到合适的房源。请告诉我您的需求，例如：</p>
            <ul>
                <li>"我想在朝阳区找一个两居室的房子"</li>
                <li>"预算在8000左右，要有电梯"</li>
                <li>"最好是南向的，离地铁近一点"</li>
            </ul>
        `);

        // 关闭面板
        parametersPanel.style.display = 'none';
        debugPanel.style.display = 'none';

        // 更新会话标题
        updateSessionTitle('新对话');

        // 显示成功消息
        addSystemMessage('会话已清除，所有上下文信息已重置。');

    } catch (error) {
        console.error('清除会话失败:', error);
        addSystemMessage('清除会话失败，请重试。');
    }
}

// 添加用户消息
function addUserMessage(content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message user-message';

    messageDiv.innerHTML = `
        <div class="message-content">
            ${formatMessageContent(content)}
        </div>
    `;

    chatMessages.appendChild(messageDiv);

    // 滚动到底部
    scrollToBottom();

    return messageDiv;
}

// 添加助手消息
function addAssistantMessage(content, debugData = null) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message assistant-message';

    // 为消息添加唯一ID
    const messageId = 'msg-' + Date.now();
    messageDiv.id = messageId;

    // 添加调试按钮HTML
    let debugButtonHtml = '';
    if (debugData) {
        debugButtonHtml = `
            <div class="message-actions">
                <button class="debug-btn btn btn-sm btn-outline-secondary" data-message-id="${messageId}">
                    <i class="bi bi-info-circle"></i> 查看详情
                </button>
            </div>
        `;

        // 存储调试数据
        messageDiv.dataset.debugData = JSON.stringify(debugData);
    }

    // 格式化内容
    const formattedContent = formatMessageContent(content);

    messageDiv.innerHTML = `
        <div class="message-content">
            ${formattedContent}
        </div>
        ${debugButtonHtml}
    `;

    chatMessages.appendChild(messageDiv);

    // 处理房源卡片
    processHouseCards(messageDiv);

    // 为调试按钮添加事件监听器
    if (debugData) {
        const debugBtn = messageDiv.querySelector('.debug-btn');
        if (debugBtn) {
            debugBtn.addEventListener('click', function () {
                showDebugPanel(JSON.parse(messageDiv.dataset.debugData));
            });
        }
    }

    // 滚动到底部
    scrollToBottom();

    return messageDiv;
}

// 更新助手消息
function updateAssistantMessage(messageElement, content) {
    const contentElement = messageElement.querySelector('.message-content');
    if (contentElement) {
        contentElement.innerHTML = formatMessageContent(content);

        // 处理房源卡片
        processHouseCards(messageElement);
    }

    // 滚动到底部
    scrollToBottom();
}

// 处理房源卡片
function processHouseCards(messageElement) {
    // 查找所有房源卡片的代码块
    const codeBlocks = messageElement.querySelectorAll('pre code');

    codeBlocks.forEach(codeBlock => {
        const content = codeBlock.textContent;

        // 检查是否是房源JSON数据
        if (content.includes('"house_id"') || content.includes('"price"') || content.includes('"title"')) {
            try {
                const houseData = JSON.parse(content);

                // 如果是单个房源对象
                if (houseData.house_id || houseData.title) {
                    replaceWithHouseCard(codeBlock, houseData);
                }
                // 如果是房源数组
                else if (Array.isArray(houseData) && houseData.length > 0 && (houseData[0].house_id || houseData[0].title)) {
                    const parentPre = codeBlock.parentNode;
                    const container = document.createElement('div');
                    container.className = 'house-cards-container';

                    houseData.forEach(house => {
                        const cardDiv = createHouseCardElement(house);
                        container.appendChild(cardDiv);
                    });

                    parentPre.parentNode.replaceChild(container, parentPre);
                }
            } catch (e) {
                console.log('Not a valid JSON or not a house data', e);
            }
        }
    });
}

// 替换代码块为房源卡片
function replaceWithHouseCard(codeBlock, houseData) {
    const parentPre = codeBlock.parentNode;

    // 创建一个简单的卡片元素
    const cardDiv = document.createElement('div');
    cardDiv.style.border = '1px solid #ddd';
    cardDiv.style.borderRadius = '8px';
    cardDiv.style.margin = '10px 0';
    cardDiv.style.padding = '15px';
    cardDiv.style.backgroundColor = '#fff';

    // 显示原始数据
    const debugDiv = document.createElement('pre');
    debugDiv.style.fontSize = '12px';
    debugDiv.style.backgroundColor = '#f5f5f5';
    debugDiv.style.padding = '10px';
    debugDiv.style.overflow = 'auto';
    debugDiv.textContent = JSON.stringify(houseData, null, 2);

    cardDiv.appendChild(debugDiv);
    parentPre.parentNode.replaceChild(cardDiv, parentPre);
}

// 显示房源详情
function showHouseDetail(house) {
    // 简单地显示一个警告框，显示房源数据
    alert('房源详情：\n' + JSON.stringify(house, null, 2));
}

// 添加系统消息
function addSystemMessage(content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message system-message';

    messageDiv.innerHTML = `
        <div class="message-content">
            ${content}
        </div>
    `;

    chatMessages.appendChild(messageDiv);

    // 滚动到底部
    scrollToBottom();

    return messageDiv;
}

// 添加加载指示器
function addLoadingIndicator() {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'message assistant-message';

    loadingDiv.innerHTML = `
        <div class="message-content typing-indicator">
            <span></span>
            <span></span>
            <span></span>
        </div>
    `;

    chatMessages.appendChild(loadingDiv);

    // 滚动到底部
    scrollToBottom();

    return loadingDiv;
}

// 格式化消息内容
function formatMessageContent(content) {
    // 确保内容是字符串
    if (!content || typeof content !== 'string') {
        return '';
    }

    // 预处理内容，确保换行符被正确处理
    // 将连续的换行符替换为HTML段落标签
    let processedContent = content;

    // 使用marked.js解析Markdown
    if (typeof marked !== 'undefined') {
        // 配置marked选项，确保换行符被正确处理
        marked.setOptions({
            breaks: true,  // 将换行符转换为<br>
            gfm: true      // 使用GitHub风格的Markdown
        });

        // 使用DOMPurify清理HTML
        if (typeof DOMPurify !== 'undefined') {
            const parsed = marked.parse(processedContent);
            return DOMPurify.sanitize(parsed, {
                ADD_ATTR: ['target'],
                FORBID_TAGS: ['style', 'script'],
                FORBID_ATTR: ['style']
            });
        }
        return marked.parse(processedContent);
    }

    // 如果没有marked.js，简单处理换行
    return processedContent.replace(/\n/g, '<br>');
}

// 滚动到底部
function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 确认删除会话
function confirmDeleteSession(sessionId) {
    if (!sessionId) return;

    // 确认是否删除
    if (confirm('确定要删除这个会话吗？此操作不可撤销。')) {
        deleteSession(sessionId);
    }
}

// 删除会话
async function deleteSession(sessionId) {
    try {
        // 显示加载状态
        const sessionItem = document.querySelector(`.session-item[data-session-id="${sessionId}"]`);
        if (sessionItem) {
            sessionItem.classList.add('deleting');
            const deleteBtn = sessionItem.querySelector('.delete-session-btn');
            if (deleteBtn) {
                deleteBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
                deleteBtn.disabled = true;
            }
        }

        // 发送删除请求
        const response = await fetch(`/api/session/${sessionId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error('删除会话失败');
        }

        // 如果删除的是当前会话，创建新会话
        if (sessionId === currentSessionId) {
            await createNewSession();
        } else {
            // 从会话列表中移除
            sessions = sessions.filter(s => s.session_id !== sessionId);
            renderSessionList();
        }

    } catch (error) {
        console.error('删除会话失败:', error);
        alert('删除会话失败: ' + error.message);

        // 恢复按钮状态
        const sessionItem = document.querySelector(`.session-item[data-session-id="${sessionId}"]`);
        if (sessionItem) {
            sessionItem.classList.remove('deleting');
            const deleteBtn = sessionItem.querySelector('.delete-session-btn');
            if (deleteBtn) {
                deleteBtn.innerHTML = '<i class="bi bi-trash"></i>';
                deleteBtn.disabled = false;
            }
        }
    }
}

// 显示参数
function showParameters(context) {
    if (!context) return;

    // 过滤出有值的参数
    const validParams = {};
    for (const key in context) {
        if (context[key] && context[key] !== 'null' && context[key] !== 'any') {
            validParams[key] = context[key];
        }
    }

    // 如果没有有效参数，不显示面板
    if (Object.keys(validParams).length === 0) {
        parametersPanel.style.display = 'none';
        return;
    }

    // 清空参数内容
    parametersContent.innerHTML = '';

    // 添加参数
    for (const key in validParams) {
        const paramItem = document.createElement('div');
        paramItem.className = 'parameter-item';

        paramItem.innerHTML = `
            <div class="parameter-name">${formatParameterName(key)}</div>
            <div class="parameter-value">${validParams[key]}</div>
        `;

        parametersContent.appendChild(paramItem);
    }

    // 显示面板
    parametersPanel.style.display = 'block';
}

// 格式化参数名称
function formatParameterName(key) {
    const nameMap = {
        'location': '位置',
        'location_type': '位置类型',
        'room_type': '租住类型',
        'budget': '预算',
        'bed_room': '居室',
        'area': '面积',
        'face': '朝向',
        'leasetype': '租期',
        'distance': '距离',
        'transport': '通勤方式',
        'transport_minutes': '通勤时间',
        'checkin_date': '入住日期',
        'checkin_people_num': '入住人数',
        'properties_with_elevator': '有电梯'
    };

    return nameMap[key] || key;
}

// 将房源列表转换为Markdown格式
function formatListingsToMarkdown(listings) {
    if (!listings || !Array.isArray(listings) || listings.length === 0) {
        return "没有找到符合条件的房源。";
    }

    let markdown = "### 为您找到以下房源：\n\n";

    listings.forEach((listing, index) => {
        markdown += `#### ${index + 1}. ${listing.title || '未知房源'}\n\n`;
        markdown += `- **价格**：${listing.price || '价格待定'} 元/月\n`;
        markdown += `- **户型**：${listing.layout || listing.room_type || '未知户型'}\n`;
        markdown += `- **面积**：${listing.area || '未知面积'}\n`;
        markdown += `- **位置**：${listing.location || listing.address || '未知位置'}\n`;

        if (listing.tags && listing.tags.length > 0) {
            const tags = listing.tags.map(tag => typeof tag === 'object' ? tag.name : tag).join('、');
            markdown += `- **特色**：${tags}\n`;
        }

        if (listing.image_url) {
            markdown += `\n![房源图片](${listing.image_url})\n`;
        }

        markdown += '\n';

        // 添加房源详情的JSON数据（用于前端处理）
        markdown += '```json\n';
        markdown += JSON.stringify(listing, null, 2);
        markdown += '\n```\n\n';
    });

    return markdown;
}

// 显示调试信息
function showDebugInfo(data) {
    // 清空调试内容
    debugContent.innerHTML = '';

    // 添加调试信息
    debugContent.textContent = JSON.stringify(data, null, 2);

    // 显示面板
    debugPanel.style.display = 'block';
}

// 显示调试面板
function showDebugPanel(debugData) {
    // 清空调试内容
    debugContent.innerHTML = '';

    // 添加提取的参数
    if (debugData.extractedParams) {
        const paramsSection = document.createElement('div');
        paramsSection.className = 'debug-section';
        paramsSection.innerHTML = `
            <h6>提取的参数</h6>
            <pre>${JSON.stringify(debugData.extractedParams, null, 2)}</pre>
        `;
        debugContent.appendChild(paramsSection);
    }

    // 添加API返回结果
    if (debugData.apiResponse) {
        const apiSection = document.createElement('div');
        apiSection.className = 'debug-section';

        // 确保中文正确显示
        let apiResponseStr;
        try {
            // 如果是字符串，先解析为对象
            if (typeof debugData.apiResponse === 'string') {
                const apiResponseObj = JSON.parse(debugData.apiResponse);
                apiResponseStr = JSON.stringify(apiResponseObj, null, 2);
            } else {
                apiResponseStr = JSON.stringify(debugData.apiResponse, null, 2);
            }
        } catch (e) {
            console.error('解析API返回结果失败:', e);
            apiResponseStr = String(debugData.apiResponse);
        }

        apiSection.innerHTML = `
            <h6>API返回结果</h6>
            <pre>${apiResponseStr}</pre>
        `;
        debugContent.appendChild(apiSection);
    }

    // 显示调试面板
    debugPanel.style.display = 'block';
}

// 切换调试格式
function toggleDebugFormat() {
    const isFormatted = debugContent.style.whiteSpace === 'pre-wrap';

    if (isFormatted) {
        debugContent.style.whiteSpace = 'nowrap';
    } else {
        debugContent.style.whiteSpace = 'pre-wrap';
    }
}
