<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="700" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .user-box { fill: #e3f2fd; }
      .api-box { fill: #e1f5fe; }
      .service-box { fill: #e8f5e9; }
      .workflow-box { fill: #fff8e1; }
      .llm-box { fill: #fff3e0; }
      .repository-box { fill: #f3e5f5; }
      .db-box { fill: #ede7f6; }
      .external-box { fill: #ffebee; }
      .arrow {
        stroke: #333;
        stroke-width: 2;
        marker-end: url(#arrowhead);
      }
      .dashed-arrow {
        stroke: #333;
        stroke-width: 2;
        stroke-dasharray: 5,5;
        marker-end: url(#arrowhead);
      }
      .label {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
      }
      .arrow-label {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
        fill: #555;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 20px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="400" y="30" class="title">AI House Search System - Data Flow</text>
  <text x="400" y="50" class="subtitle">Processing steps from user request to response</text>

  <!-- User -->
  <rect x="350" y="70" width="100" height="40" class="box user-box" />
  <text x="400" y="95" class="label">User</text>

  <!-- API Layer -->
  <rect x="250" y="150" width="300" height="60" class="box api-box" />
  <text x="400" y="185" class="label">API Layer (FastAPI)</text>

  <!-- Chat Service -->
  <rect x="250" y="250" width="300" height="60" class="box service-box" />
  <text x="400" y="285" class="label">Chat Service</text>

  <!-- Workflow Factory -->
  <rect x="250" y="350" width="300" height="60" class="box workflow-box" />
  <text x="400" y="385" class="label">Workflow Factory</text>

  <!-- Workflow Steps -->
  <rect x="50" y="450" width="700" height="100" class="box workflow-box" />
  <text x="400" y="470" class="label">Workflow Steps</text>
  
  <rect x="70" y="490" width="120" height="40" class="box" />
  <text x="130" y="515" class="label">Intent Recognition</text>
  
  <rect x="210" y="490" width="120" height="40" class="box" />
  <text x="270" y="515" class="label">Parameter Extraction</text>
  
  <rect x="350" y="490" width="120" height="40" class="box" />
  <text x="410" y="515" class="label">House Search</text>
  
  <rect x="490" y="490" width="120" height="40" class="box" />
  <text x="550" y="515" class="label">Response Generation</text>
  
  <rect x="630" y="490" width="100" height="40" class="box" />
  <text x="680" y="515" class="label">Save Session</text>

  <!-- LLM Service -->
  <rect x="50" y="590" width="200" height="60" class="box llm-box" />
  <text x="150" y="625" class="label">LLM Service (OpenAI)</text>

  <!-- Repository Layer -->
  <rect x="300" y="590" width="200" height="60" class="box repository-box" />
  <text x="400" y="625" class="label">Repository Layer</text>

  <!-- External Services -->
  <rect x="550" y="590" width="200" height="60" class="box external-box" />
  <text x="650" y="625" class="label">External House API</text>

  <!-- Arrows -->
  <!-- User to API -->
  <line x1="400" y1="110" x2="400" y2="150" class="arrow" />
  <text x="420" y="135" class="arrow-label">User Message</text>

  <!-- API to Service -->
  <line x1="400" y1="210" x2="400" y2="250" class="arrow" />
  <text x="420" y="235" class="arrow-label">Request</text>

  <!-- Service to Workflow -->
  <line x1="400" y1="310" x2="400" y2="350" class="arrow" />
  <text x="420" y="335" class="arrow-label">Context</text>

  <!-- Workflow to Steps -->
  <line x1="400" y1="410" x2="400" y2="450" class="arrow" />
  <text x="420" y="435" class="arrow-label">Execute Steps</text>

  <!-- Steps to Services -->
  <line x1="130" y1="530" x2="130" y2="590" class="arrow" />
  <text x="150" y="565" class="arrow-label">Prompt</text>
  
  <line x1="270" y1="530" x2="200" y2="590" class="arrow" />
  <text x="250" y="565" class="arrow-label">Extract</text>
  
  <line x1="410" y1="530" x2="400" y2="590" class="arrow" />
  <text x="430" y="565" class="arrow-label">Query</text>
  
  <line x1="550" y1="530" x2="600" y2="590" class="arrow" />
  <text x="590" y="565" class="arrow-label">Search</text>
  
  <line x1="680" y1="530" x2="450" y2="590" class="dashed-arrow" />
  <text x="580" y="565" class="arrow-label">Save</text>

  <!-- Return path -->
  <path d="M 650 590 C 700 500 700 200 450 150" class="dashed-arrow" fill="none" />
  <text x="700" y="350" class="arrow-label">Response</text>
  
  <path d="M 400 150 C 300 120 300 80 350 80" class="dashed-arrow" fill="none" />
  <text x="320" y="100" class="arrow-label">Result</text>
</svg>
