<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI找房系统架构图</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .diagram-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .diagram {
            width: 100%;
            height: auto;
            margin: 20px 0;
        }
        .description {
            margin-bottom: 20px;
            padding: 0 20px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
            background-color: #f1f1f1;
        }
        .tab.active {
            background-color: white;
            border-color: #ddd;
            border-bottom-color: white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>AI找房系统架构图</h1>
    
    <div class="tabs">
        <div class="tab active" data-tab="architecture">架构层次图</div>
        <div class="tab" data-tab="dataflow">数据流图</div>
        <div class="tab" data-tab="components">组件关系图</div>
    </div>
    
    <div id="architecture" class="tab-content active">
        <div class="diagram-container">
            <h2>系统架构层次图</h2>
            <div class="description">
                <p>该图展示了AI找房系统的整体架构，从API层到数据库层的各个组件及其关系。系统采用清晰的分层设计，每一层都有明确的职责。</p>
            </div>
            <img src="architecture_flow.svg" alt="系统架构层次图" class="diagram">
        </div>
    </div>
    
    <div id="dataflow" class="tab-content">
        <div class="diagram-container">
            <h2>数据流图</h2>
            <div class="description">
                <p>该图展示了用户请求从输入到输出的完整处理流程，包括意图识别、参数提取、房源搜索和响应生成等关键步骤。</p>
            </div>
            <img src="data_flow.svg" alt="数据流图" class="diagram">
        </div>
    </div>
    
    <div id="components" class="tab-content">
        <div class="diagram-container">
            <h2>组件关系图</h2>
            <div class="description">
                <p>该图详细展示了系统各个组件之间的依赖关系，包括服务层、工作流层、LLM层和仓库层的主要组件及其交互方式。</p>
            </div>
            <img src="component_relationships.svg" alt="组件关系图" class="diagram">
        </div>
    </div>

    <script>
        // 简单的标签页切换功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有活动标签和内容
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 激活当前标签和内容
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
    </script>
</body>
</html>
