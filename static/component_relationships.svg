<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Styles -->
  <defs>
    <style>
      .box {
        fill: #f5f5f5;
        stroke: #333;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .api-box { fill: #e1f5fe; }
      .service-box { fill: #e8f5e9; }
      .workflow-box { fill: #fff8e1; }
      .llm-box { fill: #fff3e0; }
      .repository-box { fill: #f3e5f5; }
      .db-box { fill: #ede7f6; }
      .external-box { fill: #ffebee; }
      .arrow {
        stroke: #333;
        stroke-width: 2;
        marker-end: url(#arrowhead);
      }
      .label {
        font-family: Arial, sans-serif;
        font-size: 14px;
        text-anchor: middle;
      }
      .component-label {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
        font-weight: bold;
      }
      .title {
        font-family: Arial, sans-serif;
        font-size: 20px;
        font-weight: bold;
        text-anchor: middle;
      }
      .subtitle {
        font-family: Arial, sans-serif;
        font-size: 12px;
        text-anchor: middle;
      }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="400" y="30" class="title">AI House Search System - Component Relationships</text>
  <text x="400" y="50" class="subtitle">Key components and their dependencies</text>

  <!-- API Layer -->
  <rect x="300" y="80" width="200" height="60" class="box api-box" />
  <text x="400" y="115" class="component-label">API Controllers</text>

  <!-- Service Layer -->
  <rect x="100" y="180" width="160" height="60" class="box service-box" />
  <text x="180" y="215" class="component-label">ChatService</text>
  
  <rect x="320" y="180" width="160" height="60" class="box service-box" />
  <text x="400" y="215" class="component-label">SessionService</text>
  
  <rect x="540" y="180" width="160" height="60" class="box service-box" />
  <text x="620" y="215" class="component-label">HouseService</text>

  <!-- Workflow Layer -->
  <rect x="100" y="280" width="160" height="60" class="box workflow-box" />
  <text x="180" y="315" class="component-label">WorkflowFactory</text>
  
  <rect x="320" y="280" width="160" height="60" class="box workflow-box" />
  <text x="400" y="315" class="component-label">ChatWorkflow</text>
  
  <rect x="540" y="280" width="160" height="60" class="box workflow-box" />
  <text x="620" y="315" class="component-label">WorkflowSteps</text>

  <!-- LLM Layer -->
  <rect x="100" y="380" width="160" height="60" class="box llm-box" />
  <text x="180" y="415" class="component-label">IntentRecognizer</text>
  
  <rect x="320" y="380" width="160" height="60" class="box llm-box" />
  <text x="400" y="415" class="component-label">ParameterExtractor</text>
  
  <rect x="540" y="380" width="160" height="60" class="box llm-box" />
  <text x="620" y="415" class="component-label">ResponseGenerator</text>

  <!-- Repository Layer -->
  <rect x="100" y="480" width="160" height="60" class="box repository-box" />
  <text x="180" y="515" class="component-label">SessionRepository</text>
  
  <rect x="320" y="480" width="160" height="60" class="box repository-box" />
  <text x="400" y="515" class="component-label">MessageRepository</text>
  
  <rect x="540" y="480" width="160" height="60" class="box repository-box" />
  <text x="620" y="515" class="component-label">HouseSearcher</text>

  <!-- Arrows -->
  <!-- API to Services -->
  <line x1="350" y1="140" x2="180" y2="180" class="arrow" />
  <line x1="400" y1="140" x2="400" y2="180" class="arrow" />
  <line x1="450" y1="140" x2="620" y2="180" class="arrow" />

  <!-- Services to Workflows -->
  <line x1="180" y1="240" x2="180" y2="280" class="arrow" />
  <line x1="400" y1="240" x2="400" y2="280" class="arrow" />
  <line x1="620" y1="240" x2="620" y2="280" class="arrow" />

  <!-- Workflow connections -->
  <line x1="180" y1="340" x2="320" y2="310" class="arrow" />
  <line x1="480" y1="310" x2="540" y2="310" class="arrow" />

  <!-- Workflows to LLM -->
  <line x1="180" y1="340" x2="180" y2="380" class="arrow" />
  <line x1="400" y1="340" x2="400" y2="380" class="arrow" />
  <line x1="620" y1="340" x2="620" y2="380" class="arrow" />

  <!-- LLM to Repository -->
  <line x1="180" y1="440" x2="180" y2="480" class="arrow" />
  <line x1="400" y1="440" x2="400" y2="480" class="arrow" />
  <line x1="620" y1="440" x2="620" y2="480" class="arrow" />

  <!-- Cross connections -->
  <line x1="260" y1="210" x2="320" y2="210" class="arrow" />
  <line x1="480" y1="210" x2="540" y2="210" class="arrow" />
  <line x1="260" y1="310" x2="320" y2="310" class="arrow" />
  <line x1="260" y1="410" x2="320" y2="410" class="arrow" />
  <line x1="480" y1="410" x2="540" y2="410" class="arrow" />
  <line x1="260" y1="510" x2="320" y2="510" class="arrow" />
  <line x1="480" y1="510" x2="540" y2="510" class="arrow" />
</svg>
